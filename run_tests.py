#!/usr/bin/env python3
"""
测试运行器
运行所有单元测试和集成测试
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path


def run_unit_tests(verbose=False, coverage=False):
    """运行单元测试"""
    print("🧪 运行单元测试...")
    
    cmd = ["pytest", "tests/unit", "-v" if verbose else "-q"]
    
    if coverage:
        cmd.extend(["--cov=elliott_wave", "--cov=llm_client", "--cov=rag_system"])
    
    result = subprocess.run(cmd, capture_output=False)
    return result.returncode == 0


def run_integration_tests(verbose=False):
    """运行集成测试"""
    print("🔗 运行集成测试...")
    
    cmd = ["pytest", "tests/integration", "-v" if verbose else "-q"]
    result = subprocess.run(cmd, capture_output=False)
    return result.returncode == 0


def run_all_tests(verbose=False, coverage=False):
    """运行所有测试"""
    print("🚀 运行所有测试...")
    
    cmd = ["pytest", "tests", "-v" if verbose else "-q"]
    
    if coverage:
        cmd.extend(["--cov=.", "--cov-report=html", "--cov-report=term"])
    
    result = subprocess.run(cmd, capture_output=False)
    return result.returncode == 0


def install_test_dependencies():
    """安装测试依赖"""
    print("📦 安装测试依赖...")
    
    requirements_file = Path("tests/test_requirements.txt")
    if requirements_file.exists():
        cmd = ["pip", "install", "-r", str(requirements_file)]
        result = subprocess.run(cmd, capture_output=False)
        return result.returncode == 0
    else:
        print("⚠️  测试依赖文件不存在")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="运行Elliott Wave分析器测试")
    parser.add_argument("--unit", action="store_true", help="仅运行单元测试")
    parser.add_argument("--integration", action="store_true", help="仅运行集成测试")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    parser.add_argument("--coverage", "-c", action="store_true", help="生成覆盖率报告")
    parser.add_argument("--install-deps", action="store_true", help="安装测试依赖")
    
    args = parser.parse_args()
    
    # 安装依赖
    if args.install_deps:
        if not install_test_dependencies():
            sys.exit(1)
    
    # 设置环境变量
    os.environ["PYTHONPATH"] = str(Path.cwd())
    
    success = True
    
    try:
        if args.unit:
            success = run_unit_tests(args.verbose, args.coverage)
        elif args.integration:
            success = run_integration_tests(args.verbose)
        else:
            success = run_all_tests(args.verbose, args.coverage)
    except KeyboardInterrupt:
        print("\n❌ 测试被中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 测试运行错误: {e}")
        sys.exit(1)
    
    if success:
        print("✅ 所有测试通过!")
    else:
        print("❌ 部分测试失败")
        sys.exit(1)


if __name__ == "__main__":
    main()