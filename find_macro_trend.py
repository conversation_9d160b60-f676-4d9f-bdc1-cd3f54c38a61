import pandas as pd
import os
import sys

# Add project root to sys.path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def find_macro_trend(data_path: str):
    """Finds the largest appreciation period in the stock data."""
    print(f"--- Analyzing Macro Trend in {data_path} ---")

    # 1. Load data
    try:
        df = pd.read_parquet(data_path)
        # Ensure index is datetime
        df.index = pd.to_datetime(df.index)
        print(f"Loaded {len(df)} total data points.")
    except Exception as e:
        print(f"Error loading data: {e}")
        return

    # 2. Find the absolute low and high
    # We use 'low' for the lowest point and 'high' for the highest to get the max possible range
    abs_low_date = df['low'].idxmin()
    abs_low_price = df['low'].min()

    abs_high_date = df['high'].idxmax()
    abs_high_price = df['high'].max()

    print(f"\n--- Macro Trend Identified ---")
    print(f"Absolute Low: {abs_low_price:.2f} on {abs_low_date.strftime('%Y-%m-%d')}")
    print(f"Absolute High: {abs_high_price:.2f} on {abs_high_date.strftime('%Y-%m-%d')}")

    # 3. Calculate multiplication factor
    if abs_low_price > 0:
        multiplication_factor = abs_high_price / abs_low_price
        print(f"Multiplication Factor: {multiplication_factor:.2f}x")
        if multiplication_factor > 25:
            print("CONFIRMED: A growth period exceeding 25x was found.")
        else:
            print("NOTE: The max growth period found is less than 25x.")
    else:
        print("Cannot calculate multiplication factor because the lowest price is zero.")

    print("\nThis period will be the focus of our next detailed wave structure analysis.")

if __name__ == '__main__':
    data_file = os.path.join(os.path.dirname(__file__), 'stock_data', '000831_long_term.parquet')
    find_macro_trend(data_file)
