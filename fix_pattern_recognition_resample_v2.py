import pandas as pd

file_path = "/Users/<USER>/Projects/stock/ai_elliott/elliott_wave/pattern_recognition.py"

with open(file_path, 'r') as f:
    lines = f.readlines()

for i, line in enumerate(lines):
    if "def find_elliott_wave_patterns(df: pd.DataFrame" in line:
        # Insert the resampling line after the function definition and docstring
        lines.insert(i + 5, "    df = df.resample('D').ffill() # Ensure continuous daily data\n")
        lines.insert(i + 6, "    logging.info(f\"[find_elliott_wave_patterns] DataFrame after daily resampling and ffill: {len(df)}.\")\n")
        break

with open(file_path, 'w') as f:
    f.writelines(lines)

print("Successfully modified find_elliott_wave_patterns in elliott_wave/pattern_recognition.py")
