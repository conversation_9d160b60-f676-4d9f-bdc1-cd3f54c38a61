"""
基础LLM客户端抽象类
定义统一的LLM接口规范
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Dict, Any, Optional, List
import time
import logging

logger = logging.getLogger(__name__)

@dataclass
class LLMResponse:
    """LLM响应数据结构"""
    content: str
    model: str
    usage: Dict[str, int]  # prompt_tokens, completion_tokens, total_tokens
    latency: float
    metadata: Optional[Dict[str, Any]] = None
    
    @property
    def cost(self) -> float:
        """估算成本（基于token使用量）"""
        # 简化的成本计算，实际应根据不同模型定价
        return (self.usage.get('prompt_tokens', 0) * 0.001 + 
                self.usage.get('completion_tokens', 0) * 0.003) / 1000

class BaseLLMClient(ABC):
    """LLM客户端抽象基类"""
    
    def __init__(self, api_key: str, model_name: str, **kwargs):
        self.api_key = api_key
        self.model_name = model_name
        self.base_url = kwargs.get('base_url', '')
        self.timeout = kwargs.get('timeout', 30)
        self.max_retries = kwargs.get('max_retries', 3)
        self.retry_delay = kwargs.get('retry_delay', 1)
        
    @abstractmethod
    async def generate(self, 
                      prompt: str, 
                      system_prompt: Optional[str] = None,
                      temperature: float = 0.7,
                      max_tokens: Optional[int] = None,
                      **kwargs) -> LLMResponse:
        """生成文本响应"""
        pass
    
    @abstractmethod
    async def batch_generate(self,
                           prompts: List[str],
                           system_prompt: Optional[str] = None,
                           **kwargs) -> List[LLMResponse]:
        """批量生成文本响应"""
        pass
    
    def _validate_config(self) -> bool:
        """验证配置有效性"""
        if not self.api_key:
            raise ValueError("API key is required")
        if not self.model_name:
            raise ValueError("Model name is required")
        return True
    
    def _calculate_latency(self, start_time: float) -> float:
        """计算延迟时间"""
        return time.time() - start_time
    
    def _log_usage(self, response: LLMResponse):
        """记录使用情况"""
        logger.info(f"Model: {response.model}, "
                   f"Tokens: {response.usage}, "
                   f"Cost: ${response.cost:.4f}, "
                   f"Latency: {response.latency:.2f}s")
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            response = await self.generate("Hello", max_tokens=10)
            return response.content is not None
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False