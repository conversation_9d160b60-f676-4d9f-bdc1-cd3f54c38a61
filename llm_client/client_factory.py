"""
LLM客户端工厂
提供统一的客户端创建接口
"""

import os
from typing import Dict, Any, Optional
from .base_client import BaseLLMClient
from .openrouter_client import OpenRouterClient
from .gemini_client import GeminiClient
from .cache_manager import CacheManager
from .config_validator import ConfigValidator

class LLMClientFactory:
    """LLM客户端工厂类"""
    
    def __init__(self):
        self.cache_manager = CacheManager()
    
    def create_client(self, 
                     provider: str = None,
                     model: str = None,
                     api_key: str = None,
                     **kwargs) -> BaseLLMClient:
        """创建LLM客户端"""
        
        # 使用环境变量或默认配置
        if provider is None:
            provider = os.getenv("DEFAULT_PROVIDER", "openrouter")
        
        if api_key is None:
            api_key = self._get_api_key_from_env(provider)
        
        if model is None:
            model = self._get_default_model(provider)
        
        # 验证配置
        config = {
            "provider": provider,
            "api_key": api_key,
            "model": model,
            **kwargs
        }
        
        validation = ConfigValidator.validate_config(config)
        if not validation["valid"]:
            raise ValueError(f"Invalid configuration: {validation['errors']}")
        
        # 创建客户端
        normalized_config = validation["config"]
        
        if provider == "openrouter":
            return OpenRouterClient(
                api_key=normalized_config["api_key"],
                model_name=normalized_config["model"],
                timeout=normalized_config["timeout"],
                max_retries=normalized_config["max_retries"],
                retry_delay=normalized_config["retry_delay"]
            )
        elif provider == "gemini":
            return GeminiClient(
                api_key=normalized_config["api_key"],
                model_name=normalized_config["model"],
                timeout=normalized_config["timeout"],
                max_retries=normalized_config["max_retries"],
                retry_delay=normalized_config["retry_delay"]
            )
        else:
            raise ValueError(f"Unsupported provider: {provider}")
    
    def _get_api_key_from_env(self, provider: str) -> str:
        """从环境变量获取API密钥"""
        env_map = {
            "openrouter": "OPENROUTER_API_KEY",
            "openai": "OPENAI_API_KEY",
            "gemini": "GEMINI_API_KEY"
        }
        
        env_var = env_map.get(provider)
        if not env_var:
            raise ValueError(f"Unknown provider: {provider}")
        
        api_key = os.getenv(env_var)
        if not api_key:
            raise ValueError(
                f"API key not found for {provider}. "
                f"Please set {env_var} environment variable or provide api_key parameter"
            )
        
        return api_key
    
    def _get_default_model(self, provider: str) -> str:
        """获取默认模型"""
        defaults = {
            "openrouter": "deepseek/deepseek-r1-0528:free",
            "openai": "gpt-4o-mini",
            "gemini": "gemini-1.5-flash"
        }
        return defaults.get(provider, "deepseek/deepseek-r1-0528:free")
    
    def get_available_providers(self) -> list:
        """获取可用提供商列表"""
        return ["openrouter", "gemini"]
    
    def get_recommended_models(self, provider: str, use_case: str = "analysis") -> list:
        """获取推荐模型列表"""
        return ConfigValidator.get_recommended_models(provider, use_case)
    
    def check_environment_setup(self) -> Dict[str, Any]:
        """检查环境配置"""
        return ConfigValidator.check_environment()
    
    def create_with_cache(self, **kwargs) -> tuple:
        """创建带缓存的客户端"""
        client = self.create_client(**kwargs)
        return client, self.cache_manager

# 全局工厂实例
factory = LLMClientFactory()

# 便捷函数
def create_llm_client(**kwargs) -> BaseLLMClient:
    """创建LLM客户端的便捷函数"""
    return factory.create_client(**kwargs)

def create_llm_client_with_cache(**kwargs) -> tuple:
    """创建带缓存的LLM客户端"""
    return factory.create_with_cache(**kwargs)

# 配置检查函数
def check_llm_setup() -> Dict[str, Any]:
    """检查LLM环境配置"""
    return {
        "environment": factory.check_environment_setup(),
        "available_providers": factory.get_available_providers(),
        "recommendations": {
            "analysis": factory.get_recommended_models("openrouter", "analysis"),
            "complex_analysis": factory.get_recommended_models("openrouter", "complex_analysis")
        }
    }

if __name__ == "__main__":
    # 测试配置
    result = check_llm_setup()
    print("LLM环境检查结果:")
    print(f"可用提供商: {result['available_providers']}")
    print(f"环境变量状态: {result['environment']}")
    print(f"推荐模型: {result['recommendations']}")