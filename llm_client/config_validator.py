"""
配置验证器
验证LLM配置的有效性和安全性
"""

import os
import re
from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)

class ConfigValidator:
    """LLM配置验证器"""
    
    # 支持的模型列表
    SUPPORTED_MODELS = {
        "openrouter": [
            "deepseek/deepseek-r1-0528:free",
            "deepseek/deepseek-chat",
            "openai/gpt-4o-mini",
            "openai/gpt-4o",
            "anthropic/claude-3-haiku",
            "anthropic/claude-3-sonnet",
            "google/gemini-flash-1.5",
            "google/gemini-pro-1.5"
        ],
        "openai": [
            "gpt-4o-mini",
            "gpt-4o",
            "gpt-4-turbo",
            "gpt-3.5-turbo"
        ],
        "gemini": [
            "gemini-1.5-flash",
            "gemini-1.5-pro",
            "gemini-pro"
        ]
    }
    
    # API密钥环境变量映射
    API_KEY_ENV_MAP = {
        "openrouter": "OPENROUTER_API_KEY",
        "openai": "OPENAI_API_KEY",
        "gemini": "GEMINI_API_KEY"
    }
    
    @classmethod
    def validate_config(cls, config: Dict[str, Any]) -> Dict[str, Any]:
        """验证配置"""
        errors = []
        warnings = []
        
        # 验证提供商
        provider = config.get("provider", "openrouter")
        if provider not in cls.SUPPORTED_MODELS:
            errors.append(f"Unsupported provider: {provider}")
        
        # 验证模型
        model = config.get("model")
        if model and model not in cls.SUPPORTED_MODELS.get(provider, []):
            warnings.append(f"Model {model} may not be supported by {provider}")
        
        # 验证API密钥
        api_key = cls._get_api_key(provider, config)
        if not api_key:
            errors.append(f"API key not found for provider {provider}")
        elif not cls._validate_api_key_format(provider, api_key):
            errors.append(f"Invalid API key format for {provider}")
        
        # 验证参数
        temperature = config.get("temperature", 0.7)
        if not 0 <= temperature <= 2:
            errors.append("Temperature must be between 0 and 2")
        
        max_tokens = config.get("max_tokens")
        if max_tokens and not isinstance(max_tokens, int) or (max_tokens and max_tokens <= 0):
            errors.append("max_tokens must be a positive integer")
        
        # 验证超时设置
        timeout = config.get("timeout", 30)
        if not isinstance(timeout, int) or timeout <= 0:
            errors.append("timeout must be a positive integer")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings,
            "config": cls._normalize_config(config, provider, api_key)
        }
    
    @classmethod
    def _get_api_key(cls, provider: str, config: Dict[str, Any]) -> Optional[str]:
        """获取API密钥"""
        # 1. 从配置中获取
        api_key = config.get("api_key")
        if api_key:
            return api_key
        
        # 2. 从环境变量获取
        env_var = cls.API_KEY_ENV_MAP.get(provider)
        if env_var:
            return os.getenv(env_var)
        
        return None
    
    @classmethod
    def _validate_api_key_format(cls, provider: str, api_key: str) -> bool:
        """验证API密钥格式"""
        if provider == "openrouter":
            # OpenRouter密钥通常以"sk-or-"开头
            return api_key.startswith("sk-or-") and len(api_key) > 20
        elif provider == "openai":
            # OpenAI密钥通常以"sk-"开头
            return api_key.startswith("sk-") and len(api_key) > 20
        elif provider == "gemini":
            # Gemini密钥格式较灵活
            return len(api_key) > 20
        return True
    
    @classmethod
    def _normalize_config(cls, config: Dict[str, Any], provider: str, api_key: str) -> Dict[str, Any]:
        """标准化配置"""
        normalized = {
            "provider": provider,
            "api_key": api_key,
            "model": config.get("model", cls.SUPPORTED_MODELS[provider][0]),
            "temperature": config.get("temperature", 0.7),
            "max_tokens": config.get("max_tokens", 2000),
            "timeout": config.get("timeout", 30),
            "max_retries": config.get("max_retries", 3),
            "retry_delay": config.get("retry_delay", 1),
            "base_url": config.get("base_url", cls._get_default_base_url(provider))
        }
        
        # 添加提供商特定配置
        if provider == "openrouter":
            normalized["headers"] = {
                "HTTP-Referer": config.get("http_referer", "https://localhost:8000"),
                "X-Title": config.get("x_title", "ElliottAgents")
            }
        
        return normalized
    
    @classmethod
    def _get_default_base_url(cls, provider: str) -> str:
        """获取默认基础URL"""
        urls = {
            "openrouter": "https://openrouter.ai/api/v1",
            "openai": "https://api.openai.com/v1",
            "gemini": "https://generativelanguage.googleapis.com/v1beta"
        }
        return urls.get(provider, "")
    
    @classmethod
    def get_recommended_models(cls, provider: str, use_case: str = "analysis") -> List[str]:
        """获取推荐模型"""
        recommendations = {
            "analysis": {
                "openrouter": [
                    "deepseek/deepseek-r1-0528:free",
                    "deepseek/deepseek-chat",
                    "openai/gpt-4o-mini"
                ],
                "openai": [
                    "gpt-4o-mini",
                    "gpt-4o"
                ],
                "gemini": [
                    "gemini-1.5-flash",
                    "gemini-1.5-pro"
                ]
            },
            "complex_analysis": {
                "openrouter": [
                    "openai/gpt-4o",
                    "anthropic/claude-3-sonnet"
                ],
                "openai": [
                    "gpt-4o",
                    "gpt-4-turbo"
                ],
                "gemini": [
                    "gemini-1.5-pro"
                ]
            }
        }
        
        return recommendations.get(use_case, {}).get(provider, [])
    
    @classmethod
    def check_environment(cls) -> Dict[str, Any]:
        """检查环境配置"""
        env_status = {
            "env_vars": {},
            "available_providers": [],
            "missing_keys": []
        }
        
        for provider, env_var in cls.API_KEY_ENV_MAP.items():
            key_value = os.getenv(env_var)
            env_status["env_vars"][provider] = {
                "variable": env_var,
                "set": bool(key_value),
                "valid": bool(key_value) and cls._validate_api_key_format(provider, key_value)
            }
            
            if key_value and cls._validate_api_key_format(provider, key_value):
                env_status["available_providers"].append(provider)
            else:
                env_status["missing_keys"].append(provider)
        
        return env_status
    
    @classmethod
    def generate_env_template(cls) -> str:
        """生成环境变量模板"""
        template = """# ElliottAgents LLM配置
# 请根据使用的提供商配置相应的API密钥

# OpenRouter (推荐，支持多种模型)
OPENROUTER_API_KEY=sk-or-your-openrouter-key-here

# OpenAI (可选)
OPENAI_API_KEY=sk-your-openai-key-here

# Google Gemini (可选)
GEMINI_API_KEY=your-gemini-key-here

# 默认配置
DEFAULT_PROVIDER=openrouter
DEFAULT_MODEL=deepseek/deepseek-r1-0528:free
"""
        return template
    
    @classmethod
    def validate_prompt_length(cls, prompt: str, max_tokens: int = 4000) -> Dict[str, Any]:
        """验证提示词长度"""
        # 粗略估算token数量（中文约1.8字符/token，英文约4字符/token）
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', prompt))
        english_chars = len(re.findall(r'[a-zA-Z]', prompt))
        
        estimated_tokens = (chinese_chars / 1.8) + (english_chars / 4)
        
        return {
            "estimated_tokens": int(estimated_tokens),
            "within_limit": estimated_tokens <= max_tokens,
            "suggestion": "Consider splitting long prompts" if estimated_tokens > max_tokens else None
        }