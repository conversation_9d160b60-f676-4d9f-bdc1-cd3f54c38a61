"""
Google Gemini LLM客户端实现
"""

import aiohttp
import json
from typing import Dict, Any, Optional, List
from .base_client import BaseLLMClient, LLMResponse

class GeminiClient(BaseLLMClient):
    """Google Gemini API客户端"""
    
    def __init__(self, api_key: str, model_name: str = "gemini-1.5-flash", **kwargs):
        super().__init__(api_key, model_name, **kwargs)
        self.base_url = "https://generativelanguage.googleapis.com/v1beta"
        self.headers = {
            "Content-Type": "application/json",
        }
    
    async def generate(self, 
                      prompt: str, 
                      system_prompt: Optional[str] = None,
                      temperature: float = 0.7,
                      max_tokens: Optional[int] = None,
                      **kwargs) -> LLMResponse:
        """使用Gemini API生成文本"""
        import time
        start_time = time.time()
        
        # 构建请求数据
        contents = []
        if system_prompt:
            # Gemini将系统提示作为用户消息的一部分
            full_prompt = f"{system_prompt}\n\n{prompt}"
        else:
            full_prompt = prompt
            
        contents.append({
            "parts": [{"text": full_prompt}]
        })
        
        payload = {
            "contents": contents,
            "generationConfig": {
                "temperature": temperature,
                **({"maxOutputTokens": max_tokens} if max_tokens else {})
            }
        }
        
        # 添加额外参数
        if "top_p" in kwargs:
            payload["generationConfig"]["topP"] = kwargs["top_p"]
        if "top_k" in kwargs:
            payload["generationConfig"]["topK"] = kwargs["top_k"]
        
        url = f"{self.base_url}/models/{self.model_name}:generateContent?key={self.api_key}"
        
        async with aiohttp.ClientSession() as session:
            for attempt in range(self.max_retries):
                try:
                    async with session.post(
                        url,
                        headers=self.headers,
                        json=payload,
                        timeout=aiohttp.ClientTimeout(total=self.timeout)
                    ) as response:
                        if response.status == 200:
                            data = await response.json()
                            
                            # 提取生成的文本
                            candidates = data.get("candidates", [])
                            if candidates and "content" in candidates[0]:
                                parts = candidates[0]["content"].get("parts", [])
                                if parts:
                                    content = parts[0].get("text", "")
                                else:
                                    content = ""
                            else:
                                content = ""
                            
                            # 提取使用统计
                            usage_metadata = data.get("usageMetadata", {})
                            prompt_tokens = usage_metadata.get("promptTokenCount", 0)
                            completion_tokens = usage_metadata.get("candidatesTokenCount", 0)
                            total_tokens = usage_metadata.get("totalTokenCount", 0)
                            
                            return LLMResponse(
                                content=content,
                                model=self.model_name,
                                usage={
                                    "prompt_tokens": prompt_tokens,
                                    "completion_tokens": completion_tokens,
                                    "total_tokens": total_tokens
                                },
                                latency=self._calculate_latency(start_time),
                                metadata={
                                    "finish_reason": candidates[0].get("finishReason") if candidates else None
                                }
                            )
                        elif response.status == 429:
                            # 速率限制
                            if attempt < self.max_retries - 1:
                                import asyncio
                                await asyncio.sleep(self.retry_delay * (2 ** attempt))
                                continue
                            else:
                                raise Exception("Rate limit exceeded after retries")
                        else:
                            error_text = await response.text()
                            raise Exception(f"API error {response.status}: {error_text}")
                            
                except Exception as e:
                    if attempt < self.max_retries - 1:
                        import asyncio
                        await asyncio.sleep(self.retry_delay * (2 ** attempt))
                        continue
                    raise e
    
    async def batch_generate(self,
                           prompts: List[str],
                           system_prompt: Optional[str] = None,
                           **kwargs) -> List[LLMResponse]:
        """批量生成文本响应"""
        import asyncio
        tasks = [self.generate(prompt, system_prompt, **kwargs) for prompt in prompts]
        return await asyncio.gather(*tasks)
    
    async def count_tokens(self, text: str) -> int:
        """计算文本token数量（估算）"""
        # Gemini API的token计数接口
        url = f"{self.base_url}/models/{self.model_name}:countTokens?key={self.api_key}"
        
        payload = {
            "contents": [{
                "parts": [{"text": text}]
            }]
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                url,
                headers=self.headers,
                json=payload
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get("totalTokens", 0)
                else:
                    raise Exception(f"Failed to count tokens: {response.status}")
    
    def estimate_cost(self, prompt_tokens: int, completion_tokens: int) -> float:
        """估算调用成本（基于Gemini定价）"""
        # Gemini定价（每1K tokens）
        pricing = {
            "gemini-1.5-flash": {"input": 0.000075, "output": 0.0003},
            "gemini-1.5-pro": {"input": 0.00125, "output": 0.005}
        }
        
        model_pricing = pricing.get(self.model_name, {"input": 0.000075, "output": 0.0003})
        return (prompt_tokens * model_pricing["input"] + 
                completion_tokens * model_pricing["output"]) / 1000