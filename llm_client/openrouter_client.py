"""
OpenRouter LLM客户端实现
支持DeepSeek、<PERSON>、GPT等多种模型
"""

import aiohttp
import json
from typing import Dict, Any, Optional, List
from .base_client import BaseLLMClient, LLMResponse

class OpenRouterClient(BaseLLMClient):
    """OpenRouter API客户端"""
    
    def __init__(self, api_key: str, model_name: str = "deepseek/deepseek-r1-0528:free", **kwargs):
        super().__init__(api_key, model_name, **kwargs)
        self.base_url = "https://openrouter.ai/api/v1"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://github.com/your-repo",  # 替换为实际URL
            "X-Title": "ElliottAgents"
        }
    
    async def generate(self, 
                      prompt: str, 
                      system_prompt: Optional[str] = None,
                      temperature: float = 0.7,
                      max_tokens: Optional[int] = None,
                      **kwargs) -> LLMResponse:
        """使用OpenRouter API生成文本"""
        import time
        start_time = time.time()
        
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        
        payload = {
            "model": self.model_name,
            "messages": messages,
            "temperature": temperature,
            **({"max_tokens": max_tokens} if max_tokens else {})
        }
        
        # 添加额外参数
        if "top_p" in kwargs:
            payload["top_p"] = kwargs["top_p"]
        if "frequency_penalty" in kwargs:
            payload["frequency_penalty"] = kwargs["frequency_penalty"]
        if "presence_penalty" in kwargs:
            payload["presence_penalty"] = kwargs["presence_penalty"]
        
        async with aiohttp.ClientSession() as session:
            for attempt in range(self.max_retries):
                try:
                    async with session.post(
                        f"{self.base_url}/chat/completions",
                        headers=self.headers,
                        json=payload,
                        timeout=aiohttp.ClientTimeout(total=self.timeout)
                    ) as response:
                        if response.status == 200:
                            data = await response.json()
                            
                            content = data["choices"][0]["message"]["content"]
                            usage = data.get("usage", {})
                            model = data.get("model", self.model_name)
                            
                            return LLMResponse(
                                content=content,
                                model=model,
                                usage={
                                    "prompt_tokens": usage.get("prompt_tokens", 0),
                                    "completion_tokens": usage.get("completion_tokens", 0),
                                    "total_tokens": usage.get("total_tokens", 0)
                                },
                                latency=self._calculate_latency(start_time),
                                metadata={
                                    "finish_reason": data["choices"][0].get("finish_reason"),
                                    "response_id": data.get("id")
                                }
                            )
                        elif response.status == 429:
                            # 速率限制，等待后重试
                            if attempt < self.max_retries - 1:
                                await asyncio.sleep(self.retry_delay * (2 ** attempt))
                                continue
                            else:
                                raise Exception("Rate limit exceeded after retries")
                        else:
                            error_text = await response.text()
                            raise Exception(f"API error {response.status}: {error_text}")
                            
                except asyncio.TimeoutError:
                    if attempt < self.max_retries - 1:
                        await asyncio.sleep(self.retry_delay * (2 ** attempt))
                        continue
                    raise Exception("Request timeout after retries")
                except Exception as e:
                    if attempt < self.max_retries - 1:
                        await asyncio.sleep(self.retry_delay * (2 ** attempt))
                        continue
                    raise e
    
    async def batch_generate(self,
                           prompts: List[str],
                           system_prompt: Optional[str] = None,
                           **kwargs) -> List[LLMResponse]:
        """批量生成文本响应"""
        import asyncio
        tasks = [self.generate(prompt, system_prompt, **kwargs) for prompt in prompts]
        return await asyncio.gather(*tasks)
    
    async def get_available_models(self) -> List[Dict[str, Any]]:
        """获取可用模型列表"""
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{self.base_url}/models",
                headers=self.headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get("data", [])
                else:
                    raise Exception(f"Failed to get models: {response.status}")
    
    def estimate_cost(self, prompt_tokens: int, completion_tokens: int) -> float:
        """估算调用成本（基于DeepSeek定价）"""
        # DeepSeek定价（每1K tokens）
        pricing = {
            "deepseek/deepseek-r1-0528:free": {"input": 0, "output": 0},
            "deepseek/deepseek-chat": {"input": 0.00014, "output": 0.00028},
            "openai/gpt-4o-mini": {"input": 0.00015, "output": 0.0006},
            "anthropic/claude-3-haiku": {"input": 0.00025, "output": 0.00125}
        }
        
        model_pricing = pricing.get(self.model_name, {"input": 0.001, "output": 0.003})
        return (prompt_tokens * model_pricing["input"] + 
                completion_tokens * model_pricing["output"]) / 1000

# 添加异步支持
import asyncio