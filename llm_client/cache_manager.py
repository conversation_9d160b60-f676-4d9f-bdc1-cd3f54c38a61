"""
缓存管理器
实现智能缓存策略以优化成本和性能
"""

import hashlib
import json
import time
from typing import Dict, Any, Optional, List
from pathlib import Path
import pickle
import logging

logger = logging.getLogger(__name__)

class CacheManager:
    """智能缓存管理器"""
    
    def __init__(self, cache_dir: str = ".cache/llm", max_size_mb: int = 100):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.max_size_mb = max_size_mb
        self.cache_index = self._load_cache_index()
        
    def _load_cache_index(self) -> Dict[str, Dict[str, Any]]:
        """加载缓存索引"""
        index_file = self.cache_dir / "index.json"
        if index_file.exists():
            try:
                with open(index_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"Failed to load cache index: {e}")
        return {}
    
    def _save_cache_index(self):
        """保存缓存索引"""
        index_file = self.cache_dir / "index.json"
        try:
            with open(index_file, 'w', encoding='utf-8') as f:
                json.dump(self.cache_index, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Failed to save cache index: {e}")
    
    def _generate_cache_key(self, 
                          prompt: str, 
                          model: str, 
                          system_prompt: Optional[str] = None,
                          **kwargs) -> str:
        """生成缓存键"""
        cache_data = {
            "prompt": prompt,
            "model": model,
            "system_prompt": system_prompt,
            "kwargs": kwargs
        }
        cache_str = json.dumps(cache_data, sort_keys=True, ensure_ascii=False)
        return hashlib.md5(cache_str.encode()).hexdigest()
    
    def get(self, 
            prompt: str, 
            model: str, 
            system_prompt: Optional[str] = None,
            **kwargs) -> Optional[Any]:
        """获取缓存"""
        cache_key = self._generate_cache_key(prompt, model, system_prompt, **kwargs)
        
        if cache_key in self.cache_index:
            cache_info = self.cache_index[cache_key]
            cache_file = self.cache_dir / f"{cache_key}.pkl"
            
            # 检查缓存是否过期（默认7天）
            if time.time() - cache_info.get('timestamp', 0) < 7 * 24 * 3600:
                try:
                    with open(cache_file, 'rb') as f:
                        return pickle.load(f)
                except Exception as e:
                    logger.warning(f"Failed to load cache file: {e}")
                    self._remove_cache_entry(cache_key)
        
        return None
    
    def set(self, 
            prompt: str, 
            model: str, 
            response: Any,
            system_prompt: Optional[str] = None,
            **kwargs):
        """设置缓存"""
        cache_key = self._generate_cache_key(prompt, model, system_prompt, **kwargs)
        cache_file = self.cache_dir / f"{cache_key}.pkl"
        
        try:
            # 保存响应数据
            with open(cache_file, 'wb') as f:
                pickle.dump(response, f)
            
            # 更新索引
            self.cache_index[cache_key] = {
                "timestamp": time.time(),
                "model": model,
                "prompt_hash": hashlib.md5(prompt.encode()).hexdigest()[:8],
                "size": cache_file.stat().st_size
            }
            
            self._save_cache_index()
            self._cleanup_old_cache()
            
        except Exception as e:
            logger.error(f"Failed to save cache: {e}")
    
    def _remove_cache_entry(self, cache_key: str):
        """移除缓存条目"""
        if cache_key in self.cache_index:
            cache_file = self.cache_dir / f"{cache_key}.pkl"
            if cache_file.exists():
                cache_file.unlink()
            del self.cache_index[cache_key]
            self._save_cache_index()
    
    def _cleanup_old_cache(self):
        """清理旧缓存"""
        try:
            # 计算总大小
            total_size = sum(info.get('size', 0) for info in self.cache_index.values())
            
            if total_size > self.max_size_mb * 1024 * 1024:
                # 按时间排序，删除最旧的
                sorted_entries = sorted(
                    self.cache_index.items(),
                    key=lambda x: x[1].get('timestamp', 0)
                )
                
                for cache_key, info in sorted_entries:
                    if total_size <= self.max_size_mb * 1024 * 1024:
                        break
                    
                    self._remove_cache_entry(cache_key)
                    total_size -= info.get('size', 0)
                    
        except Exception as e:
            logger.error(f"Failed to cleanup cache: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        total_entries = len(self.cache_index)
        total_size = sum(info.get('size', 0) for info in self.cache_index.values())
        
        # 按模型统计
        model_stats = {}
        for info in self.cache_index.values():
            model = info.get('model', 'unknown')
            model_stats[model] = model_stats.get(model, 0) + 1
        
        return {
            "total_entries": total_entries,
            "total_size_mb": total_size / (1024 * 1024),
            "model_distribution": model_stats,
            "hit_rate": self._calculate_hit_rate()
        }
    
    def _calculate_hit_rate(self) -> float:
        """计算缓存命中率（简化版）"""
        # 实际实现需要记录访问日志
        return 0.0
    
    def clear_cache(self, model: Optional[str] = None):
        """清除缓存"""
        if model:
            # 清除特定模型的缓存
            keys_to_remove = [
                key for key, info in self.cache_index.items()
                if info.get('model') == model
            ]
            for key in keys_to_remove:
                self._remove_cache_entry(key)
        else:
            # 清除所有缓存
            for cache_file in self.cache_dir.glob("*.pkl"):
                cache_file.unlink()
            self.cache_index.clear()
            self._save_cache_index()
    
    def preload_patterns(self, patterns: List[Dict[str, Any]]):
        """预加载常见模式"""
        # 用于预加载常见的波浪分析模式
        for pattern in patterns:
            self.set(
                prompt=pattern["prompt"],
                model=pattern["model"],
                response=pattern["response"],
                system_prompt=pattern.get("system_prompt")
            )