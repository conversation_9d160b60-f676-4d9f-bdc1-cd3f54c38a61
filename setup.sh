#!/bin/bash
# ElliottAgents project setup with uv
# Requires Python 3.11+

echo "=== Setting up Python environment with uv ==="

# Create virtual environment
python3.11 -m venv .venv
source .venv/bin/activate

# Install uv if not available
if ! command -v uv &> /dev/null; then
    echo "Installing uv..."
    pip install uv
fi

# Install dependencies with uv
echo "Installing dependencies..."
uv pip install -r requirements.txt

# Set up environment variables
echo "export ELLIOTT_DB_HOST='localhost'" >> .env
echo "export ELLIOTT_DB_PORT=7687" >> .env
echo "export MEMGRAPH_USER='memgraph'" >> .env
echo "export MEMGRAPH_PASSWORD='memgraph'" >> .env

echo "=== Setup complete ==="
echo "Activate environment: source .venv/bin/activate"