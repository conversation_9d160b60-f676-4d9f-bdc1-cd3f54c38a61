#!/usr/bin/env python3
"""
ElliottAgents 主程序
基于学术论文的多智能体艾略特波浪分析系统
"""

import asyncio
import logging
import argparse
from pathlib import Path
import sys
from typing import Dict, Any, Optional

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from agents.wave_interpreter import WaveInterpreterAgent as WaveInterpreter
from llm_client.client_factory import LLMClientFactory
from rag_system.rag_enhanced_analyzer import RAGEhancedWaveAnalyzer as RAGEnhancedAnalyzer
from realtime_data.live_data_provider import LiveDataProvider
from crypto_data.crypto_provider import CryptoDataProvider
from performance.optimizer import monitor, measure_time
from web_interface.app import main as run_web_app

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('elliott_agents.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ElliottWaveAnalyzer:
    """艾略特波浪分析器主类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.wave_interpreter = WaveInterpreter()
        self.llm_client = LLMClientFactory.create_client()
        self.rag_analyzer = RAGEnhancedAnalyzer()
        self.live_provider = LiveDataProvider()
        self.crypto_provider = CryptoDataProvider()
        
        logger.info("ElliottWaveAnalyzer 初始化完成")
    
    @measure_time
    async def analyze_stock(self, symbol: str, market: str = "A股",
                          timeframe: str = "daily") -> Dict[str, Any]:
        """分析股票"""
        logger.info(f"开始分析 {symbol} ({market}) - {timeframe}")
        
        try:
            # 获取数据
            if market.upper() == "CRYPTO":
                data = await self.crypto_provider.get_crypto_data(symbol, timeframe)
            else:
                data = await self.live_provider.get_stock_data(symbol, market, timeframe)
            
            # 准备波浪分析数据
            wave_data = {
                "current_price": data['close'].iloc[-1] if len(data) > 0 else 0,
                "wave_structure": {"pattern": "待分析"},
                "technical_indicators": {"trend": "待计算"},
                "timeframe": timeframe
            }
            
            stock_info = {
                "symbol": symbol,
                "name": symbol
            }
            
            # 波浪分析
            wave_result = await self.wave_interpreter.analyze_wave_pattern(
                wave_data, stock_info
            )
            
            # RAG增强分析
            enhanced_analysis = await self.rag_analyzer.analyze_with_knowledge(
                wave_result["analysis"], {"symbol": symbol, "name": symbol}
            )
            
            # LLM生成报告
            report = await self.llm_client.generate_text(
                f"基于以下分析为{symbol}生成投资建议：{enhanced_analysis}"
            )
            
            result = {
                "symbol": symbol,
                "market": market,
                "timeframe": timeframe,
                "data": data,
                "wave_analysis": wave_result,
                "enhanced_analysis": enhanced_analysis,
                "report": report,
                "timestamp": asyncio.get_event_loop().time()
            }
            
            # 记录性能指标
            monitor.record_metric("analysis_complete", 1, {
                "symbol": symbol,
                "market": market,
                "timeframe": timeframe
            })
            
            return result
            
        except Exception as e:
            logger.error(f"分析失败 {symbol}: {str(e)}")
            raise
    
    async def analyze_portfolio(self, symbols: list, market: str = "A股") -> Dict[str, Any]:
        """分析投资组合"""
        logger.info(f"开始分析投资组合: {symbols}")
        
        tasks = [
            self.analyze_stock(symbol, market) 
            for symbol in symbols
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        successful = []
        failed = []
        
        for symbol, result in zip(symbols, results):
            if isinstance(result, Exception):
                failed.append({"symbol": symbol, "error": str(result)})
            else:
                successful.append(result)
        
        return {
            "successful": successful,
            "failed": failed,
            "total": len(symbols),
            "success_count": len(successful),
            "failure_count": len(failed)
        }
    
    async def generate_chinese_report(self, analysis_result: Dict[str, Any]) -> str:
        """生成中文分析报告"""
        symbol = analysis_result["symbol"]
        market = analysis_result["market"]
        
        wave_analysis = analysis_result.get("wave_analysis", {})
        enhanced_analysis = analysis_result.get("enhanced_analysis", {})
        
        prompt = f"""
        基于以下艾略特波浪分析结果，为{symbol}({market})生成一份专业的中文投资分析报告：
        
        波浪分析：{str(wave_analysis)}
        增强分析：{str(enhanced_analysis)}
        
        报告应包含：
        1. 当前波浪阶段判断
        2. 关键支撑位和阻力位
        3. 短期、中期、长期投资建议
        4. 风险提示
        5. 操作建议
        
        请用专业但易懂的语言撰写，适合中国投资者阅读。
        """
        
        return await self.llm_client.generate_text(prompt)
    
    def export_performance_report(self, filepath: str = "performance_report.json"):
        """导出性能报告"""
        monitor.export_metrics(filepath)
        logger.info(f"性能报告已导出到: {filepath}")

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="ElliottAgents - 艾略特波浪分析系统")
    parser.add_argument("--symbol", "-s", help="股票代码")
    parser.add_argument("--market", "-m", default="A股", help="市场类型")
    parser.add_argument("--timeframe", "-t", default="daily", help="时间框架")
    parser.add_argument("--web", action="store_true", help="启动Web界面")
    parser.add_argument("--portfolio", "-p", nargs="+", help="分析投资组合")
    parser.add_argument("--crypto", action="store_true", help="加密货币模式")
    
    args = parser.parse_args()
    
    analyzer = ElliottWaveAnalyzer()
    
    if args.web:
        logger.info("启动Web界面...")
        run_web_app()
        return
    
    if args.portfolio:
        market = "CRYPTO" if args.crypto else args.market
        results = await analyzer.analyze_portfolio(args.portfolio, market)
        
        print(f"\n投资组合分析完成:")
        print(f"成功: {results['success_count']}/{results['total']}")
        
        for result in results['successful']:
            report = await analyzer.generate_chinese_report(result)
            print(f"\n{'='*50}")
            print(f"{result['symbol']} 分析报告:")
            print(report)
        
        return
    
    if args.symbol:
        market = "CRYPTO" if args.crypto else args.market
        result = await analyzer.analyze_stock(args.symbol, market, args.timeframe)
        
        report = await analyzer.generate_chinese_report(result)
        
        print(f"\n{args.symbol} 艾略特波浪分析报告:")
        print("="*50)
        print(report)
        
        # 导出性能报告
        analyzer.export_performance_report()
        
    else:
        print("请提供股票代码或使用 --web 启动Web界面")
        print("示例:")
        print("  python main.py --symbol 000831 --market A股")
        print("  python main.py --symbol BTCUSDT --crypto")
        print("  python main.py --portfolio 000831 600519 000001 --market A股")
        print("  python main.py --web")

if __name__ == "__main__":
    asyncio.run(main())