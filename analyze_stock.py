import pandas as pd
import os
import sys
import json # Added for debugging

# Add the project root to sys.path to ensure imports work correctly
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# --- Debugging: Test file write permissions ---
try:
    with open("test_write_permission.txt", "w") as f:
        f.write("This is a test.")
    print("Successfully wrote test_write_permission.txt")
except Exception as e:
    print(f"Error writing test_write_permission.txt: {e}")
# --- End Debugging ---

from elliott_wave.pattern_recognition import find_elliott_wave_patterns
from elliott_wave.elliott_wave_visualizer import plot_elliott_wave_patterns
from elliott_wave.forecast_generator import ForecastGenerator
from elliott_wave.similarity_search import SimilaritySearcher # Import the new class
from database.data_manager import save_elliott_wave_patterns_to_db
from elliott_wave.ml_validator import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from elliott_wave.multi_timeframe_analysis import validate_and_score_by_subwaves

def analyze_stock_data(stock_code: str, file_path: str):
    print(f"Starting analyze_stock_data for {stock_code}...")
    try:
        df = pd.read_parquet(file_path)
        print("Data loaded successfully.")

        if 'date' not in df.columns:
            df.reset_index(inplace=True)
        df['date'] = pd.to_datetime(df['date'])
        df.set_index('date', inplace=True)
        df.sort_index(inplace=True)

        required_cols = {'open', 'high', 'low', 'close', 'volume'}
        if not required_cols.issubset(df.columns):
            return {"error": f"Missing required columns: {required_cols - set(df.columns)}"}

        daily_df = df.copy()
        monthly_df = daily_df.resample('ME').agg({
            'open': 'first', 'high': 'max', 'low': 'min', 'close': 'last', 'volume': 'sum'
        }).dropna()

        all_timeframes_data = {
            'daily': daily_df,
            'monthly': monthly_df
        }

        ml_validator = MLValidator()
        model_path = "drl_logs/ml_validator_model.joblib"
        if os.path.exists(model_path):
            ml_validator.load_model(model_path)
        else:
            print("Warning: No pre-trained ML model found.")

        print("Starting Elliott Wave pattern recognition...")
        raw_hypotheses = {}
        for timeframe, df_timeframe in all_timeframes_data.items():
            print(f"--- Analyzing {timeframe} data ---")
            patterns_result = find_elliott_wave_patterns(df_timeframe, ml_validator=ml_validator)
            raw_hypotheses[timeframe] = patterns_result.get("pattern_hypotheses", {})
            print(f"Found {sum(len(v) for v in raw_hypotheses[timeframe].values())} raw {timeframe} pattern hypotheses.")

        print("Performing Multi-Timeframe Consistency Analysis...")
        raw_hypotheses['monthly'] = validate_and_score_by_subwaves(
            parent_hypotheses=raw_hypotheses.get('monthly', {}),
            full_df=all_timeframes_data['daily'],
            ml_validator=ml_validator
        )

        all_final_patterns = []
        for timeframe, hypotheses_dict in raw_hypotheses.items():
            for patterns_list in hypotheses_dict.values():
                for p in patterns_list:
                    p['timeframe'] = timeframe
                    all_final_patterns.append(p)
        
        # Sort by end date to find the most recent pattern for forecasting
        all_final_patterns.sort(key=lambda p: pd.to_datetime(p['end_date']), reverse=True)
        print(f"Detected {len(all_final_patterns)} total patterns after MTA.")

        forecast_data = None
        similar_patterns = None
        if all_final_patterns:
            top_pattern = all_final_patterns[0] # The most recent pattern
            print(f"Generating forecast based on most recent pattern: {top_pattern['pattern_name']} ending on {top_pattern['end_date']} ({top_pattern['confidence']:.2f})")
            forecast_generator = ForecastGenerator()
            forecast_data = forecast_generator.generate_forecast(top_pattern)

            print("Finding similar historical patterns...")
            similarity_searcher = SimilaritySearcher(daily_df)
            similar_patterns = similarity_searcher.find_similar(top_pattern)

        print("Generating final plot...")
        plot_filename = f"elliott_wave_{stock_code}_full_history.html"
        fig, _ = plot_elliott_wave_patterns(daily_df, all_final_patterns, {}, stock_code, forecast_data, similar_patterns)
        if fig:
            fig.write_html(plot_filename)
            print(f"Successfully saved plot to {plot_filename}")

        final_report = {
            'patterns': all_final_patterns,
            'forecast': forecast_data,
            'similar_patterns': similar_patterns
        }
        output_json_filename = f"analysis_report_{stock_code}_full_history.json"
        with open(output_json_filename, 'w') as f:
            json.dump(final_report, f, indent=2, default=str)
        print(f"Successfully wrote final report to {output_json_filename}")

        return final_report

    except Exception as e:
        print(f"An error occurred during analysis: {e}")
        import traceback
        traceback.print_exc()
        return {"error": str(e)}

if __name__ == '__main__':
    stock_code = '600111'
    file_path = os.path.join(os.path.dirname(__file__), 'stock_data', f'{stock_code}_full_history.parquet')
    analyze_stock_data(stock_code, file_path)