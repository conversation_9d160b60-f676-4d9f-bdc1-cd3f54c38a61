"""
RAG增强的波浪分析器
结合知识检索和LLM分析的增强系统
"""

import json
from typing import Dict, Any, List, Optional
from datetime import datetime
from llm_client import create_llm_client, LLMResponse
from .knowledge_base import knowledge_base
import logging

logger = logging.getLogger(__name__)

class RAGEhancedWaveAnalyzer:
    """RAG增强的波浪分析器"""
    
    def __init__(self, llm_client=None):
        self.llm_client = llm_client or create_llm_client()
        self.kb = knowledge_base
    
    async def analyze_with_knowledge(self, 
                                   wave_data: Dict[str, Any],
                                   stock_info: Dict[str, str],
                                   query: Optional[str] = None) -> Dict[str, Any]:
        """基于知识检索的波浪分析"""
        
        # 1. 根据波浪数据生成查询
        if not query:
            query = self._generate_query_from_data(wave_data)
        
        # 2. 检索相关知识
        relevant_knowledge = self.kb.search(query, top_k=5)
        
        # 3. 构建增强提示词
        enhanced_prompt = self._build_enhanced_prompt(
            wave_data, stock_info, relevant_knowledge
        )
        
        # 4. 调用LLM进行分析
        response = await self.llm_client.generate(
            prompt=enhanced_prompt,
            system_prompt=self._get_rag_system_prompt(),
            temperature=0.3,
            max_tokens=2500
        )
        
        # 5. 解析和增强结果
        analysis = self._parse_enhanced_response(
            response.content, relevant_knowledge, wave_data
        )
        
        return {
            "analysis": analysis,
            "knowledge_used": relevant_knowledge,
            "metadata": {
                "model": response.model,
                "tokens": response.usage,
                "cost": response.cost,
                "timestamp": datetime.now().isoformat(),
                "query": query
            }
        }
    
    def _generate_query_from_data(self, wave_data: Dict[str, Any]) -> str:
        """从波浪数据生成查询"""
        
        # 提取关键特征
        wave_structure = wave_data.get("wave_structure", {})
        current_wave = wave_structure.get("current_wave", "")
        pattern_type = wave_structure.get("pattern_type", "")
        
        # 构建查询
        query_parts = []
        
        if current_wave:
            query_parts.append(f"{current_wave} 分析")
        
        if pattern_type:
            query_parts.append(f"{pattern_type} 模式")
        
        # 添加技术指标相关查询
        indicators = wave_data.get("technical_indicators", {})
        if indicators.get("rsi"):
            rsi = indicators["rsi"]
            if rsi > 70:
                query_parts.append("超买 RSI")
            elif rsi < 30:
                query_parts.append("超卖 RSI")
        
        if indicators.get("macd"):
            macd = indicators["macd"]
            if macd > 0:
                query_parts.append("MACD 金叉")
            else:
                query_parts.append("MACD 死叉")
        
        return " ".join(query_parts) if query_parts else "波浪理论 分析"
    
    def _build_enhanced_prompt(self, 
                             wave_data: Dict[str, Any], 
                             stock_info: Dict[str, str],
                             relevant_knowledge: List[Dict[str, Any]]) -> str:
        """构建增强提示词"""
        
        # 构建知识上下文
        knowledge_context = "\n\n".join([
            f"知识{i+1} ({item['metadata']['category']}): {item['content']}"
            for i, item in enumerate(relevant_knowledge)
        ])
        
        prompt = f"""
基于以下波浪数据和相关理论知识，请进行专业的波浪分析：

股票信息：
- 代码：{stock_info.get('symbol', '未知')}
- 名称：{stock_info.get('name', '未知')}
- 当前价格：{wave_data.get('current_price', 0)}

波浪数据：
{json.dumps(wave_data, ensure_ascii=False, indent=2)}

相关理论知识：
{knowledge_context}

请结合上述理论知识，提供以下分析：

1. **理论依据**：指出分析所依据的具体波浪理论原则
2. **模式识别**：识别当前符合的波浪模式
3. **规则验证**：验证当前波浪是否符合波浪理论规则
4. **关键价位**：基于理论计算支撑阻力位
5. **时间周期**：分析时间周期的协调性
6. **风险提示**：基于理论的风险警告
7. **操作建议**：结合理论的具体操作建议

要求：
- 必须引用具体的波浪理论原则
- 结合A股市场特点进行分析
- 提供多种可能性分析
- 强调风险控制的重要性

请用中文回答，确保分析专业、准确、实用。
"""
        
        return prompt
    
    def _get_rag_system_prompt(self) -> str:
        """获取RAG系统提示词"""
        return """你是一个基于RAG（检索增强生成）的艾略特波浪理论专家。

你的分析必须：
1. **基于理论**：严格依据检索到的理论知识进行分析
2. **结合实践**：将理论与当前市场情况相结合
3. **多维度验证**：从多个角度验证波浪计数的合理性
4. **风险控制**：始终强调风险管理的重要性
5. **清晰表达**：用简洁易懂的中文解释复杂概念

分析框架：
- 首先确认当前波浪位置
- 然后验证是否符合波浪理论规则
- 接着计算关键价位和时间窗口
- 最后给出具体的操作建议

请确保每个结论都有理论依据支持。"""
    
    def _parse_enhanced_response(self, 
                               content: str, 
                               knowledge_used: List[Dict[str, Any]],
                               wave_data: Dict[str, Any]) -> Dict[str, Any]:
        """解析增强响应"""
        
        # 提取结构化信息
        analysis = {
            "theoretical_basis": self._extract_section(content, "理论依据"),
            "pattern_recognition": self._extract_section(content, "模式识别"),
            "rule_validation": self._extract_section(content, "规则验证"),
            "key_levels": self._extract_section(content, "关键价位"),
            "time_analysis": self._extract_section(content, "时间周期"),
            "risk_warnings": self._extract_section(content, "风险提示"),
            "trading_recommendations": self._extract_section(content, "操作建议"),
            "raw_content": content,
            "confidence_score": self._calculate_confidence(knowledge_used, wave_data)
        }
        
        # 提取引用的知识
        cited_knowledge = self._extract_cited_knowledge(content, knowledge_used)
        analysis["cited_knowledge"] = cited_knowledge
        
        return analysis
    
    def _extract_section(self, content: str, section_name: str) -> str:
        """提取特定章节内容"""
        lines = content.split('\n')
        section_lines = []
        in_section = False
        
        for line in lines:
            line_lower = line.lower()
            if section_name.lower() in line_lower:
                in_section = True
                continue
            elif in_section and line.strip() and any(char in line for char in ['1.', '2.', '3.', '4.', '5.', '6.', '7.']):
                # 遇到下一个章节
                break
            elif in_section and line.strip():
                section_lines.append(line.strip())
        
        return '\n'.join(section_lines).strip()
    
    def _extract_cited_knowledge(self, content: str, knowledge_used: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """提取引用的知识"""
        cited = []
        
        for item in knowledge_used:
            # 检查内容中是否提及该知识
            content_lower = content.lower()
            knowledge_lower = item["content"].lower()
            
            # 简单的关键词匹配
            keywords = knowledge_lower.split()[:5]  # 取前5个词作为关键词
            if any(keyword in content_lower for keyword in keywords):
                cited.append({
                    "content": item["content"][:200] + "...",
                    "category": item["metadata"]["category"],
                    "score": item["score"]
                })
        
        return cited
    
    def _calculate_confidence(self, 
                            knowledge_used: List[Dict[str, Any]], 
                            wave_data: Dict[str, Any]) -> float:
        """计算分析置信度"""
        
        # 基础置信度
        base_confidence = 0.5
        
        # 知识相关性加分
        knowledge_score = sum(item["score"] for item in knowledge_used) / len(knowledge_used) if knowledge_used else 0
        
        # 数据完整性加分
        data_completeness = 0
        required_fields = ["current_price", "wave_structure", "technical_indicators"]
        for field in required_fields:
            if field in wave_data and wave_data[field]:
                data_completeness += 0.15
        
        # 技术指标完整性加分
        indicators = wave_data.get("technical_indicators", {})
        indicator_score = len(indicators) * 0.05
        
        # 计算最终置信度
        confidence = min(0.95, base_confidence + knowledge_score * 0.3 + data_completeness + indicator_score)
        
        return round(confidence, 2)
    
    async def generate_contextual_analysis(self, 
                                         wave_data: Dict[str, Any],
                                         stock_info: Dict[str, str],
                                         market_context: Dict[str, Any]) -> Dict[str, Any]:
        """生成上下文分析"""
        
        # 构建上下文查询
        context_queries = [
            f"{stock_info.get('sector', '')} 行业 波浪特征",
            f"A股 {market_context.get('market_phase', '')} 波浪模式",
            f"{market_context.get('market_cap', '')} 市值 波浪分析"
        ]
        
        # 检索相关知识
        all_knowledge = []
        for query in context_queries:
            knowledge = self.kb.search(query, top_k=2)
            all_knowledge.extend(knowledge)
        
        # 去重
        seen = set()
        unique_knowledge = []
        for item in all_knowledge:
            key = item["content"][:50]
            if key not in seen:
                seen.add(key)
                unique_knowledge.append(item)
        
        # 生成分析
        prompt = f"""
请基于以下市场上下文和波浪理论，进行综合分析：

股票信息：
{json.dumps(stock_info, ensure_ascii=False, indent=2)}

市场上下文：
{json.dumps(market_context, ensure_ascii=False, indent=2)}

波浪数据：
{json.dumps(wave_data, ensure_ascii=False, indent=2)}

相关背景知识：
{json.dumps([item['content'] for item in unique_knowledge], ensure_ascii=False, indent=2)}

请提供：
1. **行业特征分析**：该行业股票的波浪特征
2. **市场环境适应性**：当前市场环境下的波浪表现
3. **市值影响**：不同市值对波浪模式的影响
4. **综合判断**：结合所有因素的最终分析
5. **策略建议**：基于上下文的交易策略

请用中文回答，确保分析全面且实用。
"""
        
        response = await self.llm_client.generate(
            prompt=prompt,
            system_prompt="你是一个结合市场上下文进行波浪分析的专家。请综合考虑行业、市场环境、市值等因素，提供专业的波浪分析。",
            temperature=0.3,
            max_tokens=2000
        )
        
        return {
            "contextual_analysis": response.content,
            "knowledge_used": unique_knowledge,
            "metadata": {
                "model": response.model,
                "tokens": response.usage,
                "cost": response.cost
            }
        }