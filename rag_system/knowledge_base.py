"""
知识库系统
基于RAG的艾略特波浪理论知识检索
"""

import json
import os
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import logging

logger = logging.getLogger(__name__)

class ElliottWaveKnowledgeBase:
    """艾略特波浪理论知识库"""
    
    def __init__(self, knowledge_dir: str = "docs/knowledge"):
        self.knowledge_dir = Path(knowledge_dir)
        self.knowledge_dir.mkdir(parents=True, exist_ok=True)
        
        # 知识库数据
        self.documents = []
        self.metadata = []
        self.vectorizer = TfidfVectorizer(
            max_features=1000,
            stop_words=['的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'],
            ngram_range=(1, 2)
        )
        self.tfidf_matrix = None
        
        # 加载知识库
        self._load_knowledge_base()
    
    def _load_knowledge_base(self):
        """加载知识库"""
        # 加载内置知识
        self._load_builtin_knowledge()
        
        # 加载外部知识文件
        self._load_external_knowledge()
        
        # 构建向量索引
        if self.documents:
            self._build_index()
    
    def _load_builtin_knowledge(self):
        """加载内置知识"""
        builtin_knowledge = [
            {
                "content": "艾略特波浪理论是由拉尔夫·尼尔森·艾略特在1930年代提出的技术分析理论。该理论认为市场价格以可预测的波浪模式运动，这些模式反映了投资者心理的变化。",
                "category": "基础理论",
                "tags": ["艾略特波浪理论", "基础", "历史"]
            },
            {
                "content": "推动浪由5个子浪组成，标记为1-2-3-4-5。其中浪1、3、5是推动浪，浪2和4是调整浪。浪3通常是最长且最强的浪，永远不会是最短的浪。",
                "category": "推动浪",
                "tags": ["推动浪", "5浪结构", "浪3", "规则"]
            },
            {
                "content": "调整浪通常由3个子浪组成，标记为A-B-C。调整浪的模式包括锯齿形、平台形和三角形。调整浪的目的是修正之前的推动浪。",
                "category": "调整浪",
                "tags": ["调整浪", "ABC", "锯齿形", "平台形", "三角形"]
            },
            {
                "content": "波浪理论的三大铁律：1) 浪2不能回撤超过浪1的100%；2) 浪3不能是最短的推动浪；3) 浪4不能与浪1的价格区域重叠。",
                "category": "基本规则",
                "tags": ["铁律", "规则", "浪2", "浪3", "浪4"]
            },
            {
                "content": "斐波那契数列在波浪理论中的应用：浪2通常回撤浪1的50%、61.8%或38.2%；浪3通常是浪1的1.618倍；浪5通常是浪1的0.618或1倍。",
                "category": "斐波那契",
                "tags": ["斐波那契", "回撤", "目标位", "比例"]
            },
            {
                "content": "延长浪是指某个推动浪比正常情况更长。延长浪通常出现在浪3，有时也出现在浪1或浪5。延长浪本身也包含5个子浪结构。",
                "category": "延长浪",
                "tags": ["延长浪", "浪3", "子浪", "结构"]
            },
            {
                "content": "失败浪是指浪5未能超过浪3的终点，这通常是趋势减弱的信号。失败浪在顶部比底部更常见，预示着趋势可能反转。",
                "category": "失败浪",
                "tags": ["失败浪", "浪5", "趋势减弱", "反转信号"]
            },
            {
                "content": "波浪的级别从大到小分为：超级循环浪、循环浪、大浪、中浪、小浪、细浪、微浪、次微浪。每个级别的浪包含下一级别的5浪或3浪结构。",
                "category": "浪级",
                "tags": ["浪级", "超级循环浪", "循环浪", "级别"]
            },
            {
                "content": "成交量在波浪理论中的重要性：成交量应在浪3达到最大，浪5次之，浪1最小。成交量萎缩可能预示趋势减弱。",
                "category": "成交量",
                "tags": ["成交量", "确认", "浪3", "趋势"]
            },
            {
                "content": "通道技术：连接浪2和浪4的终点形成通道线，浪5通常触及或突破通道线。通道线有助于预测浪5的目标位。",
                "category": "通道技术",
                "tags": ["通道线", "浪2", "浪4", "浪5", "目标位"]
            },
            {
                "content": "中国股市的波浪特征：A股市场受政策影响较大，波浪结构可能不如成熟市场清晰。需要结合政策面、资金面等因素综合分析。",
                "category": "A股特征",
                "tags": ["A股", "政策", "资金面", "中国特色"]
            },
            {
                "content": "波浪理论的局限性：市场噪音可能干扰波浪识别；主观性强，不同分析师可能得出不同结论；需要结合其他技术指标验证。",
                "category": "局限性",
                "tags": ["局限性", "噪音", "主观性", "验证"]
            }
        ]
        
        for item in builtin_knowledge:
            self.documents.append(item["content"])
            self.metadata.append({
                "category": item["category"],
                "tags": item["tags"],
                "source": "builtin"
            })
    
    def _load_external_knowledge(self):
        """加载外部知识文件"""
        knowledge_files = [
            "elliott_wave_theory.txt",
            "ewp_patterns_compendium.md",
            "a_share_patterns.md"
        ]
        
        for filename in knowledge_files:
            file_path = self.knowledge_dir / filename
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 按段落分割
                    paragraphs = content.split('\n\n')
                    for para in paragraphs:
                        if para.strip() and len(para.strip()) > 50:
                            self.documents.append(para.strip())
                            self.metadata.append({
                                "category": "external",
                                "tags": [filename.replace('.txt', '').replace('.md', '')],
                                "source": str(file_path)
                            })
                            
                except Exception as e:
                    logger.warning(f"Failed to load {filename}: {e}")
    
    def _build_index(self):
        """构建向量索引"""
        try:
            self.tfidf_matrix = self.vectorizer.fit_transform(self.documents)
            logger.info(f"Built TF-IDF index with {len(self.documents)} documents")
        except Exception as e:
            logger.error(f"Failed to build index: {e}")
    
    def search(self, query: str, top_k: int = 5, threshold: float = 0.1) -> List[Dict[str, Any]]:
        """搜索相关知识"""
        
        if not self.tfidf_matrix or not self.documents:
            return []
        
        try:
            # 转换查询为向量
            query_vector = self.vectorizer.transform([query])
            
            # 计算相似度
            similarities = cosine_similarity(query_vector, self.tfidf_matrix).flatten()
            
            # 获取top-k结果
            top_indices = np.argsort(similarities)[::-1][:top_k]
            
            results = []
            for idx in top_indices:
                if similarities[idx] >= threshold:
                    results.append({
                        "content": self.documents[idx],
                        "metadata": self.metadata[idx],
                        "score": float(similarities[idx])
                    })
            
            return results
            
        except Exception as e:
            logger.error(f"Search failed: {e}")
            return []
    
    def get_by_category(self, category: str) -> List[Dict[str, Any]]:
        """按类别获取知识"""
        results = []
        for i, meta in enumerate(self.metadata):
            if meta.get("category") == category:
                results.append({
                    "content": self.documents[i],
                    "metadata": meta
                })
        return results
    
    def get_by_tags(self, tags: List[str]) -> List[Dict[str, Any]]:
        """按标签获取知识"""
        results = []
        for i, meta in enumerate(self.metadata):
            doc_tags = meta.get("tags", [])
            if any(tag in doc_tags for tag in tags):
                results.append({
                    "content": self.documents[i],
                    "metadata": meta
                })
        return results
    
    def add_knowledge(self, content: str, category: str, tags: List[str], source: str = "user"):
        """添加新知识"""
        self.documents.append(content)
        self.metadata.append({
            "category": category,
            "tags": tags,
            "source": source,
            "added_time": str(Path().stat().st_mtime)
        })
        
        # 重新构建索引
        if self.tfidf_matrix is not None:
            self._build_index()
    
    def get_categories(self) -> List[str]:
        """获取所有类别"""
        categories = set()
        for meta in self.metadata:
            categories.add(meta.get("category", "unknown"))
        return sorted(list(categories))
    
    def get_tags(self) -> List[str]:
        """获取所有标签"""
        tags = set()
        for meta in self.metadata:
            for tag in meta.get("tags", []):
                tags.add(tag)
        return sorted(list(tags))
    
    def get_stats(self) -> Dict[str, Any]:
        """获取知识库统计"""
        return {
            "total_documents": len(self.documents),
            "categories": self.get_categories(),
            "tags": self.get_tags(),
            "sources": list(set(meta.get("source", "unknown") for meta in self.metadata))
        }
    
    def save_knowledge(self, filename: str = "knowledge_base.json"):
        """保存知识库到文件"""
        data = {
            "documents": self.documents,
            "metadata": self.metadata,
            "stats": self.get_stats()
        }
        
        file_path = self.knowledge_dir / filename
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info(f"Knowledge base saved to {file_path}")
        except Exception as e:
            logger.error(f"Failed to save knowledge base: {e}")
    
    def load_knowledge(self, filename: str = "knowledge_base.json"):
        """从文件加载知识库"""
        file_path = self.knowledge_dir / filename
        if not file_path.exists():
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.documents = data.get("documents", [])
            self.metadata = data.get("metadata", [])
            
            if self.documents:
                self._build_index()
            
            logger.info(f"Knowledge base loaded from {file_path}")
            
        except Exception as e:
            logger.error(f"Failed to load knowledge base: {e}")

# 全局知识库实例
knowledge_base = ElliottWaveKnowledgeBase()

if __name__ == "__main__":
    # 测试知识库
    print("知识库统计:", knowledge_base.get_stats())
    
    # 测试搜索
    results = knowledge_base.search("推动浪 规则")
    print(f"\n搜索结果: {len(results)} 条")
    for result in results[:3]:
        print(f"- {result['content'][:100]}...")
        print(f"  类别: {result['metadata']['category']}, 分数: {result['score']:.3f}")