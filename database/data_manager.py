import pandas as pd
import os
from datetime import datetime, timedelta
from data_acquisition.stock_data import fetch_single_stock
from database.neo4j_db import Neo4jDB
import logging

DATA_DIR = "stock_data"
if not os.path.exists(DATA_DIR):
    os.makedirs(DATA_DIR)

def get_stock_filepath(stock_code: str) -> str:
    """Returns the standardized path for a stock's data file."""
    return os.path.join(DATA_DIR, f"{stock_code}.parquet")

def update_stock_data(stock_code: str) -> pd.DataFrame:
    """
    Updates and returns the full historical data for a stock.
    - If data exists, it fetches only the new data since the last record.
    - If no data exists, it fetches the full history.
    """
    filepath = get_stock_filepath(stock_code)
    
    if os.path.exists(filepath):
        try:
            df = pd.read_parquet(filepath)
            last_date = df.index.max()
            start_date = (last_date + timedelta(days=1)).strftime('%Y%m%d')
            end_date = datetime.now().strftime('%Y%m%d')
            
            if pd.to_datetime(start_date) > datetime.now():
                logging.info(f"Data for {stock_code} is already up to date.")
                return df

            logging.info(f"Updating data for {stock_code} from {start_date} to {end_date}...")
            new_data = fetch_single_stock(stock_code, start_date, end_date)
            
            if not new_data.empty:
                df = pd.concat([df, new_data])
                df.to_parquet(filepath)
                logging.info(f"Successfully updated {stock_code} with {len(new_data)} new records.")
            
            return df
        except Exception as e:
            logging.error(f"Error updating data for {stock_code}: {e}. Refetching all data.")
            # Fallback to refetching all data if update fails
            pass

    # If file doesn't exist or update failed, fetch all data
    logging.info(f"Fetching full history for {stock_code}...")
    full_df = fetch_single_stock(stock_code, "19900101", datetime.now().strftime('%Y%m%d'))
    if not full_df.empty:
        full_df.to_parquet(filepath)
        logging.info(f"Saved full history for {stock_code} to {filepath}")
    return full_df

def save_elliott_wave_patterns_to_db(stock_code: str, patterns: list):
    """
    Saves Elliott Wave patterns to the Neo4j graph database.
    Each pattern is stored as a node, linked to the stock.
    """
    logging.info(f"Attempting to save {len(patterns)} Elliott Wave patterns for {stock_code} to Neo4j...")
    db = Neo4jDB()
    try:
        # Ensure the Stock node exists (Neo4jDB's init_schema handles this for constraints)
        # We can explicitly create the stock node if it doesn't exist, or rely on MERGE in save_pattern
        # For now, let's rely on MERGE in save_pattern to create the stock node if it doesn't exist.

        for pattern in patterns:
            # Convert pattern data to a format suitable for save_pattern
            # Note: save_pattern expects 'company_name' and 'market' which are not in our pattern dict
            # We'll pass empty strings for now, or retrieve from a stock info source if available.
            pattern_data_for_db = {
                "pattern_name": pattern.get('pattern_name', 'Unknown Pattern'),
                "pattern_type": pattern.get('pattern_type', 'unknown'),
                "confidence": pattern.get('confidence', 0.0),
                "start_date": pattern.get('start_date'),
                "end_date": pattern.get('end_date'),
                "company_name": "", # Placeholder, ideally fetched from stock info
                "market": ""
            }
            db.save_pattern(
                pattern_data=pattern_data_for_db,
                stock_symbol=stock_code,
                timeframe=pattern.get('timeframe', 'daily')
            )
        logging.info(f"Successfully saved {len(patterns)} patterns for {stock_code}.")
    except Exception as e:
        logging.error(f"Failed to save Elliott Wave patterns to Neo4j for {stock_code}: {e}")
    finally:
        db.close()

if __name__ == '__main__':
    # Test the data manager
    # df = update_stock_data('000831') # Commented out to focus on DB test
    # print("\nData for 000831:")
    # print(df.head())
    # print(df.tail())

    # Dummy patterns for testing save_elliott_wave_patterns_to_db
    dummy_patterns = [
        {
            "pattern_name": "Bullish Impulse Wave (Hurst: 0.75)",
            "pattern_type": "impulsive_bullish",
            "start_date": "2023-01-01",
            "end_date": "2023-01-31",
            "confidence": 0.85,
            "hurst_exponent": 0.75,
            "timeframe": "daily",
            "mta_validation": "Pass"
        },
        {
            "pattern_name": "Bearish Corrective Wave (ABC)",
            "pattern_type": "corrective_bearish",
            "start_date": "2023-02-01",
            "end_date": "2023-02-15",
            "confidence": 0.70,
            "hurst_exponent": 0.60,
            "timeframe": "weekly",
            "mta_validation": "Fail"
        }
    ]
    print("\nSaving dummy patterns to Neo4j...")
    save_elliott_wave_patterns_to_db('000831', dummy_patterns)
    print("Dummy patterns save attempt finished.")