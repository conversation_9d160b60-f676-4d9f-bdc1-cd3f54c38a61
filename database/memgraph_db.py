"""
Memgraph database connection and operations
"""

import os
from gqlalchemy import Memgraph, Field, Node, Relationship
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Stock(Node):
    """Stock node representing a company"""
    symbol: str = Field(index=True, unique=True, db=Memgraph())
    name: str = Field()
    market: str = Field()
    
class Pattern(Node):
    """Elliott Wave pattern node"""
    name: str = Field(unique=True)
    pattern_type: str = Field()  # impulsive/corrective
    confidence: float = Field()
    
class DetectedAt(Relationship, type="DETECTED_AT"):
    """Relationship between Pattern and Stock at specific time"""
    start: str = Field()
    end: str = Field()
    timeframe: str = Field()  # daily/hourly
    
class EvolvesTo(Relationship, type="EVOLVES_TO"):
    """Pattern evolution relationship"""
    probability: float = Field()
    
class MemgraphDB:
    def __init__(self):
        self.host = os.getenv('MEMGRAPH_HOST', 'localhost')
        self.port = int(os.getenv('MEMGRAPH_PORT', 7687))
        self.user = os.getenv('MEMGRAPH_USER', 'memgraph')
        self.password = os.getenv('MEMGRAPH_PASSWORD', 'memgraph')
        self.use_ssl = os.getenv('MEMGRAPH_USE_SSL', 'true').lower() == 'true'
        self.db = self._connect()
        self.init_schema()
        
    def _connect(self):
        """Establish connection to Memgraph"""
        try:
            memgraph = Memgraph(
                host=self.host,
                port=self.port,
                username=self.user,
                password=self.password,
                encrypted=self.use_ssl
            )
            
            print(f"Connected to Memgraph at {self.host}:{self.port}")
            return memgraph
        except Exception as e:
            print(f"Failed to connect to Memgraph: {e}")
            return None
            
    def execute_query(self, query: str, params: dict = {}):
        """Execute Cypher query and return results"""
        if not self.db:
            print("Database not connected")
            return None
            
        try:
            return list(self.db.execute_and_fetch(query, params))
        except Exception as e:
            print(f"Query failed: {e}")
            return None
            
    def init_schema(self):
        """Initialize database schema"""
        queries = [
            "CREATE CONSTRAINT ON (s:Stock) ASSERT s.symbol IS UNIQUE;",
            "CREATE CONSTRAINT ON (p:Pattern) ASSERT p.name IS UNIQUE;",
            "CREATE INDEX ON :Stock(name);",
            "CREATE INDEX ON :Pattern(pattern_type);"
        ]
        
        for query in queries:
            self.execute_query(query)
        print("Database schema initialized")

    def clear_database(self):
        """Clear all data from the database"""
        self.execute_query("MATCH (n) DETACH DELETE n;")
        print("Database cleared.")
        
    def save_pattern(self, pattern_data: dict, stock_symbol: str, timeframe: str):
        """
        Save detected pattern to knowledge graph
        - Creates Stock node if not exists
        - Creates Pattern node
        - Creates DETECTED_AT relationship
        """
        # Create or get Stock
        stock_query = """
        MERGE (s:Stock {symbol: $symbol})
        ON CREATE SET s.name = $name, s.market = $market
        RETURN id(s)
        """
        stock_params = {
            "symbol": stock_symbol,
            "name": pattern_data.get("company_name", ""),
            "market": "A-share"
        }
        stock_id = self.execute_query(stock_query, stock_params)[0]["id(s)"]
        
        # Create Pattern
        pattern_query = """
        MERGE (p:Pattern {name: $name})
        ON CREATE SET p.pattern_type = $type, p.confidence = $confidence
        RETURN id(p)
        """
        pattern_params = {
            "name": pattern_data["pattern_name"],
            "type": pattern_data["pattern_type"],
            "confidence": pattern_data["confidence"]
        }
        pattern_id = self.execute_query(pattern_query, pattern_params)[0]["id(p)"]
        
        # Create relationship
        rel_query = """
        MATCH (s:Stock), (p:Pattern)
        WHERE id(s) = $stock_id AND id(p) = $pattern_id
        MERGE (p)-[r:DETECTED_AT {timeframe: $timeframe}]->(s)
        SET r.start = $start, r.end = $end
        """
        rel_params = {
            "stock_id": stock_id,
            "pattern_id": pattern_id,
            "timeframe": timeframe,
            "start": pattern_data["start_date"],
            "end": pattern_data["end_date"]
        }
        self.execute_query(rel_query, rel_params)
        print(f"Saved pattern {pattern_data['pattern_name']} for {stock_symbol}")

if __name__ == '__main__':
    # Test database connection
    db = MemgraphDB()
    if db.db:
        print("\n--- Testing Database Connection ---")
        # Clear database for a clean test
        db.clear_database()
        
        # Test saving a pattern
        test_pattern = {
            "pattern_name": "Test Impulse Wave",
            "pattern_type": "impulsive",
            "confidence": 0.85,
            "start_date": "2023-01-01",
            "end_date": "2023-01-10",
            "company_name": "Test Inc."
        }
        db.save_pattern(test_pattern, "TEST001", "daily")
        
        # Verify data was saved
        results = db.execute_query("MATCH (p:Pattern)-[r:DETECTED_AT]->(s:Stock) RETURN p, r, s")
        print("\n--- Verification Query Results ---")
        for row in results:
            print(row)
        
        print("\n--- Test Complete ---")