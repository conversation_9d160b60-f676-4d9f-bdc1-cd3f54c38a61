import os
from neo4j import GraphDatabase
from dotenv import load_dotenv
from datetime import datetime

# Load environment variables
load_dotenv()

class Neo4jDB:
    def __init__(self):
        self.uri = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
        self.user = os.getenv('NEO4J_USER', 'neo4j')
        self.password = os.getenv('NEO4J_PASSWORD', 'neo4j')
        self.driver = None
        self._connect()
        self.init_schema()

    def _connect(self):
        """Establish connection to Neo4j"""
        try:
            self.driver = GraphDatabase.driver(self.uri, auth=(self.user, self.password))
            self.driver.verify_connectivity()
            print(f"Connected to Neo4j at {self.uri}")
        except Exception as e:
            print(f"Failed to connect to Neo4j: {e}")
            self.driver = None

    def close(self):
        """Close the Neo4j connection"""
        if self.driver:
            self.driver.close()
            print("Neo4j connection closed.")

    def execute_query(self, query: str, params: dict = None):
        """Execute Cypher query and return results"""
        if not self.driver:
            print("Database not connected")
            return None

        with self.driver.session() as session:
            try:
                result = session.run(query, params)
                return [record for record in result]
            except Exception as e:
                print(f"Query failed: {e}")
                return None

    def init_schema(self):
        """Initialize database schema with constraints and indexes"""
        queries = [
            "CREATE CONSTRAINT IF NOT EXISTS FOR (s:Stock) REQUIRE s.symbol IS UNIQUE;",
            "CREATE CONSTRAINT IF NOT EXISTS FOR (p:Pattern) REQUIRE p.name IS UNIQUE;",
            "CREATE INDEX IF NOT EXISTS FOR (s:Stock) ON (s.name);",
            "CREATE INDEX IF NOT EXISTS FOR (p:Pattern) ON (p.pattern_type);"
        ]

        for query in queries:
            self.execute_query(query)
        print("Database schema initialized.")

    def clear_database(self):
        """Clear all data from the database"""
        self.execute_query("MATCH (n) DETACH DELETE n;")
        print("Database cleared.")

    def save_pattern(self, pattern_data: dict, stock_symbol: str, timeframe: str):
        """
        Save detected pattern to knowledge graph
        - Creates Stock node if not exists
        - Creates Pattern node
        - Creates DETECTED_AT relationship
        """
        query = """
        MERGE (s:Stock {symbol: $stock_symbol})
        ON CREATE SET s.name = $company_name, s.market = $market
        MERGE (p:Pattern {name: $pattern_name})
        ON CREATE SET p.pattern_type = $pattern_type, p.confidence = $confidence
        MERGE (p)-[r:DETECTED_AT {timeframe: $timeframe, start: $start_date, end: $end_date}]->(s)
        RETURN s, p, r
        """
        params = {
            "stock_symbol": stock_symbol,
            "company_name": pattern_data.get("company_name", ""),
            "market": "A-share",
            "pattern_name": pattern_data["pattern_name"],
            "pattern_type": pattern_data["pattern_type"],
            "confidence": pattern_data["confidence"],
            "timeframe": timeframe,
            "start_date": pattern_data["start_date"],
            "end_date": pattern_data["end_date"]
        }
        self.execute_query(query, params)
        print(f"Saved pattern {pattern_data['pattern_name']} for {stock_symbol}")

    def save_backtest_result(self, backtest_data: dict, pattern_name: str, pattern_start_date: str, pattern_end_date: str, stock_symbol: str, timeframe: str):
        """
        Save backtest results to knowledge graph and link to the corresponding pattern.
        """
        query = """
        MATCH (s:Stock {symbol: $stock_symbol})<-[r:DETECTED_AT {timeframe: $timeframe, start: $pattern_start_date, end: $pattern_end_date}]-(p:Pattern {name: $pattern_name})
        MERGE (br:BacktestResult {
            timestamp: datetime(),
            final_net_worth: $final_net_worth,
            profit_loss: $profit_loss,
            profit_factor: $profit_factor,
            max_drawdown: $max_drawdown,
            episode_length: $episode_length
        })
        MERGE (p)-[:HAS_BACKTEST_RESULT]->(br)
        RETURN p, br
        """
        params = {
            "stock_symbol": stock_symbol,
            "pattern_name": pattern_name,
            "pattern_start_date": pattern_start_date,
            "pattern_end_date": pattern_end_date,
            "timeframe": timeframe,
            "final_net_worth": backtest_data.get("final_net_worth"),
            "profit_loss": backtest_data.get("profit_loss"),
            "profit_factor": backtest_data.get("profit_factor"),
            "max_drawdown": backtest_data.get("max_drawdown"),
            "episode_length": backtest_data.get("episode_length")
        }
        self.execute_query(query, params)
        print(f"Saved backtest result for pattern {pattern_name} of {stock_symbol}")

    def get_recent_patterns(self, stock_symbol: str, start_date: str, end_date: str) -> list:
        """
        Retrieves recent patterns for a given stock and date range to serve as a cache.
        """
        try:
            query = """
            MATCH (s:Stock {symbol: $stock_symbol})<-[:DETECTED_AT]-(p:Pattern)
            WHERE p.start_date >= $start_date AND p.end_date <= $end_date
            RETURN p
            ORDER BY p.end_date DESC
            LIMIT 10
            """
            
            # Convert string dates to datetime objects for comparison
            start_dt = datetime.strptime(start_date, '%Y%m%d').strftime('%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y%m%d').strftime('%Y-%m-%d')

            result = self.execute_query(query, {
                "stock_symbol": stock_symbol,
                "start_date": start_dt,
                "end_date": end_dt
            })
            
            patterns = [record['p'] for record in result]
            return patterns
        except Exception as e:
            print(f"Error getting recent patterns: {e}")
            return []

if __name__ == '__main__':
    # Test database connection
    db = Neo4jDB()
    if db.driver:
        print("\n--- Testing Database Connection ---")
        # Clear database for a clean test
        db.clear_database()
        
        # Test saving a pattern
        test_pattern = {
            "pattern_name": "Test Impulse Wave",
            "pattern_type": "impulsive",
            "confidence": 0.85,
            "start_date": "2023-01-01",
            "end_date": "2023-01-10",
            "company_name": "Test Inc."
        }
        db.save_pattern(test_pattern, "TEST001", "daily")
        
        # Test saving a backtest result for the pattern
        test_backtest_data = {
            "final_net_worth": 1200.0,
            "profit_loss": 200.0,
            "profit_factor": 1.2,
            "max_drawdown": 0.05,
            "episode_length": 100
        }
        db.save_backtest_result(test_backtest_data, "Test Impulse Wave", "2023-01-01", "2023-01-10", "TEST001", "daily")

        # Verify data was saved
        results = db.execute_query("MATCH (p:Pattern)-[r:DETECTED_AT]->(s:Stock) OPTIONAL MATCH (p)-[:HAS_BACKTEST_RESULT]->(br:BacktestResult) RETURN p, r, s, br")
        print("\n--- Verification Query Results ---")
        for record in results:
            print(record)
        
        print("\n--- Test Complete ---")
        db.close()
