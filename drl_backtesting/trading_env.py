import gymnasium as gym
from gymnasium import spaces
import numpy as np
import pandas as pd
from typing import Dict, List, Any

class TradingEnv(gym.Env):
    """
    A custom stock trading environment for Gymnasium, now with Elliott Wave pattern features.
    """
    metadata = {'render_modes': ['human'], 'render_fps': 30}

    def __init__(self, df: pd.DataFrame, elliott_wave_hypotheses: Dict[str, List[Dict[str, Any]]], initial_balance=10000, lookback_window=20, trade_fee_pct=0.001):
        super().__init__()
        self.df = df.reset_index(drop=True)
        self.elliott_wave_hypotheses = elliott_wave_hypotheses
        self.initial_balance = initial_balance
        self.lookback_window = lookback_window
        self.current_step = lookback_window
        self.balance = initial_balance
        self.shares_held = 0
        self.net_worth = initial_balance
        self.max_net_worth = initial_balance
        self.episode_history = []
        self.trade_fee_pct = trade_fee_pct

        self.action_space = spaces.Discrete(3) # 0:hold, 1:buy, 2:sell

        self.feature_columns = [col for col in df.columns if col not in ['date', 'code']]
        self.num_features = len(self.feature_columns)
        
        # Define a mapping for pattern types to numerical values
        self.pattern_type_map = {
            "impulsive_bullish": 1, "impulsive_bearish": 2,
            "corrective_bearish": 3, "corrective_flat_bullish": 4, "corrective_flat_bearish": 5,
            "corrective_triangle": 6, "corrective_wxy_bullish": 7, "corrective_wxy_bearish": 8,
            "corrective_wxyxz_bullish": 9, "corrective_wxyxz_bearish": 10,
            "corrective_double_zigzag_bullish": 11, "corrective_double_zigzag_bearish": 12,
            "corrective_triple_zigzag_bullish": 13, "corrective_triple_zigzag_bearish": 14,
            "none": 0 # For when no pattern is active
        }

        # Observation space: (lookback_window, num_features) for price/volume + fixed Elliott Wave features
        # Elliott Wave features: [top_impulse_conf, top_corrective_conf, top_impulse_type, top_corrective_type, remaining_duration,
        #                       top_impulse_duration, top_corrective_duration, top_impulse_completion, top_corrective_completion]
        self.elliott_wave_feature_count = 9
        total_observation_features = self.num_features + self.elliott_wave_feature_count

        self.observation_space = spaces.Box(low=-np.inf, high=np.inf,
                                            shape=(self.lookback_window, total_observation_features), dtype=np.float32)

    def _get_elliott_wave_features(self, current_date: pd.Timestamp) -> np.ndarray:
        top_impulse_conf = 0.0
        top_corrective_conf = 0.0
        top_impulse_type = self.pattern_type_map["none"]
        top_corrective_type = self.pattern_type_map["none"]
        remaining_duration = 0.0 # in days
        top_impulse_duration = 0.0
        top_corrective_duration = 0.0
        top_impulse_completion = 0.0
        top_corrective_completion = 0.0

        active_patterns = []
        for p_type, patterns in self.elliott_wave_hypotheses.items():
            for p in patterns:
                start_date = pd.to_datetime(p['start_date'])
                end_date = pd.to_datetime(p['end_date'])
                
                if start_date <= current_date <= end_date:
                    active_patterns.append(p)
        
        # Sort active patterns by confidence to get the top ones
        active_patterns.sort(key=lambda x: x['confidence'], reverse=True)

        if active_patterns:
            # Get top impulse and corrective patterns
            top_impulse_pattern = next((p for p in active_patterns if "impulsive" in p['pattern_type']), None)
            top_corrective_pattern = next((p for p in active_patterns if "corrective" in p['pattern_type']), None)

            if top_impulse_pattern:
                top_impulse_conf = top_impulse_pattern['confidence']
                top_impulse_type = self.pattern_type_map.get(top_impulse_pattern['pattern_type'], self.pattern_type_map["none"])
                top_impulse_duration = (pd.to_datetime(top_impulse_pattern['end_date']) - pd.to_datetime(top_impulse_pattern['start_date'])).days
                top_impulse_completion = (current_date - pd.to_datetime(top_impulse_pattern['start_date'])).days / top_impulse_duration if top_impulse_duration > 0 else 0
            
            if top_corrective_pattern:
                top_corrective_conf = top_corrective_pattern['confidence']
                top_corrective_type = self.pattern_type_map.get(top_corrective_pattern['pattern_type'], self.pattern_type_map["none"])
                top_corrective_duration = (pd.to_datetime(top_corrective_pattern['end_date']) - pd.to_datetime(top_corrective_pattern['start_date'])).days
                top_corrective_completion = (current_date - pd.to_datetime(top_corrective_pattern['start_date'])).days / top_corrective_duration if top_corrective_duration > 0 else 0

            # Remaining duration of the overall highest confidence pattern
            overall_top_pattern = active_patterns[0]
            overall_end_date = pd.to_datetime(overall_top_pattern['end_date'])
            remaining_duration = (overall_end_date - current_date).days

        return np.array([top_impulse_conf, top_corrective_conf, top_impulse_type, top_corrective_type, remaining_duration,
                         top_impulse_duration, top_corrective_duration, top_impulse_completion, top_corrective_completion], dtype=np.float32)

    def _get_observation(self):
        # Get the data for the current lookback window
        start_index = self.current_step - self.lookback_window
        end_index = self.current_step
        
        # Price/Volume features
        obs_data = self.df.loc[start_index:end_index-1, self.feature_columns].values

        # Elliott Wave features for each step in the lookback window
        elliott_wave_features_window = []
        for i in range(start_index, end_index):
            current_date_for_ew = self.df.loc[i, 'date'] # Assuming 'date' column is available in df
            ew_features = self._get_elliott_wave_features(current_date_for_ew)
            elliott_wave_features_window.append(ew_features)
        
        # Concatenate price/volume features with Elliott Wave features
        # Reshape elliott_wave_features_window to match obs_data's first dimension
        ew_features_array = np.array(elliott_wave_features_window).reshape(self.lookback_window, self.elliott_wave_feature_count)
        
        # Concatenate along the last axis (features axis)
        final_observation = np.concatenate((obs_data, ew_features_array), axis=1)

        return final_observation.astype(np.float32)

    def _calculate_reward(self):
        # Reward is based on the change in net worth, with risk adjustments
        current_net_worth = self.net_worth
        previous_net_worth = self.episode_history[-1]['net_worth'] if self.episode_history else self.initial_balance
        
        daily_return = (current_net_worth - previous_net_worth) / previous_net_worth if previous_net_worth != 0 else 0
        
        # Base reward for positive returns
        reward = daily_return * 100 # Scale return to make it more significant

        # Penalize large drawdowns (Sharpe-like ratio component)
        # Calculate daily volatility (simple standard deviation of returns over lookback window)
        if len(self.episode_history) > self.lookback_window:
            recent_returns = [h['net_worth'] / (h['net_worth'] - h['reward']/0.01) - 1 for h in self.episode_history[-self.lookback_window:]]
            volatility = np.std(recent_returns) if len(recent_returns) > 1 else 0
            if volatility > 0:
                sharpe_ratio_component = daily_return / volatility
                reward += sharpe_ratio_component * 0.5 # Reward higher risk-adjusted returns
            else:
                reward += 0.1 # Small reward for stable periods

        # Penalize being stuck (no significant change in net worth)
        if abs(daily_return) < 0.0001: # If return is near zero
            reward -= 0.05 # Small penalty for inaction

        # Encourage longer episodes (if not losing money)
        if daily_return > 0: 
            reward += 0.001
        
        return reward

    def step(self, action):
        self.current_step += 1

        if self.current_step >= len(self.df):
            # Episode ends if we run out of data
            terminated = True
            truncated = False
            reward = self._calculate_reward() # Final reward
            return self._get_observation(), reward, terminated, truncated, {}

        current_price = self.df.loc[self.current_step - 1, 'close']
        previous_net_worth = self.net_worth

        if action == 1: # Buy
            if self.balance > current_price: # Can afford to buy at least one share
                num_shares_to_buy = int(self.balance / current_price)
                cost = num_shares_to_buy * current_price
                self.shares_held += num_shares_to_buy
                self.balance -= cost * (1 + self.trade_fee_pct) # Apply trade fee
                # print(f"Step {self.current_step}: Bought {num_shares_to_buy} shares at {current_price:.2f}")
        elif action == 2: # Sell
            if self.shares_held > 0:
                self.balance += self.shares_held * current_price * (1 - self.trade_fee_pct) # Apply trade fee
                # print(f"Step {self.current_step}: Sold {self.shares_held} shares at {current_price:.2f}")
                self.shares_held = 0
        # Action 0: Hold (do nothing)

        self.net_worth = self.balance + self.shares_held * current_price
        self.max_net_worth = max(self.max_net_worth, self.net_worth)
        
        reward = self._calculate_reward()

        terminated = False
        truncated = False # No truncation for now

        # Store episode history for analysis/rendering
        self.episode_history.append({
            'step': self.current_step,
            'balance': self.balance,
            'shares_held': self.shares_held,
            'net_worth': self.net_worth,
            'price': current_price,
            'action': action,
            'reward': reward
        })

        return self._get_observation(), reward, terminated, truncated, {}

    def reset(self, seed=None, options=None):
        super().reset(seed=seed)
        self.current_step = self.lookback_window
        self.balance = self.initial_balance
        self.shares_held = 0
        self.net_worth = self.initial_balance
        self.max_net_worth = self.initial_balance
        self.episode_history = []
        
        observation = self._get_observation()
        info = {}
        return observation, info

    def render(self):
        # Implement rendering if needed (e.g., plot net worth, trades)
        pass

    def close(self):
        pass

if __name__ == '__main__':
    # Create dummy data for testing the environment
    data = {
        'date': pd.to_datetime(pd.date_range(start='2023-01-01', periods=100)),
        'open': np.random.rand(100) * 100 + 50,
        'high': np.random.rand(100) * 100 + 50,
        'low': np.random.rand(100) * 100 + 50,
        'close': np.random.rand(100) * 100 + 50,
        'volume': np.random.rand(100) * 10000 + 1000
    }
    dummy_df = pd.DataFrame(data)

    # Dummy Elliott Wave Hypotheses
    dummy_ew_hypotheses = {
        "impulsive_bullish": [
            {"pattern_name": "Impulse 1", "pattern_type": "impulsive_bullish", "start_date": "2023-01-05", "end_date": "2023-01-15", "confidence": 0.9},
            {"pattern_name": "Impulse 2", "pattern_type": "impulsive_bullish", "start_date": "2023-01-20", "end_date": "2023-01-30", "confidence": 0.8}
        ],
        "corrective_bearish": [
            {"pattern_name": "Corrective 1", "pattern_type": "corrective_bearish", "start_date": "2023-01-16", "end_date": "2023-01-19", "confidence": 0.7}
        ]
    }

    env = TradingEnv(dummy_df, dummy_ew_hypotheses)

    obs, info = env.reset()
    print(f"Initial Observation Shape: {obs.shape}")

    done = False
    total_reward = 0
    while not done:
        action = env.action_space.sample() # Random action
        obs, reward, terminated, truncated, info = env.step(action)
        total_reward += reward
        done = terminated or truncated

    print(f"Episode finished. Total Reward: {total_reward:.2f}")
    print(f"Final Net Worth: {env.net_worth:.2f}")
    print(f"Max Net Worth: {env.max_net_worth:.2f}")
    print(f"Episode History Length: {len(env.episode_history)}")