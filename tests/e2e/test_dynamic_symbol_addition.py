#!/usr/bin/env python3
"""
端到端测试套件：动态股票符号添加和实时数据处理验证
验证从前端添加新股票到艾略特波浪分析完整流程
"""

import pytest
import time
import json
import pandas as pd
from datetime import datetime, timedelta
import requests
import subprocess
import os
import sys
from pathlib import Path
import threading
import queue
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from web_interface.app import generate_demo_data
from realtime_data.data_manager import DataManager
from elliott_wave.elliott_wave_analyzer import ElliottWaveAnalyzer
from agents.coordinator import CoordinatorAgent
from web_interface.observability import observability_manager

class TestDynamicSymbolAddition:
    """端到端测试类：动态股票符号添加"""
    
    @pytest.fixture(scope="class")
    def test_config(self):
        """测试配置"""
        return {
            "test_symbols": ["AAPL", "GOOGL", "TSLA", "MSFT", "BTC-USD"],
            "markets": ["美股", "加密货币"],
            "timeframes": ["daily", "1h"],
            "timeout": 30,
            "base_url": "http://localhost:8501"
        }
    
    @pytest.fixture(scope="class")
    def data_manager(self):
        """数据管理器实例"""
        return DataManager()
    
    @pytest.fixture(scope="class")
    def coordinator(self):
        """协调器代理实例"""
        return CoordinatorAgent()
    
    @pytest.fixture(scope="class")
    def wave_analyzer(self):
        """艾略特波浪分析器实例"""
        return ElliottWaveAnalyzer()
    
    def test_01_initial_system_state(self, test_config):
        """测试1：验证系统初始状态"""
        print("🧪 测试1：验证系统初始状态")
        
        # 验证可观测性管理器已初始化
        assert observability_manager is not None
        assert hasattr(observability_manager, 'log_user_action')
        assert hasattr(observability_manager, 'log_performance_metric')
        
        # 验证数据管理器可用
        dm = DataManager()
        assert dm is not None
        assert hasattr(dm, 'get_stock_data')
        assert hasattr(dm, 'get_crypto_data')
        
        print("✅ 系统初始状态验证通过")
    
    def test_02_demo_data_generation(self, test_config):
        """测试2：验证演示数据生成"""
        print("🧪 测试2：验证演示数据生成")
        
        for symbol in test_config["test_symbols"]:
            # 生成演示数据
            data = generate_demo_data(symbol, days=30)
            
            # 验证数据结构
            assert isinstance(data, pd.DataFrame)
            assert len(data) > 0
            assert all(col in data.columns for col in ['open', 'high', 'low', 'close', 'volume'])
            
            # 验证数据质量
            assert data['close'].notna().all()
            assert data['high'] >= data['low']
            assert data['high'] >= data['close']
            assert data['low'] <= data['close']
            
            print(f"✅ {symbol} 演示数据验证通过")
    
    def test_03_realtime_data_ingestion(self, data_manager, test_config):
        """测试3：验证实时数据摄取"""
        print("🧪 测试3：验证实时数据摄取")
        
        for symbol in test_config["test_symbols"][:3]:  # 测试前3个股票
            for market in test_config["markets"]:
                if "BTC" in symbol and market != "加密货币":
                    continue
                    
                print(f"📊 测试 {symbol} ({market}) 数据摄取...")
                
                # 获取实时数据
                start_time = time.time()
                data = data_manager.get_data(symbol, market, "daily")
                ingestion_time = time.time() - start_time
                
                # 验证数据摄取性能
                assert ingestion_time < test_config["timeout"]
                assert data is not None
                assert len(data) > 20  # 至少20天数据
                
                # 验证数据完整性
                assert isinstance(data, pd.DataFrame)
                assert data.index.is_monotonic_increasing
                
                print(f"✅ {symbol} 数据摄取成功 ({ingestion_time:.2f}s)")
    
    def test_04_elliott_wave_analysis_engine(self, wave_analyzer, test_config):
        """测试4：验证艾略特波浪分析引擎"""
        print("🧪 测试4：验证艾略特波浪分析引擎")
        
        for symbol in test_config["test_symbols"][:2]:  # 测试前2个股票
            # 获取测试数据
            data = generate_demo_data(symbol, days=90)
            
            # 执行波浪分析
            start_time = time.time()
            analysis_result = wave_analyzer.analyze(data)
            analysis_time = time.time() - start_time
            
            # 验证分析结果
            assert analysis_result is not None
            assert 'patterns' in analysis_result
            assert 'confidence' in analysis_result
            assert 'wave_counts' in analysis_result
            
            # 验证分析性能
            assert analysis_time < 10  # 分析应在10秒内完成
            
            # 验证模式识别
            patterns = analysis_result['patterns']
            if patterns:
                assert isinstance(patterns, list)
                assert len(patterns) > 0
                
                for pattern in patterns:
                    assert 'type' in pattern
                    assert 'confidence' in pattern
                    assert 0 <= pattern['confidence'] <= 1
            
            print(f"✅ {symbol} 波浪分析完成 ({analysis_time:.2f}s)")
    
    def test_05_coordinator_agent_workflow(self, coordinator, test_config):
        """测试5：验证协调器代理工作流程"""
        print("🧪 测试5：验证协调器代理工作流程")
        
        test_symbol = "AAPL"
        
        # 模拟用户添加新股票
        user_request = {
            "action": "analyze_new_symbol",
            "symbol": test_symbol,
            "market": "美股",
            "timeframe": "daily"
        }
        
        # 记录开始时间
        start_time = time.time()
        
        # 执行协调器工作流程
        result = coordinator.process_request(user_request)
        
        # 计算总处理时间
        total_time = time.time() - start_time
        
        # 验证结果
        assert result is not None
        assert 'status' in result
        assert result['status'] in ['success', 'partial_success']
        
        # 验证处理时间
        assert total_time < 30  # 整个流程应在30秒内完成
        
        # 验证返回数据结构
        if result['status'] == 'success':
            assert 'analysis' in result
            assert 'recommendations' in result
            assert 'report' in result
        
        print(f"✅ 协调器代理工作流程验证通过 ({total_time:.2f}s)")
    
    def test_06_session_state_management(self):
        """测试6：验证会话状态管理"""
        print("🧪 测试6：验证会话状态管理")
        
        # 模拟Streamlit会话状态
        class MockSessionState:
            def __init__(self):
                self.current_symbol = None
                self.current_market = None
                self.current_timeframe = None
                self.data_cache = {}
                self.last_analysis = None
        
        session_state = MockSessionState()
        
        # 测试状态更新
        new_symbol = "TSLA"
        new_market = "美股"
        new_timeframe = "1h"
        
        session_state.current_symbol = new_symbol
        session_state.current_market = new_market
        session_state.current_timeframe = new_timeframe
        
        # 验证状态更新
        assert session_state.current_symbol == new_symbol
        assert session_state.current_market == new_market
        assert session_state.current_timeframe == new_timeframe
        
        # 测试缓存机制
        test_data = generate_demo_data(new_symbol)
        session_state.data_cache[f"{new_symbol}_{new_market}_{new_timeframe}"] = test_data
        
        # 验证缓存
        cache_key = f"{new_symbol}_{new_market}_{new_timeframe}"
        assert cache_key in session_state.data_cache
        assert len(session_state.data_cache[cache_key]) > 0
        
        print("✅ 会话状态管理验证通过")
    
    def test_07_error_handling_and_recovery(self, data_manager):
        """测试7：验证错误处理和恢复机制"""
        print("🧪 测试7：验证错误处理和恢复机制")
        
        # 测试无效股票代码
        invalid_symbols = ["INVALID123", "XYZ789", ""]
        
        for invalid_symbol in invalid_symbols:
            try:
                data = data_manager.get_data(invalid_symbol, "美股", "daily")
                # 应该返回None或抛出异常
                assert data is None or len(data) == 0
            except Exception as e:
                # 验证错误被正确处理
                assert isinstance(e, (ValueError, KeyError, ConnectionError))
        
        # 测试网络错误恢复
        # 模拟网络中断
        original_timeout = data_manager.timeout
        data_manager.timeout = 0.001  # 设置极短超时
        
        try:
            data = data_manager.get_data("AAPL", "美股", "daily")
            # 应该触发超时处理
        except Exception as e:
            assert "timeout" in str(e).lower() or "connection" in str(e).lower()
        
        # 恢复设置
        data_manager.timeout = original_timeout
        
        print("✅ 错误处理和恢复机制验证通过")
    
    def test_08_performance_benchmarks(self, test_config):
        """测试8：性能基准测试"""
        print("🧪 测试8：性能基准测试")
        
        performance_metrics = {
            "data_ingestion": [],
            "wave_analysis": [],
            "coordinator_processing": []
        }
        
        # 数据摄取性能测试
        dm = DataManager()
        for symbol in test_config["test_symbols"][:3]:
            start_time = time.time()
            data = dm.get_data(symbol, "美股", "daily")
            ingestion_time = time.time() - start_time
            performance_metrics["data_ingestion"].append(ingestion_time)
        
        # 波浪分析性能测试
        wa = ElliottWaveAnalyzer()
        for symbol in test_config["test_symbols"][:2]:
            data = generate_demo_data(symbol, days=60)
            start_time = time.time()
            result = wa.analyze(data)
            analysis_time = time.time() - start_time
            performance_metrics["wave_analysis"].append(analysis_time)
        
        # 验证性能指标
        avg_ingestion = sum(performance_metrics["data_ingestion"]) / len(performance_metrics["data_ingestion"])
        avg_analysis = sum(performance_metrics["wave_analysis"]) / len(performance_metrics["wave_analysis"])
        
        assert avg_ingestion < 5  # 数据摄取平均<5秒
        assert avg_analysis < 10  # 波浪分析平均<10秒
        
        print(f"✅ 性能基准测试通过")
        print(f"   数据摄取平均: {avg_ingestion:.2f}s")
        print(f"   波浪分析平均: {avg_analysis:.2f}s")