#!/usr/bin/env python3
"""
端到端测试运行器
执行完整的动态股票添加和实时数据处理验证
"""

import pytest
import sys
import os
from pathlib import Path
import subprocess
import time
import requests
import signal
import threading
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

class E2ETestRunner:
    """端到端测试运行器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.streamlit_process = None
        self.test_results = {}
        
    def setup_environment(self):
        """设置测试环境"""
        print("🔧 设置测试环境...")
        
        # 确保测试依赖已安装
        requirements_file = self.project_root / "tests" / "test_requirements.txt"
        if requirements_file.exists():
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
            ], check=True)
        
        # 启动Streamlit应用
        self.start_streamlit_app()
        
        # 等待应用启动
        time.sleep(5)
        
        print("✅ 测试环境设置完成")
    
    def start_streamlit_app(self):
        """启动Streamlit应用"""
        print("🚀 启动Streamlit应用...")
        
        app_path = self.project_root / "web_interface" / "app.py"
        
        self.streamlit_process = subprocess.Popen([
            sys.executable, "-m", "streamlit", "run", str(app_path),
            "--server.port=8501",
            "--server.address=localhost",
            "--server.headless=true"
        ], cwd=str(self.project_root))
        
        # 等待应用启动
        max_wait = 30
        for i in range(max_wait):
            try:
                response = requests.get("http://localhost:8501", timeout=1)
                if response.status_code == 200:
                    print("✅ Streamlit应用启动成功")
                    break
            except:
                time.sleep(1)
        else:
            raise RuntimeError("Streamlit应用启动失败")
    
    def run_e2e_tests(self):
        """运行端到端测试"""
        print("\n🧪 开始端到端测试...")
        
        test_modules = [
            "tests.e2e.test_dynamic_symbol_addition",
            "tests.e2e.test_web_interface_integration"
        ]
        
        for test_module in test_modules:
            print(f"\n📊 运行 {test_module}...")
            
            # 运行pytest
            result = subprocess.run([
                sys.executable, "-m", "pytest", test_module, "-v", "--tb=short"
            ], capture_output=True, text=True, cwd=str(self.project_root))
            
            self.test_results[test_module] = {
                "returncode": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr
            }
            
            if result.returncode == 0:
                print(f"✅ {test_module} 测试通过")
            else:
                print(f"❌ {test_module} 测试失败")
                print(result.stdout)
                print(result.stderr)
    
    def run_performance_tests(self):
        """运行性能测试"""
        print("\n⚡ 运行性能测试...")
        
        # 运行性能基准测试
        perf_test_script = """
import sys
sys.path.append('tests/e2e')
from test_dynamic_symbol_addition import TestDynamicSymbolAddition

test = TestDynamicSymbolAddition()
test.test_08_performance_benchmarks({'test_symbols': ['AAPL', 'GOOGL', 'TSLA']})
"""
        
        result = subprocess.run([
            sys.executable, "-c", perf_test_script
        ], capture_output=True, text=True, cwd=str(self.project_root))
        
        if result.returncode == 0:
            print("✅ 性能测试通过")
        else:
            print("❌ 性能测试失败")
            print(result.stderr)
    
    def test_real_time_data_flow(self):
        """测试实时数据流"""
        print("\n📊 测试实时数据流...")
        
        # 测试数据获取
        test_script = """
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from realtime_data.data_manager import DataManager
import time

dm = DataManager()
symbols = [("AAPL", "美股"), ("BTC-USD", "加密货币")]

for symbol, market in symbols:
    start_time = time.time()
    data = dm.get_data(symbol, market, "daily")
    duration = time.time() - start_time
    
    if data is not None and len(data) > 0:
        print(f"✅ {symbol} 数据获取成功 ({duration:.2f}s, {len(data)}条记录)")
    else:
        print(f"❌ {symbol} 数据获取失败")
"""
        
        result = subprocess.run([
            sys.executable, "-c", test_script
        ], capture_output=True, text=True, cwd=str(self.project_root))
        
        print(result.stdout)
        if result.stderr:
            print(result.stderr)
    
    def test_wave_analysis_pipeline(self):
        """测试波浪分析管道"""
        print("\n🌊 测试波浪分析管道...")
        
        test_script = """
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from elliott_wave.elliott_wave_analyzer import ElliottWaveAnalyzer
from web_interface.app import generate_demo_data
import time

analyzer = ElliottWaveAnalyzer()
symbols = ["AAPL", "TSLA"]

for symbol in symbols:
    # 生成测试数据
    data = generate_demo_data(symbol, days=60)
    
    # 执行分析
    start_time = time.time()
    result = analyzer.analyze(data)
    duration = time.time() - start_time
    
    if result and 'patterns' in result:
        pattern_count = len(result['patterns'])
        print(f"✅ {symbol} 波浪分析完成 ({duration:.2f}s, {pattern_count}个模式)")
    else:
        print(f"❌ {symbol} 波浪分析失败")
"""
        
        result = subprocess.run([
            sys.executable, "-c", test_script
        ], capture_output=True, text=True, cwd=str(self.project_root))
        
        print(result.stdout)
        if result.stderr:
            print(result.stderr)
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n📋 生成测试报告...")
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "test_results": self.test_results,
            "summary": {
                "total_tests": len(self.test_results),
                "passed": sum(1 for r in self.test_results.values() if r["returncode"] == 0),
                "failed": sum(1 for r in self.test_results.values() if r["returncode"] != 0)
            }
        }
        
        # 保存测试报告
        report_file = self.project_root / "tests" / "e2e" / "test_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 测试报告已保存: {report_file}")
        return report
    
    def cleanup(self):
        """清理测试环境"""
        print("\n🧹 清理测试环境...")
        
        if self.streamlit_process:
            self.streamlit_process.terminate()
            self.streamlit_process.wait()
            print("✅ Streamlit应用已关闭")
    
    def run_all_tests(self):
        """运行所有测试"""
        try:
            print("🎯 ElliottAgents 端到端测试开始")
            print("=" * 50)
            
            self.setup_environment()
            self.run_e2e_tests()
            self.run_performance_tests()
            self.test_real_time_data_flow()
            self.test_wave_analysis_pipeline()
            
            report = self.generate_test_report()
            
            print("\n" + "=" * 50)
            print("📊 测试总结")
            print(f"总测试数: {report['summary']['total_tests']}")
            print(f"通过: {report['summary']['passed']}")
            print(f"失败: {report['summary']['failed']}")
            
            if report['summary']['failed'] == 0:
                print("🎉 所有测试通过！系统运行正常")
            else:
                print("⚠️  部分测试失败，请查看详细报告")
            
            return report