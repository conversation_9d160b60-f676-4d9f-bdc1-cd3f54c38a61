"""
集成测试 - 完整工作流测试
测试从数据获取到报告生成的完整流程
"""

import pytest
import pandas as pd
import tempfile
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from data_acquisition.stock_data import StockDataFetcher
from elliott_wave.pattern_recognition import ElliottWaveAnalyzer
from rag_system.rag_enhanced_analyzer import RAGEnhancedAnalyzer
from rag_system.knowledge_base import KnowledgeBase
from llm_client.client_factory import LLMClientFactory
from web_interface.app import create_app


class TestFullWorkflow:
    """测试完整工作流"""
    
    @pytest.fixture
    def mock_stock_data(self):
        """创建模拟股票数据"""
        dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
        
        # 创建模拟的5浪上升结构
        base_price = 100
        prices = []
        for i in range(100):
            if i < 20:
                price = base_price + i * 0.5  # 浪1
            elif i < 35:
                price = base_price + 10 - (i-20) * 0.3  # 浪2
            elif i < 55:
                price = base_price + 5 + (i-35) * 1.0  # 浪3
            elif i < 65:
                price = base_price + 25 - (i-55) * 0.5  # 浪4
            else:
                price = base_price + 20 + (i-65) * 0.8  # 浪5
            
            prices.append(price)
        
        data = pd.DataFrame({
            'open': [p - 0.5 for p in prices],
            'high': [p + 1.0 for p in prices],
            'low': [p - 1.0 for p in prices],
            'close': prices,
            'volume': [1000000] * 100
        }, index=dates)
        
        return data
    
    @pytest.fixture
    def temp_knowledge_dir(self):
        """创建临时知识库"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试知识文件
            theory_file = os.path.join(temp_dir, "elliott_wave_theory.txt")
            with open(theory_file, 'w', encoding='utf-8') as f:
                f.write("""
艾略特波浪理论核心要点：
1. 市场走势呈波浪形态
2. 推动浪有5个子浪结构
3. 调整浪有3个子浪结构
4. 斐波那契比例关系重要
5. 浪3通常是最强劲的浪
""")
            
            yield temp_dir
    
    def test_data_fetching_to_analysis(self, mock_stock_data):
        """测试从数据获取到分析的完整流程"""
        # 模拟数据获取
        with patch('data_acquisition.stock_data.ak') as mock_ak:
            mock_ak.stock_zh_a_hist.return_value = mock_stock_data
            
            fetcher = StockDataFetcher()
            data = fetcher.fetch_stock_data("000001", "20240101", "20240410")
            
            assert len(data) > 0
            assert all(col in data.columns for col in ['open', 'high', 'low', 'close', 'volume'])
    
    def test_wave_analysis_workflow(self, mock_stock_data):
        """测试波浪分析工作流"""
        analyzer = ElliottWaveAnalyzer()
        result = analyzer.analyze(mock_stock_data)
        
        assert isinstance(result, dict)
        assert 'status' in result
        
        if result['status'] == 'success':
            assert 'wave_structure' in result
            assert 'key_levels' in result
            assert 'fibonacci_ratios' in result
    
    def test_rag_enhanced_analysis(self, mock_stock_data, temp_knowledge_dir):
        """测试RAG增强分析"""
        # 创建知识库
        knowledge_base = KnowledgeBase(knowledge_dir=temp_knowledge_dir)
        
        # 创建模拟LLM客户端
        mock_llm = Mock()
        mock_llm.generate_response.return_value = "基于RAG的详细波浪分析"
        mock_llm.analyze_wave_pattern.return_value = {
            "wave_type": "impulse",
            "confidence": 0.85,
            "analysis": "RAG增强分析结果"
        }
        
        # 创建RAG分析器
        rag_analyzer = RAGEnhancedAnalyzer(
            llm_client=mock_llm,
            knowledge_base=knowledge_base
        )
        
        # 执行波浪分析
        wave_analyzer = ElliottWaveAnalyzer()
        wave_result = wave_analyzer.analyze(mock_stock_data)
        
        if wave_result['status'] == 'success':
            # 使用RAG增强分析
            enhanced_result = rag_analyzer.enhance_wave_analysis(wave_result)
            
            assert 'enhanced_analysis' in enhanced_result
            assert 'knowledge_context' in enhanced_result
    
    def test_report_generation_workflow(self, mock_stock_data, temp_knowledge_dir):
        """测试报告生成工作流"""
        # 创建知识库
        knowledge_base = KnowledgeBase(knowledge_dir=temp_knowledge_dir)
        
        # 创建模拟LLM客户端
        mock_llm = Mock()
        mock_llm.generate_response.return_value = """
# 艾略特波浪分析报告
        
## 当前波浪结构
识别到推动浪结构，置信度85%

## 技术分析
- RSI: 65 (中性偏多)
- MACD: 金叉状态
- 关键支撑位: 105.50
- 关键阻力位: 112.80

## 交易建议
建议逢低买入，止损设在104.50
"""
        
        # 创建RAG分析器
        rag_analyzer = RAGEnhancedAnalyzer(
            llm_client=mock_llm,
            knowledge_base=knowledge_base
        )
        
        # 执行完整分析
        wave_analyzer = ElliottWaveAnalyzer()
        wave_result = wave_analyzer.analyze(mock_stock_data)
        
        if wave_result['status'] == 'success':
            # 生成上下文感知报告
            report = rag_analyzer.generate_context_aware_report(wave_result)
            
            assert isinstance(report, str)
            assert len(report) > 0
            assert "艾略特波浪" in report
    
    def test_error_handling_workflow(self):
        """测试错误处理工作流"""
        # 测试空数据
        empty_data = pd.DataFrame()
        analyzer = ElliottWaveAnalyzer()
        result = analyzer.analyze(empty_data)
        
        assert 'error' in result or result['status'] == 'no_waves_found'
    
    def test_realtime_data_integration(self):
        """测试实时数据集成"""
        # 模拟实时数据
        mock_realtime_data = pd.DataFrame({
            'open': [100, 101, 102],
            'high': [101, 102, 103],
            'low': [99, 100, 101],
            'close': [100.5, 101.5, 102.5],
            'volume': [1000000, 1100000, 1200000]
        })
        
        analyzer = ElliottWaveAnalyzer()
        result = analyzer.analyze(mock_realtime_data)
        
        # 实时数据可能不足以识别完整波浪，但不应崩溃
        assert isinstance(result, dict)
    
    def test_multi_timeframe_analysis(self, mock_stock_data):
        """测试多时间框架分析"""
        # 创建不同时间框架的数据
        daily_data = mock_stock_data
        
        # 创建周线数据
        weekly_data = daily_data.resample('W').agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum'
        }).dropna()
        
        analyzer = ElliottWaveAnalyzer()
        
        # 分析日线
        daily_result = analyzer.analyze(daily_data)
        
        # 分析周线
        weekly_result = analyzer.analyze(weekly_data)
        
        assert isinstance(daily_result, dict)
        assert isinstance(weekly_result, dict)
    
    def test_web_interface_integration(self, mock_stock_data):
        """测试Web界面集成"""
        # 创建模拟应用
        app = create_app()
        
        # 测试应用创建
        assert app is not None
        
        # 测试配置
        assert hasattr(app, 'config')
    
    def test_performance_benchmark(self, mock_stock_data):
        """测试性能基准"""
        import time
        
        analyzer = ElliottWaveAnalyzer()
        
        # 测试分析时间
        start_time = time.time()
        result = analyzer.analyze(mock_stock_data)
        end_time = time.time()
        
        analysis_time = end_time - start_time
        
        # 分析时间应小于5秒
        assert analysis_time < 5.0
        
        # 结果应包含必要字段
        assert isinstance(result, dict)
        assert 'status' in result


class TestEndToEndScenarios:
    """测试端到端场景"""
    
    def test_bullish_impulse_wave_scenario(self):
        """测试牛市推动浪场景"""
        # 创建典型的牛市推动浪数据
        dates = pd.date_range(start='2024-01-01', periods=50, freq='D')
        
        # 5浪上升结构
        prices = []
        for i in range(50):
            if i < 10:
                price = 100 + i * 0.5  # 浪1
            elif i < 15:
                price = 105 - (i-10) * 0.3  # 浪2
            elif i < 25:
                price = 103.5 + (i-15) * 1.2  # 浪3
            elif i < 30:
                price = 115.5 - (i-25) * 0.8  # 浪4
            else:
                price = 111.5 + (i-30) * 0.7  # 浪5
            
            prices.append(price)
        
        data = pd.DataFrame({
            'open': [p - 0.5 for p in prices],
            'high': [p + 1.0 for p in prices],
            'low': [p - 1.0 for p in prices],
            'close': prices,
            'volume': [1000000] * 50
        }, index=dates)
        
        analyzer = ElliottWaveAnalyzer()
        result = analyzer.analyze(data)
        
        assert isinstance(result, dict)
        assert 'status' in result
    
    def test_bearish_corrective_wave_scenario(self):
        """测试熊市调整浪场景"""
        # 创建典型的熊市调整浪数据
        dates = pd.date_range(start='2024-01-01', periods=30, freq='D')
        
        # A-B-C调整结构
        prices = []
        for i in range(30):
            if i < 10:
                price = 120 - i * 1.0  # 浪A
            elif i < 20:
                price = 110 + (i-10) * 0.8  # 浪B
            else:
                price = 118 - (i-20) * 1.5  # 浪C
            
            prices.append(price)
        
        data = pd.DataFrame({
            'open': [p - 0.5 for p in prices],
            'high': [p + 1.0 for p in prices],
            'low': [p - 1.0 for p in prices],
            'close': prices,
            'volume': [1000000] * 30
        }, index=dates)
        
        analyzer = ElliottWaveAnalyzer()
        result = analyzer.analyze(data)
        
        assert isinstance(result, dict)
        assert 'status' in result


if __name__ == '__main__':
    pytest.main([__file__])