"""
集成测试
测试整个ElliottAgents系统的功能完整性
"""

import pytest
import asyncio
import tempfile
import os
from pathlib import Path
import pandas as pd
import numpy as np

from main import ElliottWaveAnalyzer
from agents.wave_interpreter import WaveInterpreterAgent as WaveInterpreter
from llm_client.client_factory import LLMClientFactory
from rag_system.rag_enhanced_analyzer import RAGEnhancedAnalyzer
from realtime_data.live_data_provider import LiveDataProvider
from crypto_data.crypto_provider import CryptoDataProvider
from performance.optimizer import monitor, MemoryOptimizer

class TestIntegration:
    """集成测试类"""
    
    @pytest.fixture
    def analyzer(self):
        """创建分析器实例"""
        return ElliottWaveAnalyzer()
    
    @pytest.fixture
    def sample_data(self):
        """创建测试数据"""
        dates = pd.date_range(start='2023-01-01', end='2024-01-01', freq='D')
        np.random.seed(42)
        
        # 生成模拟股票数据
        prices = 100 + np.cumsum(np.random.randn(len(dates)) * 2)
        
        return pd.DataFrame({
            'date': dates,
            'open': prices + np.random.randn(len(dates)) * 0.5,
            'high': prices + np.abs(np.random.randn(len(dates))) * 0.5 + 1,
            'low': prices - np.abs(np.random.randn(len(dates))) * 0.5 - 1,
            'close': prices,
            'volume': np.random.randint(1000000, 10000000, len(dates))
        })
    
    @pytest.mark.asyncio
    async def test_full_analysis_workflow(self, analyzer, sample_data):
        """测试完整分析工作流"""
        # 使用模拟数据进行分析
        result = await analyzer.analyze_stock("TEST", "A股", "daily")
        
        assert "symbol" in result
        assert "market" in result
        assert "wave_analysis" in result
        assert "enhanced_analysis" in result
        assert "report" in result
        
        # 验证数据完整性
        assert result["symbol"] == "TEST"
        assert result["market"] == "A股"
        assert isinstance(result["report"], str)
    
    @pytest.mark.asyncio
    async def test_portfolio_analysis(self, analyzer):
        """测试投资组合分析"""
        symbols = ["TEST1", "TEST2", "TEST3"]
        results = await analyzer.analyze_portfolio(symbols, "A股")
        
        assert "successful" in results
        assert "failed" in results
        assert results["total"] == 3
        assert len(results["successful"]) + len(results["failed"]) == 3
    
    @pytest.mark.asyncio
    async def test_crypto_analysis(self, analyzer):
        """测试加密货币分析"""
        result = await analyzer.analyze_stock("BTCUSDT", "CRYPTO", "daily")
        
        assert result["market"] == "CRYPTO"
        assert "wave_analysis" in result
    
    @pytest.mark.asyncio
    async def test_chinese_report_generation(self, analyzer, sample_data):
        """测试中文报告生成"""
        # 创建模拟分析结果
        analysis_result = {
            "symbol": "000831",
            "market": "A股",
            "wave_analysis": {"current_wave": 3, "confidence": 0.85},
            "enhanced_analysis": {"trend": "bullish", "support": 10.5, "resistance": 12.8}
        }
        
        report = await analyzer.generate_chinese_report(analysis_result)
        
        assert isinstance(report, str)
        assert len(report) > 100  # 确保报告有足够内容
        assert any(char in report for char in "波浪趋势支撑阻力")  # 包含关键中文词汇
    
    def test_performance_monitoring(self):
        """测试性能监控"""
        monitor.record_metric("test_metric", 1.5, {"test": "true"})
        
        stats = monitor.get_metric_stats("test_metric")
        assert stats["count"] >= 1
        assert "mean" in stats
    
    def test_memory_optimization(self, sample_data):
        """测试内存优化"""
        original_size = sample_data.memory_usage(deep=True).sum()
        
        optimized = MemoryOptimizer.optimize_dataframe(sample_data)
        optimized_size = optimized.memory_usage(deep=True).sum()
        
        assert optimized_size <= original_size
    
    @pytest.mark.asyncio
    async def test_error_handling(self, analyzer):
        """测试错误处理"""
        # 测试无效股票代码
        try:
            await analyzer.analyze_stock("INVALID", "INVALID", "daily")
            assert False, "应该抛出异常"
        except Exception as e:
            assert "分析失败" in str(e) or "INVALID" in str(e)
    
    @pytest.mark.asyncio
    async def test_data_integrity(self, analyzer):
        """测试数据完整性"""
        result = await analyzer.analyze_stock("TEST", "A股", "daily")
        
        # 验证数据结构
        assert isinstance(result["data"], pd.DataFrame)
        assert len(result["data"]) > 0
        assert all(col in result["data"].columns 
                  for col in ["open", "high", "low", "close", "volume"])
    
    def test_config_validation(self):
        """测试配置验证"""
        # 测试有效配置
        analyzer = ElliottWaveAnalyzer({
            "max_workers": 4,
            "cache_ttl": 3600
        })
        assert analyzer.config["max_workers"] == 4
    
    @pytest.mark.asyncio
    async def test_concurrent_analysis(self, analyzer):
        """测试并发分析"""
        symbols = ["TEST1", "TEST2", "TEST3", "TEST4", "TEST5"]
        
        start_time = asyncio.get_event_loop().time()
        results = await analyzer.analyze_portfolio(symbols, "A股")
        end_time = asyncio.get_event_loop().time()
        
        # 验证并发执行效率
        duration = end_time - start_time
        assert duration < 10  # 5个股票分析应在10秒内完成
    
    def test_file_operations(self, analyzer):
        """测试文件操作"""
        with tempfile.TemporaryDirectory() as temp_dir:
            filepath = os.path.join(temp_dir, "test_report.json")
            analyzer.export_performance_report(filepath)
            
            assert os.path.exists(filepath)
            with open(filepath, 'r') as f:
                data = f.read()
                assert len(data) > 0

@pytest.mark.asyncio
async def test_system_health():
    """测试系统健康状态"""
    # 测试所有核心组件
    components = [
        ("LLM Client", LLMClientFactory.create_client()),
        ("Wave Interpreter", WaveInterpreter()),
        ("RAG Analyzer", RAGEnhancedAnalyzer()),
        ("Live Provider", LiveDataProvider()),
        ("Crypto Provider", CryptoDataProvider())
    ]
    
    for name, component in components:
        assert component is not None, f"{name} 初始化失败"

if __name__ == "__main__":
    pytest.main([__file__, "-v"])