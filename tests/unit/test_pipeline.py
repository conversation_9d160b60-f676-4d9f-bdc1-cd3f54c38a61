import unittest
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, MinMaxScaler

from data_preprocessing.pipeline import (
    add_technical_indicators,
    create_lagged_features,
    normalize_data,
    create_sequences,
    train_val_test_split,
    preprocess_stock_data
)

class TestPipeline(unittest.TestCase):

    def setUp(self):
        # Set random seed for reproducibility
        np.random.seed(42)
        # Sample DataFrame for testing - increased periods for better statistical properties
        dates = pd.to_datetime(pd.date_range(start='2023-01-01', periods=1000))
        self.sample_df = pd.DataFrame({
            'date': dates,
            'open': np.random.rand(1000) * 100 + 100,
            'high': np.random.rand(1000) * 100 + 105,
            'low': np.random.rand(1000) * 100 + 95,
            'close': np.random.rand(1000) * 100 + 100,
            'volume': np.random.rand(1000) * 1000000 + 100000
        }).set_index('date')

    def test_add_technical_indicators(self):
        df_with_indicators = add_technical_indicators(self.sample_df.copy())
        self.assertGreater(len(df_with_indicators.columns), len(self.sample_df.columns))
        self.assertFalse(df_with_indicators.isnull().any().any())
        self.assertIn('SMA_20', df_with_indicators.columns)
        self.assertIn('RSI', df_with_indicators.columns)
        self.assertIn('MACD', df_with_indicators.columns)

    def test_create_lagged_features(self):
        df_lagged = create_lagged_features(self.sample_df.copy(), lags=3)
        self.assertGreater(len(df_lagged.columns), len(self.sample_df.columns))
        self.assertFalse(df_lagged.isnull().any().any())
        self.assertIn('close_lag_1', df_lagged.columns)
        self.assertIn('volume_lag_3', df_lagged.columns)

    def test_normalize_data_standard(self):
        normalized_df, scaler = normalize_data(self.sample_df.copy(), method='standard')
        self.assertIsInstance(scaler, StandardScaler)
        self.assertEqual(normalized_df.shape, self.sample_df.shape)
        for col in normalized_df.columns:
            self.assertAlmostEqual(normalized_df[col].mean(), 0.0, places=5)
            self.assertAlmostEqual(normalized_df[col].std(), 1.0, places=2) # Increased tolerance

    def test_normalize_data_minmax(self):
        normalized_df, scaler = normalize_data(self.sample_df.copy(), method='minmax')
        self.assertIsInstance(scaler, MinMaxScaler)
        self.assertEqual(normalized_df.shape, self.sample_df.shape)
        for col in normalized_df.columns:
            self.assertAlmostEqual(normalized_df[col].min(), -1.0, places=5)
            self.assertAlmostEqual(normalized_df[col].max(), 1.0, places=5)

    def test_normalize_data_empty_df(self):
        normalized_df, scaler = normalize_data(pd.DataFrame(), method='standard')
        self.assertTrue(normalized_df.empty)
        self.assertIsNone(scaler)

    def test_create_sequences(self):
        data = np.array([[i, i*2] for i in range(50)]) # 2 features
        seq_length = 10
        target_length = 1
        X, y = create_sequences(data, seq_length, target_length)

        self.assertEqual(X.shape[0], len(data) - seq_length - target_length + 1)
        self.assertEqual(X.shape[1], seq_length)
        self.assertEqual(X.shape[2], data.shape[1])
        self.assertEqual(y.shape[0], len(data) - seq_length - target_length + 1)
        self.assertEqual(y.shape[1], target_length)

    def test_train_val_test_split(self):
        train_df, val_df, test_df = train_val_test_split(self.sample_df.copy(), val_size=0.1, test_size=0.1)

        total_len = len(self.sample_df)
        self.assertAlmostEqual(len(train_df) + len(val_df) + len(test_df), total_len, delta=2) # Allow for rounding
        self.assertTrue(train_df.index.max() < val_df.index.min())
        self.assertTrue(val_df.index.max() < test_df.index.min())

    def test_preprocess_stock_data(self):
        # Mock stock_data dictionary
        stock_data = {
            'STOCK1': self.sample_df.copy(),
            'STOCK2': self.sample_df.iloc[:20].copy() # Smaller DF to test edge cases with rolling windows
        }
        preprocessed_data = preprocess_stock_data(stock_data)

        self.assertIn('STOCK1', preprocessed_data)
        self.assertIn('STOCK2', preprocessed_data)
        self.assertFalse(preprocessed_data['STOCK1'].empty)
        self.assertTrue(preprocessed_data['STOCK2'].empty) # Assert that STOCK2 is empty
        self.assertGreater(len(preprocessed_data['STOCK1'].columns), len(self.sample_df.columns))
        self.assertFalse(preprocessed_data['STOCK1'].isnull().any().any())

if __name__ == '__main__':
    unittest.main()
