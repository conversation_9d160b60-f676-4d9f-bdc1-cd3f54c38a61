import unittest
import pandas as pd
import numpy as np

from data_acquisition.data_quality import validate_ohlcv, detect_anomalies

class TestDataQuality(unittest.TestCase):

    def setUp(self):
        # Sample valid DataFrame - extended for better anomaly detection and rolling mean
        dates = pd.to_datetime(pd.date_range(start='2023-01-01', periods=35))
        self.valid_df = pd.DataFrame({
            'date': dates,
            'open': np.arange(100, 135),
            'high': np.arange(102, 137),
            'low': np.arange(99, 134),
            'close': np.arange(101, 136),
            'volume': np.arange(1000, 1035)
        }).set_index('date')

    def test_validate_ohlcv_valid_data(self):
        df = validate_ohlcv(self.valid_df.copy())
        pd.testing.assert_frame_equal(df, self.valid_df)

    def test_validate_ohlcv_missing_values(self):
        df_missing = self.valid_df.copy()
        df_missing.loc[pd.to_datetime('2023-01-02'), 'open'] = np.nan
        df_missing.loc[pd.to_datetime('2023-01-03'), 'volume'] = np.nan

        df = validate_ohlcv(df_missing)
        # Expected: open should be ffilled, volume should be 0 and then row removed
        expected_df = self.valid_df.copy()
        expected_df.loc[pd.to_datetime('2023-01-02'), 'open'] = 100 # ffilled from 2023-01-01
        expected_df['open'] = expected_df['open'].astype(float) # Ensure dtype matches after NaN introduction
        expected_df['volume'] = expected_df['volume'].astype(float) # Ensure dtype matches after NaN introduction
        # The row for 2023-01-03 will be removed because its volume becomes 0
        expected_df = expected_df.drop(pd.to_datetime('2023-01-03'))
        pd.testing.assert_frame_equal(df, expected_df)

    def test_validate_ohlcv_unordered_data(self):
        df_unordered = pd.DataFrame({
            'date': pd.to_datetime(['2023-01-03', '2023-01-01', '2023-01-02']),
            'open': [102, 100, 101],
            'high': [104, 102, 103],
            'low': [101, 99, 100],
            'close': [103, 101, 102],
            'volume': [1200, 1000, 1100]
        }).set_index('date')

        df = validate_ohlcv(df_unordered)
        # Recreate the expected ordered DataFrame for comparison
        expected_ordered_df = pd.DataFrame({
            'date': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03']),
            'open': [100, 101, 102],
            'high': [102, 103, 104],
            'low': [99, 100, 101],
            'close': [101, 102, 103],
            'volume': [1000, 1100, 1200]
        }).set_index('date')
        pd.testing.assert_frame_equal(df, expected_ordered_df)

    def test_validate_ohlcv_invalid_ohlc_relationships(self):
        df_invalid_ohlc = self.valid_df.copy()
        df_invalid_ohlc.loc[pd.to_datetime('2023-01-02'), 'high'] = 90 # high < low
        df_invalid_ohlc.loc[pd.to_datetime('2023-01-03'), 'low'] = 110 # low > high

        df = validate_ohlcv(df_invalid_ohlc)
        # Expected: high should be max(open, close, high), low should be min(open, close, low)
        expected_df = self.valid_df.copy()
        expected_df.loc[pd.to_datetime('2023-01-02'), 'high'] = max(101, 102, 90) # open, close, high
        expected_df.loc[pd.to_datetime('2023-01-02'), 'low'] = min(101, 102, 100) # open, close, low
        expected_df.loc[pd.to_datetime('2023-01-03'), 'high'] = max(102, 103, 104) # open, close, high
        expected_df.loc[pd.to_datetime('2023-01-03'), 'low'] = min(102, 103, 110) # Corrected: min(102, 103, 110) -> 102
        pd.testing.assert_frame_equal(df, expected_df)

    def test_validate_ohlcv_zero_volume_days(self):
        df_zero_volume = self.valid_df.copy()
        df_zero_volume.loc[pd.to_datetime('2023-01-02'), 'volume'] = 0

        df = validate_ohlcv(df_zero_volume)
        expected_df = self.valid_df.drop(pd.to_datetime('2023-01-02'))
        pd.testing.assert_frame_equal(df, expected_df)

    def test_detect_anomalies_no_anomalies(self):
        df = detect_anomalies(self.valid_df.copy())
        self.assertIn('returns', df.columns)
        self.assertIn('price_anomaly', df.columns)
        self.assertIn('vol_ma', df.columns)
        self.assertIn('volume_anomaly', df.columns)
        self.assertIn('anomaly', df.columns)
        self.assertFalse(df['anomaly'].any()) # No anomalies expected in valid_df

    def test_detect_anomalies_price_anomaly(self):
        df_price_anomaly = self.valid_df.copy()
        # Create a large return to trigger price anomaly
        df_price_anomaly.loc[self.valid_df.index[-1], 'close'] = 500 # Use last valid date

        df = detect_anomalies(df_price_anomaly)
        self.assertTrue(df.loc[self.valid_df.index[-1], 'price_anomaly'])
        self.assertTrue(df.loc[self.valid_df.index[-1], 'anomaly'])

    def test_detect_anomalies_volume_anomaly(self):
        df_volume_anomaly = self.valid_df.copy()
        # Create a large volume to trigger volume anomaly
        df_volume_anomaly.loc[self.valid_df.index[-1], 'volume'] = 1000000 # Use last valid date

        df = detect_anomalies(df_volume_anomaly)
        self.assertTrue(df.loc[self.valid_df.index[-1], 'volume_anomaly'])
        self.assertTrue(df.loc[self.valid_df.index[-1], 'anomaly'])

if __name__ == '__main__':
    unittest.main()
