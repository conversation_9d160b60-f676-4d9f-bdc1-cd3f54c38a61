"""
艾略特波浪分析器单元测试
测试ElliottWaveAnalyzer的核心功能
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from elliott_wave.pattern_recognition import ElliottWaveAnalyzer, ElliottWave, WavePoint


class TestElliottWaveAnalyzer:
    """测试ElliottWaveAnalyzer类"""
    
    @pytest.fixture
    def sample_data(self):
        """创建测试用的股票数据"""
        dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
        
        # 创建模拟的价格数据，包含明显的波浪结构
        base_price = 100
        prices = []
        for i in range(100):
            # 模拟5浪上升结构
            if i < 20:
                price = base_price + i * 0.5  # 第1浪
            elif i < 35:
                price = base_price + 10 - (i-20) * 0.3  # 第2浪调整
            elif i < 55:
                price = base_price + 5 + (i-35) * 1.0  # 第3浪
            elif i < 65:
                price = base_price + 25 - (i-55) * 0.5  # 第4浪
            else:
                price = base_price + 20 + (i-65) * 0.8  # 第5浪
            
            prices.append(price)
        
        data = pd.DataFrame({
            'open': [p - 0.5 for p in prices],
            'high': [p + 1.0 for p in prices],
            'low': [p - 1.0 for p in prices],
            'close': prices,
            'volume': [1000000] * 100
        }, index=dates)
        
        return data
    
    @pytest.fixture
    def analyzer(self):
        """创建分析器实例"""
        return ElliottWaveAnalyzer()
    
    def test_initialization(self, analyzer):
        """测试分析器初始化"""
        assert analyzer.min_wave_length == 5
        assert analyzer.confidence_threshold == 0.6
        assert analyzer.indicators is not None
    
    def test_preprocess_data(self, analyzer, sample_data):
        """测试数据预处理"""
        processed = analyzer._preprocess_data(sample_data)
        
        assert len(processed) == len(sample_data)
        assert 'price_change' in processed.columns
        assert 'price_change_abs' in processed.columns
        assert not processed.isnull().any().any()
    
    def test_identify_wave_structures(self, analyzer, sample_data):
        """测试波浪结构识别"""
        processed = analyzer._preprocess_data(sample_data)
        waves = analyzer._identify_wave_structures(processed)
        
        assert isinstance(waves, list)
        # 应该识别到至少一个波浪结构
        assert len(waves) > 0
    
    def test_calculate_wave_confidence(self, analyzer):
        """测试波浪置信度计算"""
        waves = [
            WavePoint(price=100, date=datetime.now(), wave_type='trough', confidence=0.8),
            WavePoint(price=110, date=datetime.now() + timedelta(days=1), wave_type='peak', confidence=0.8),
            WavePoint(price=105, date=datetime.now() + timedelta(days=2), wave_type='trough', confidence=0.8),
            WavePoint(price=120, date=datetime.now() + timedelta(days=3), wave_type='peak', confidence=0.8),
            WavePoint(price=115, date=datetime.now() + timedelta(days=4), wave_type='trough', confidence=0.8)
        ]
        
        confidence = analyzer._calculate_wave_confidence(waves)
        assert 0 <= confidence <= 1
    
    def test_calculate_fibonacci_ratios(self, analyzer):
        """测试斐波那契比例计算"""
        waves = [
            WavePoint(price=100, date=datetime.now(), wave_type='trough', confidence=0.8),
            WavePoint(price=110, date=datetime.now() + timedelta(days=1), wave_type='peak', confidence=0.8),
            WavePoint(price=120, date=datetime.now() + timedelta(days=2), wave_type='peak', confidence=0.8)
        ]
        
        ratios = analyzer._calculate_fibonacci_ratios(waves)
        assert isinstance(ratios, dict)
        assert len(ratios) > 0
    
    def test_calculate_key_levels(self, analyzer):
        """测试关键价位计算"""
        waves = [
            WavePoint(price=100, date=datetime.now(), wave_type='trough', confidence=0.8),
            WavePoint(price=120, date=datetime.now() + timedelta(days=2), wave_type='peak', confidence=0.8)
        ]
        
        wave = ElliottWave(
            wave_type='impulse',
            direction='bullish',
            waves=waves,
            confidence=0.8,
            start_date=datetime.now(),
            end_date=datetime.now() + timedelta(days=2),
            fibonacci_ratios={}
        )
        
        supports, resistances = analyzer._calculate_key_levels(wave, 110)
        assert isinstance(supports, list)
        assert isinstance(resistances, list)
        assert len(supports) > 0 or len(resistances) > 0
    
    def test_full_analysis(self, analyzer, sample_data):
        """测试完整分析流程"""
        result = analyzer.analyze(sample_data)
        
        assert isinstance(result, dict)
        assert 'status' in result
        assert result['status'] in ['success', 'no_waves_found']
        
        if result['status'] == 'success':
            assert 'wave_structure' in result
            assert 'key_levels' in result
            assert 'fibonacci_ratios' in result
    
    def test_empty_data(self, analyzer):
        """测试空数据处理"""
        empty_data = pd.DataFrame(columns=['open', 'high', 'low', 'close', 'volume'])
        result = analyzer.analyze(empty_data)
        
        assert 'error' in result or result['status'] == 'no_waves_found'
    
    def test_insufficient_data(self, analyzer):
        """测试数据不足情况"""
        # 创建少于最小波浪长度的数据
        dates = pd.date_range(start='2024-01-01', periods=3, freq='D')
        data = pd.DataFrame({
            'open': [100, 101, 102],
            'high': [101, 102, 103],
            'low': [99, 100, 101],
            'close': [100.5, 101.5, 102.5],
            'volume': [1000, 1000, 1000]
        }, index=dates)
        
        result = analyzer.analyze(data)
        assert result['status'] == 'no_waves_found'


class TestTechnicalIndicators:
    """测试技术指标计算"""
    
    @pytest.fixture
    def indicators(self):
        return ElliottWaveAnalyzer().indicators
    
    @pytest.fixture
    def sample_data(self):
        """创建技术指标测试数据"""
        dates = pd.date_range(start='2024-01-01', periods=50, freq='D')
        
        # 创建有趋势的价格数据
        trend = np.linspace(100, 110, 50)
        noise = np.random.normal(0, 1, 50)
        prices = trend + noise
        
        data = pd.DataFrame({
            'open': prices - 0.5,
            'high': prices + 1.0,
            'low': prices - 1.0,
            'close': prices,
            'volume': [1000000] * 50
        }, index=dates)
        
        return data
    
    def test_calculate_rsi(self, indicators):
        """测试RSI计算"""
        prices = pd.Series([100, 101, 102, 101, 100, 99, 98, 99, 100, 101])
        rsi = indicators.calculate_rsi(prices, period=3)
        
        assert len(rsi) == len(prices)
        assert 0 <= rsi.iloc[-1] <= 100
    
    def test_calculate_macd(self, indicators):
        """测试MACD计算"""
        prices = pd.Series(np.linspace(100, 110, 50))
        macd, signal, histogram = indicators.calculate_macd(prices)
        
        assert len(macd) == len(prices)
        assert len(signal) == len(prices)
        assert len(histogram) == len(prices)
    
    def test_calculate_kdj(self, indicators):
        """测试KDJ计算"""
        high = pd.Series(np.linspace(100, 110, 50))
        low = pd.Series(np.linspace(95, 105, 50))
        close = pd.Series(np.linspace(97.5, 107.5, 50))
        
        k, d, j = indicators.calculate_kdj(high, low, close)
        
        assert len(k) == len(high)
        assert len(d) == len(high)
        assert len(j) == len(high)
        assert 0 <= k.iloc[-1] <= 100
        assert 0 <= d.iloc[-1] <= 100
    
    def test_calculate_all(self, indicators, sample_data):
        """测试所有技术指标计算"""
        result = indicators.calculate_all(sample_data)
        
        expected_columns = ['RSI', 'MACD', 'MACD_signal', 'MACD_histogram', 'KDJ_K', 'KDJ_D', 'KDJ_J']
        for col in expected_columns:
            assert col in result.columns
        
        assert not result[expected_columns].isnull().all().all()


if __name__ == '__main__':
    pytest.main([__file__])