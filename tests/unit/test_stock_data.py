import unittest
from unittest.mock import patch, MagicMock
import pandas as pd
import numpy as np
from datetime import datetime

# Assuming the stock_data.py is in data_acquisition directory
from data_acquisition.stock_data import fetch_single_stock, get_stock_history, get_all_stock_codes, save_to_parquet, get_first_trading_day

class TestStockData(unittest.TestCase):

    def setUp(self):
        # Sample data for mocking ak.stock_zh_a_hist
        self.sample_df_chinese_cols_data = {
            '日期': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03']),
            '开盘': [100, 101, 102],
            '收盘': [101, 102, 103],
            '最高': [102, 103, 104],
            '最低': [99, 100, 101],
            '成交量': [1000, 1100, 1200]
        }
        self.sample_df_chinese_cols = pd.DataFrame(self.sample_df_chinese_cols_data)

        # Sample data with English column names, as expected after renaming
        self.sample_df_english_cols_data = {
            'date': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03']),
            'open': [100, 101, 102],
            'close': [101, 102, 103],
            'high': [102, 103, 104],
            'low': [99, 100, 101],
            'volume': [1000, 1100, 1200]
        }
        self.sample_df_english_cols = pd.DataFrame(self.sample_df_english_cols_data).set_index('date')

    @patch('akshare.stock_zh_a_hist')
    @patch('data_acquisition.stock_data.validate_ohlcv')
    @patch('data_acquisition.stock_data.detect_anomalies')
    def test_fetch_single_stock_success(self, mock_detect_anomalies, mock_validate_ohlcv, mock_ak_hist):
        mock_ak_hist.return_value = self.sample_df_chinese_cols.copy()
        # These mocks should return data with English columns, as if renaming happened
        mock_validate_ohlcv.return_value = self.sample_df_english_cols.copy()
        mock_detect_anomalies.return_value = self.sample_df_english_cols.copy() # Mocking to return a clean df

        df = fetch_single_stock('000001', '20230101', '20230103')

        self.assertFalse(df.empty)
        self.assertIn('open', df.columns)
        self.assertIn('close', df.columns)
        self.assertIn('volume', df.columns)
        self.assertEqual(len(df), 3)
        mock_ak_hist.assert_called_once()
        mock_validate_ohlcv.assert_called_once()
        mock_detect_anomalies.assert_called_once()

    @patch('akshare.stock_zh_a_hist')
    @patch('data_acquisition.stock_data.validate_ohlcv')
    @patch('data_acquisition.stock_data.detect_anomalies')
    def test_fetch_single_stock_empty_data(self, mock_detect_anomalies, mock_validate_ohlcv, mock_ak_hist):
        mock_ak_hist.return_value = pd.DataFrame() # Simulate empty data
        mock_validate_ohlcv.return_value = pd.DataFrame()
        mock_detect_anomalies.return_value = pd.DataFrame()

        df = fetch_single_stock('000001', '20230101', '20230103', retries=1)
        self.assertTrue(df.empty)
        mock_ak_hist.assert_called_once() # Should still be called once

    @patch('akshare.stock_zh_a_hist')
    @patch('data_acquisition.stock_data.validate_ohlcv')
    @patch('data_acquisition.stock_data.detect_anomalies')
    def test_fetch_single_stock_retries(self, mock_detect_anomalies, mock_validate_ohlcv, mock_ak_hist):
        mock_ak_hist.side_effect = [Exception("API Error"), self.sample_df_chinese_cols.copy()]
        mock_validate_ohlcv.return_value = self.sample_df_english_cols.copy()
        mock_detect_anomalies.return_value = self.sample_df_english_cols.copy()

        df = fetch_single_stock('000001', '20230101', '20230103', retries=2, delay=0)
        self.assertFalse(df.empty)
        self.assertEqual(mock_ak_hist.call_count, 2) # Should retry once

    @patch('data_acquisition.stock_data.fetch_single_stock')
    def test_get_stock_history_single(self, mock_fetch_single_stock):
        mock_fetch_single_stock.return_value = self.sample_df_english_cols.copy()
        
        result = get_stock_history('000001', '20230101', '20230103', max_workers=1)
        self.assertIn('000001', result)
        self.assertFalse(result['000001'].empty)
        mock_fetch_single_stock.assert_called_once_with('000001', '20230101', '20230103', 'daily')

    @patch('data_acquisition.stock_data.fetch_single_stock')
    def test_get_stock_history_multiple(self, mock_fetch_single_stock):
        mock_fetch_single_stock.side_effect = [self.sample_df_english_cols.copy(), self.sample_df_english_cols.copy()]
        
        result = get_stock_history(['000001', '000002'], '20230101', '20230103', max_workers=2)
        self.assertIn('000001', result)
        self.assertIn('000002', result)
        self.assertEqual(len(result), 2)
        self.assertEqual(mock_fetch_single_stock.call_count, 2)

    @patch('akshare.stock_zh_a_spot_em')
    def test_get_all_stock_codes_success(self, mock_ak_spot_em):
        mock_ak_spot_em.return_value = pd.DataFrame({'代码': ['000001', '000002']})
        codes = get_all_stock_codes()
        self.assertEqual(codes, ['000001', '000002'])
        mock_ak_spot_em.assert_called_once()

    @patch('akshare.stock_zh_a_spot_em')
    def test_get_all_stock_codes_failure(self, mock_ak_spot_em):
        mock_ak_spot_em.side_effect = Exception("API Error")
        codes = get_all_stock_codes()
        self.assertEqual(codes, [])
        mock_ak_spot_em.assert_called_once()

    @patch('pandas.DataFrame.to_parquet')
    @patch('pandas.concat')
    def test_save_to_parquet_success(self, mock_concat, mock_to_parquet):
        # Mock the concatenated DataFrame to have a to_parquet method
        mock_concat_df = MagicMock()
        mock_concat.return_value = mock_concat_df

        data = {'000001': self.sample_df_english_cols.copy()}
        save_to_parquet(data, 'test.parquet')
        mock_concat.assert_called_once()
        mock_concat_df.to_parquet.assert_called_once_with('test.parquet')

    def test_save_to_parquet_empty_data(self):
        # This should not call to_parquet
        with patch('pandas.DataFrame.to_parquet') as mock_to_parquet:
            save_to_parquet({}, 'test.parquet')
            mock_to_parquet.assert_not_called()

    @patch('akshare.stock_zh_a_hist')
    def test_get_first_trading_day_success(self, mock_ak_hist):
        mock_ak_hist.return_value = pd.DataFrame({'日期': ['2005-01-01', '2005-01-02']})
        first_day = get_first_trading_day('000001')
        self.assertEqual(first_day, pd.to_datetime('2005-01-01'))
        mock_ak_hist.assert_called_once()

    @patch('akshare.stock_zh_a_hist')
    def test_get_first_trading_day_failure(self, mock_ak_hist):
        mock_ak_hist.side_effect = Exception("API Error")
        first_day = get_first_trading_day('000001')
        self.assertEqual(first_day, pd.to_datetime('2000-01-01')) # Default fallback
        mock_ak_hist.assert_called_once()

if __name__ == '__main__':
    unittest.main()