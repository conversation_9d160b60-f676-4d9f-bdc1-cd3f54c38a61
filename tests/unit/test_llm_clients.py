"""
LLM客户端单元测试
测试各种LLM集成功能
"""

import pytest
import os
from unittest.mock import Mock, patch, MagicMock
import json

from llm_client.base_client import BaseLLMClient
from llm_client.openrouter_client import OpenRouterClient
from llm_client.gemini_client import Gemini<PERSON>lient
from llm_client.client_factory import LL<PERSON>lientFactory
from llm_client.config_validator import ConfigValidator


class TestBaseLLMClient:
    """测试基础LLM客户端"""
    
    def test_base_client_initialization(self):
        """测试基础客户端初始化"""
        client = BaseLLMClient(api_key="test_key", model="test_model")
        assert client.api_key == "test_key"
        assert client.model == "test_model"
    
    def test_base_client_abstract_methods(self):
        """测试抽象方法"""
        client = BaseLLMClient(api_key="test_key", model="test_model")
        
        with pytest.raises(NotImplementedError):
            client.generate_response("test prompt")
        
        with pytest.raises(NotImplementedError):
            client.analyze_wave_pattern("test data")


class TestOpenRouterClient:
    """测试OpenRouter客户端"""
    
    @pytest.fixture
    def mock_openai(self):
        """模拟OpenAI客户端"""
        with patch('llm_client.openrouter_client.OpenAI') as mock:
            yield mock
    
    def test_initialization(self, mock_openai):
        """测试初始化"""
        client = OpenRouterClient(api_key="test_key", model="gpt-4")
        
        mock_openai.assert_called_once_with(
            api_key="test_key",
            base_url="https://openrouter.ai/api/v1"
        )
    
    def test_generate_response(self, mock_openai):
        """测试响应生成"""
        mock_client = Mock()
        mock_response = Mock()
        mock_response.choices = [Mock(message=Mock(content="测试响应"))]
        mock_client.chat.completions.create.return_value = mock_response
        
        mock_openai.return_value = mock_client
        
        client = OpenRouterClient(api_key="test_key", model="gpt-4")
        response = client.generate_response("测试提示")
        
        assert response == "测试响应"
        mock_client.chat.completions.create.assert_called_once()
    
    def test_analyze_wave_pattern(self, mock_openai):
        """测试波浪分析"""
        mock_client = Mock()
        mock_response = Mock()
        mock_response.choices = [Mock(message=Mock(content=json.dumps({
            "wave_type": "impulse",
            "confidence": 0.85,
            "analysis": "测试分析"
        })))]
        mock_client.chat.completions.create.return_value = mock_response
        
        mock_openai.return_value = mock_client
        
        client = OpenRouterClient(api_key="test_key", model="gpt-4")
        result = client.analyze_wave_pattern("测试波浪数据")
        
        assert result["wave_type"] == "impulse"
        assert result["confidence"] == 0.85


class TestGeminiClient:
    """测试Gemini客户端"""
    
    @pytest.fixture
    def mock_genai(self):
        """模拟Google Generative AI"""
        with patch('llm_client.gemini_client.genai') as mock:
            yield mock
    
    def test_initialization(self, mock_genai):
        """测试初始化"""
        client = GeminiClient(api_key="test_key", model="gemini-pro")
        
        mock_genai.configure.assert_called_once_with(api_key="test_key")
    
    def test_generate_response(self, mock_genai):
        """测试响应生成"""
        mock_model = Mock()
        mock_response = Mock()
        mock_response.text = "测试响应"
        mock_model.generate_content.return_value = mock_response
        
        mock_genai.GenerativeModel.return_value = mock_model
        
        client = GeminiClient(api_key="test_key", model="gemini-pro")
        response = client.generate_response("测试提示")
        
        assert response == "测试响应"
        mock_model.generate_content.assert_called_once()
    
    def test_analyze_wave_pattern(self, mock_genai):
        """测试波浪分析"""
        mock_model = Mock()
        mock_response = Mock()
        mock_response.text = json.dumps({
            "wave_type": "corrective",
            "confidence": 0.75,
            "analysis": "测试调整浪分析"
        })
        mock_model.generate_content.return_value = mock_response
        
        mock_genai.GenerativeModel.return_value = mock_model
        
        client = GeminiClient(api_key="test_key", model="gemini-pro")
        result = client.analyze_wave_pattern("测试调整浪数据")
        
        assert result["wave_type"] == "corrective"
        assert result["confidence"] == 0.75


class TestConfigValidator:
    """测试配置验证器"""
    
    def test_validate_openai_config(self):
        """测试OpenAI配置验证"""
        config = {
            "provider": "openrouter",
            "api_key": "test_key",
            "model": "gpt-4"
        }
        
        validator = ConfigValidator()
        is_valid, error = validator.validate_config(config)
        
        assert is_valid is True
        assert error is None
    
    def test_validate_gemini_config(self):
        """测试Gemini配置验证"""
        config = {
            "provider": "gemini",
            "api_key": "test_key",
            "model": "gemini-pro"
        }
        
        validator = ConfigValidator()
        is_valid, error = validator.validate_config(config)
        
        assert is_valid is True
        assert error is None
    
    def test_validate_invalid_config(self):
        """测试无效配置验证"""
        config = {
            "provider": "invalid",
            "api_key": "test_key"
        }
        
        validator = ConfigValidator()
        is_valid, error = validator.validate_config(config)
        
        assert is_valid is False
        assert error is not None
    
    def test_validate_missing_api_key(self):
        """测试缺失API密钥"""
        config = {
            "provider": "openrouter",
            "model": "gpt-4"
        }
        
        validator = ConfigValidator()
        is_valid, error = validator.validate_config(config)
        
        assert is_valid is False
        assert "api_key" in str(error).lower()


class TestLLMClientFactory:
    """测试客户端工厂"""
    
    def test_create_openrouter_client(self):
        """测试创建OpenRouter客户端"""
        config = {
            "provider": "openrouter",
            "api_key": "test_key",
            "model": "gpt-4"
        }
        
        with patch.dict(os.environ, {"OPENROUTER_API_KEY": "test_key"}):
            client = LLMClientFactory.create_client(config)
            assert isinstance(client, OpenRouterClient)
    
    def test_create_gemini_client(self):
        """测试创建Gemini客户端"""
        config = {
            "provider": "gemini",
            "api_key": "test_key",
            "model": "gemini-pro"
        }
        
        with patch.dict(os.environ, {"GEMINI_API_KEY": "test_key"}):
            client = LLMClientFactory.create_client(config)
            assert isinstance(client, GeminiClient)
    
    def test_create_client_from_env(self):
        """测试从环境变量创建客户端"""
        with patch.dict(os.environ, {"OPENAI_API_KEY": "test_key"}):
            client = LLMClientFactory.create_client_from_env("openrouter")
            assert isinstance(client, OpenRouterClient)
    
    def test_create_invalid_client(self):
        """测试创建无效客户端"""
        config = {
            "provider": "invalid_provider",
            "api_key": "test_key"
        }
        
        with pytest.raises(ValueError):
            LLMClientFactory.create_client(config)


class TestErrorHandling:
    """测试错误处理"""
    
    def test_openrouter_api_error(self):
        """测试OpenRouter API错误处理"""
        with patch('llm_client.openrouter_client.OpenAI') as mock_openai:
            mock_client = Mock()
            mock_client.chat.completions.create.side_effect = Exception("API Error")
            mock_openai.return_value = mock_client
            
            client = OpenRouterClient(api_key="test_key", model="gpt-4")
            response = client.generate_response("test")
            
            assert "error" in response
    
    def test_gemini_api_error(self):
        """测试Gemini API错误处理"""
        with patch('llm_client.gemini_client.genai') as mock_genai:
            mock_model = Mock()
            mock_model.generate_content.side_effect = Exception("API Error")
            mock_genai.GenerativeModel.return_value = mock_model
            
            client = GeminiClient(api_key="test_key", model="gemini-pro")
            response = client.generate_response("test")
            
            assert "error" in response
    
    def test_invalid_json_response(self):
        """测试无效JSON响应处理"""
        with patch('llm_client.openrouter_client.OpenAI') as mock_openai:
            mock_client = Mock()
            mock_response = Mock()
            mock_response.choices = [Mock(message=Mock(content="invalid json"))]
            mock_client.chat.completions.create.return_value = mock_response
            mock_openai.return_value = mock_client
            
            client = OpenRouterClient(api_key="test_key", model="gpt-4")
            result = client.analyze_wave_pattern("test data")
            
            assert "error" in result


if __name__ == '__main__':
    pytest.main([__file__])