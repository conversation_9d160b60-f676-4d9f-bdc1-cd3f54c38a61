"""
RAG系统单元测试
测试知识检索和增强分析功能
"""

import pytest
import json
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock

from rag_system.knowledge_base import KnowledgeBase
from rag_system.rag_enhanced_analyzer import RAGEnhancedAnalyzer


class TestKnowledgeBase:
    """测试知识库"""
    
    @pytest.fixture
    def temp_knowledge_dir(self):
        """创建临时知识库目录"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试知识文件
            theory_file = os.path.join(temp_dir, "elliott_wave_theory.txt")
            with open(theory_file, 'w', encoding='utf-8') as f:
                f.write("""
艾略特波浪理论要点：
1. 市场走势呈波浪形态
2. 推动浪有5个子浪
3. 调整浪有3个子浪
4. 斐波那契比例关系
""")
            
            patterns_file = os.path.join(temp_dir, "patterns.json")
            with open(patterns_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "impulse_wave": {
                        "description": "推动浪特征",
                        "rules": ["浪3不能最短", "浪2不能回撤浪1的100%"]
                    },
                    "corrective_wave": {
                        "description": "调整浪特征",
                        "rules": ["通常呈3浪结构", "浪C通常等于浪A"]
                    }
                }, f, ensure_ascii=False)
            
            yield temp_dir
    
    @pytest.fixture
    def knowledge_base(self, temp_knowledge_dir):
        """创建知识库实例"""
        return KnowledgeBase(knowledge_dir=temp_knowledge_dir)
    
    def test_initialization(self, knowledge_base):
        """测试初始化"""
        assert knowledge_base.knowledge_dir is not None
        assert len(knowledge_base.documents) > 0
    
    def test_load_documents(self, knowledge_base):
        """测试文档加载"""
        documents = knowledge_base.load_documents()
        assert len(documents) > 0
        assert any("艾略特波浪理论" in doc for doc in documents)
    
    def test_search_relevant_content(self, knowledge_base):
        """测试相关内容搜索"""
        query = "推动浪特征"
        results = knowledge_base.search_relevant_content(query, top_k=2)
        
        assert len(results) <= 2
        assert any("推动浪" in str(result) for result in results)
    
    def test_get_wave_theory(self, knowledge_base):
        """测试获取波浪理论"""
        theory = knowledge_base.get_wave_theory()
        assert "艾略特波浪理论" in theory
    
    def test_get_pattern_rules(self, knowledge_base):
        """测试获取模式规则"""
        rules = knowledge_base.get_pattern_rules("impulse_wave")
        assert isinstance(rules, list)
        assert len(rules) > 0
    
    def test_empty_knowledge_dir(self):
        """测试空知识库目录"""
        with tempfile.TemporaryDirectory() as empty_dir:
            kb = KnowledgeBase(knowledge_dir=empty_dir)
            documents = kb.load_documents()
            assert len(documents) == 0


class TestRAGEnhancedAnalyzer:
    """测试RAG增强分析器"""
    
    @pytest.fixture
    def mock_llm_client(self):
        """模拟LLM客户端"""
        client = Mock()
        client.generate_response.return_value = "基于RAG的波浪分析结果"
        client.analyze_wave_pattern.return_value = {
            "wave_type": "impulse",
            "confidence": 0.85,
            "analysis": "RAG增强分析"
        }
        return client
    
    @pytest.fixture
    def mock_knowledge_base(self):
        """模拟知识库"""
        kb = Mock()
        kb.search_relevant_content.return_value = [
            "推动浪通常有5个子浪",
            "浪3通常是最强劲的浪"
        ]
        kb.get_wave_theory.return_value = "艾略特波浪理论基础知识"
        return kb
    
    @pytest.fixture
    def rag_analyzer(self, mock_llm_client, mock_knowledge_base):
        """创建RAG分析器实例"""
        return RAGEnhancedAnalyzer(
            llm_client=mock_llm_client,
            knowledge_base=mock_knowledge_base
        )
    
    def test_initialization(self, rag_analyzer):
        """测试初始化"""
        assert rag_analyzer.llm_client is not None
        assert rag_analyzer.knowledge_base is not None
    
    def test_enhance_wave_analysis(self, rag_analyzer):
        """测试增强波浪分析"""
        wave_data = {
            "wave_type": "impulse",
            "waves": [
                {"price": 100, "date": "2024-01-01", "type": "trough"},
                {"price": 110, "date": "2024-01-02", "type": "peak"}
            ]
        }
        
        result = rag_analyzer.enhance_wave_analysis(wave_data)
        
        assert "enhanced_analysis" in result
        assert "knowledge_context" in result
        assert rag_analyzer.knowledge_base.search_relevant_content.called
    
    def test_generate_context_aware_report(self, rag_analyzer):
        """测试生成上下文感知报告"""
        analysis_data = {
            "wave_structure": {"type": "impulse", "confidence": 0.8},
            "technical_indicators": {"RSI": 65, "MACD": 0.5}
        }
        
        report = rag_analyzer.generate_context_aware_report(analysis_data)
        
        assert isinstance(report, str)
        assert len(report) > 0
        assert rag_analyzer.llm_client.generate_response.called
    
    def test_get_trading_recommendations(self, rag_analyzer):
        """测试获取交易建议"""
        market_data = {
            "current_price": 105,
            "wave_analysis": {"type": "impulse", "direction": "bullish"},
            "key_levels": {"supports": [100, 98], "resistances": [110, 112]}
        }
        
        recommendations = rag_analyzer.get_trading_recommendations(market_data)
        
        assert isinstance(recommendations, dict)
        assert "recommendation" in recommendations
        assert "confidence" in recommendations
        assert "risk_level" in recommendations
    
    def test_error_handling(self, rag_analyzer):
        """测试错误处理"""
        # 模拟LLM客户端错误
        rag_analyzer.llm_client.generate_response.side_effect = Exception("API错误")
        
        result = rag_analyzer.enhance_wave_analysis({})
        
        assert "error" in result
        assert "API错误" in result["error"]
    
    def test_empty_knowledge_search(self, rag_analyzer):
        """测试空知识搜索结果"""
        rag_analyzer.knowledge_base.search_relevant_content.return_value = []
        
        result = rag_analyzer.enhance_wave_analysis({"test": "data"})
        
        assert "knowledge_context" in result
        assert result["knowledge_context"] == "无相关知识"


class TestIntegration:
    """测试集成场景"""
    
    def test_full_rag_workflow(self):
        """测试完整RAG工作流"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试知识库
            theory_file = os.path.join(temp_dir, "test_theory.txt")
            with open(theory_file, 'w', encoding='utf-8') as f:
                f.write("测试波浪理论内容")
            
            # 创建真实知识库
            knowledge_base = KnowledgeBase(knowledge_dir=temp_dir)
            
            # 创建模拟LLM客户端
            llm_client = Mock()
            llm_client.generate_response.return_value = "集成测试响应"
            
            # 创建RAG分析器
            analyzer = RAGEnhancedAnalyzer(
                llm_client=llm_client,
                knowledge_base=knowledge_base
            )
            
            # 执行分析
            wave_data = {
                "wave_type": "test",
                "price": 100,
                "indicators": {"RSI": 50}
            }
            
            result = analyzer.enhance_wave_analysis(wave_data)
            
            assert "enhanced_analysis" in result
            assert "knowledge_context" in result


if __name__ == '__main__':
    pytest.main([__file__])