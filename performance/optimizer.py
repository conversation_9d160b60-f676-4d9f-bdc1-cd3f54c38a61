"""
性能优化器
提供数据缓存、并行处理和性能监控功能
"""

import asyncio
import time
import logging
from typing import Dict, Any, List, Optional, Callable, Union
from functools import wraps
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import threading
import psutil
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import json
import hashlib
from pathlib import Path
import pickle
import gzip

logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = {}
        self.start_time = time.time()
        self.lock = threading.Lock()
    
    def record_metric(self, name: str, value: float, tags: Dict[str, str] = None):
        """记录性能指标"""
        with self.lock:
            if name not in self.metrics:
                self.metrics[name] = []
            
            self.metrics[name].append({
                'value': value,
                'timestamp': time.time(),
                'tags': tags or {}
            })
    
    def get_metric_stats(self, name: str) -> Dict[str, float]:
        """获取指标统计"""
        if name not in self.metrics:
            return {}
        
        values = [m['value'] for m in self.metrics[name]]
        return {
            'count': len(values),
            'mean': np.mean(values),
            'median': np.median(values),
            'min': np.min(values),
            'max': np.max(values),
            'std': np.std(values),
            'p95': np.percentile(values, 95),
            'p99': np.percentile(values, 99)
        }
    
    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计"""
        return {
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_percent': psutil.virtual_memory().percent,
            'memory_available_gb': psutil.virtual_memory().available / (1024**3),
            'disk_usage_percent': psutil.disk_usage('/').percent,
            'uptime_seconds': time.time() - self.start_time
        }
    
    def export_metrics(self, filepath: str):
        """导出性能指标"""
        with open(filepath, 'w') as f:
            json.dump({
                'metrics': self.metrics,
                'system_stats': self.get_system_stats(),
                'export_time': datetime.now().isoformat()
            }, f, indent=2)

class AsyncExecutor:
    """异步执行器"""
    
    def __init__(self, max_workers: int = None):
        self.max_workers = max_workers or (psutil.cpu_count() * 2)
        self.thread_pool = ThreadPoolExecutor(max_workers=self.max_workers)
        self.process_pool = ProcessPoolExecutor(max_workers=self.max_workers)
    
    async def run_in_thread(self, func: Callable, *args, **kwargs):
        """在线程池中运行函数"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.thread_pool, func, *args, **kwargs)
    
    async def run_in_process(self, func: Callable, *args, **kwargs):
        """在进程池中运行函数"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.process_pool, func, *args, **kwargs)
    
    def shutdown(self):
        """关闭执行器"""
        self.thread_pool.shutdown(wait=True)
        self.process_pool.shutdown(wait=True)

class BatchProcessor:
    """批处理器"""
    
    def __init__(self, batch_size: int = 100, max_concurrent: int = 10):
        self.batch_size = batch_size
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)
    
    async def process_batch(self, items: List[Any], processor: Callable) -> List[Any]:
        """批量处理"""
        results = []
        
        # 分批处理
        for i in range(0, len(items), self.batch_size):
            batch = items[i:i + self.batch_size]
            batch_results = await self._process_single_batch(batch, processor)
            results.extend(batch_results)
        
        return results
    
    async def _process_single_batch(self, batch: List[Any], processor: Callable) -> List[Any]:
        """处理单批数据"""
        async with self.semaphore:
            tasks = [processor(item) for item in batch]
            return await asyncio.gather(*tasks, return_exceptions=True)

class MemoryOptimizer:
    """内存优化器"""
    
    @staticmethod
    def optimize_dataframe(df: pd.DataFrame) -> pd.DataFrame:
        """优化DataFrame内存使用"""
        df = df.copy()
        
        # 优化数值类型
        for col in df.select_dtypes(include=['int64']).columns:
            if df[col].min() >= 0:
                if df[col].max() < 255:
                    df[col] = df[col].astype('uint8')
                elif df[col].max() < 65535:
                    df[col] = df[col].astype('uint16')
                elif df[col].max() < 4294967295:
                    df[col] = df[col].astype('uint32')
                else:
                    df[col] = df[col].astype('uint64')
            else:
                if df[col].min() > -128 and df[col].max() < 127:
                    df[col] = df[col].astype('int8')
                elif df[col].min() > -32768 and df[col].max() < 32767:
                    df[col] = df[col].astype('int16')
                elif df[col].min() > -2147483648 and df[col].max() < 2147483647:
                    df[col] = df[col].astype('int32')
                else:
                    df[col] = df[col].astype('int64')
        
        for col in df.select_dtypes(include=['float64']).columns:
            df[col] = df[col].astype('float32')
        
        return df
    
    @staticmethod
    def chunk_dataframe(df: pd.DataFrame, chunk_size: int = 10000) -> List[pd.DataFrame]:
        """分块处理DataFrame"""
        chunks = []
        for i in range(0, len(df), chunk_size):
            chunk = df.iloc[i:i + chunk_size].copy()
            chunks.append(chunk)
        return chunks

# 全局实例
monitor = PerformanceMonitor()
executor = AsyncExecutor()
batch_processor = BatchProcessor()

# 装饰器函数
def measure_time(func_name: str = None):
    """测量函数执行时间的装饰器"""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                execution_time = time.time() - start_time
                monitor.record_metric(
                    f"{func_name or func.__name__}_duration",
                    execution_time
                )
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                monitor.record_metric(
                    f"{func_name or func.__name__}_error_duration",
                    execution_time
                )
                raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                monitor.record_metric(
                    f"{func_name or func.__name__}_duration",
                    execution_time
                )
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                monitor.record_metric(
                    f"{func_name or func.__name__}_error_duration",
                    execution_time
                )
                raise
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator