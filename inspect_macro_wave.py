import pandas as pd
import os
import sys

# Add project root to sys.path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from elliott_wave.core_patterns import _find_local_extrema, _validate_impulse_wave, _validate_diagonal_triangle

def analyze_macro_wave(data_path: str, start_date: str, end_date: str):
    """Analyzes a specific macro period of stock data to check for a 5-wave impulse structure."""
    print(f"--- Analyzing Macro Wave for period {start_date} to {end_date} ---")

    # 1. Load and filter data
    try:
        df = pd.read_parquet(data_path)
        df.index = pd.to_datetime(df.index)
        period_df = df.loc[start_date:end_date]
        if period_df.empty:
            print("Error: No data found for the specified period.")
            return
        print(f"Loaded {len(period_df)} data points for the macro period.")
    except Exception as e:
        print(f"Error loading or filtering data: {e}")
        return

    # 2. Find major pivots in the macro trend
    # For a macro trend, we need to smooth the data to find the most significant pivots.
    # We can use weekly resampling for this.
    weekly_df = period_df.resample('W').agg({
        'open': 'first', 'high': 'max', 'low': 'min', 'close': 'last', 'volume': 'sum'
    }).dropna()
    
    extrema = _find_local_extrema(weekly_df)
    if len(extrema) < 6:
        print(f"Found only {len(extrema)} major pivots on a weekly scale. Not enough for a 5-wave impulse structure.")
        print("Pivots found:", extrema)
        return

    print(f"Found {len(extrema)} major pivots on a weekly scale. Checking the first 5-wave sequence...")
    points = extrema[:6]
    
    print("\n--- Potential Macro 5-Wave Impulse Structure Points (Weekly Pivots) ---")
    for i, p in enumerate(points):
        print(f"  Point {i}: {p['date'].strftime('%Y-%m-%d')} at price {p['price']:.2f} ({p['type']})")

    # 3. Check Elliott Wave Rules
    print("\n--- Elliott Wave Rule Validation ---")
    is_bullish = points[5]['price'] > points[0]['price']
    
    # Check for Standard Impulse Wave
    std_impulse_confidence = _validate_impulse_wave(points, is_bullish, weekly_df)
    
    # Check for Diagonal Triangle
    diag_triangle_confidence = _validate_diagonal_triangle(points, is_bullish, weekly_df)

    print(f"Standard Impulse Wave Confidence: {std_impulse_confidence:.2f}")
    print(f"Diagonal Triangle Confidence: {diag_triangle_confidence:.2f}")

    # 4. Conclusion
    print("\n--- Conclusion ---")
    if std_impulse_confidence > 0.5:
        print("The macro trend IS CONSISTENT with the rules of a STANDARD 5-wave impulse structure.")
    elif diag_triangle_confidence > 0.5:
        print("The macro trend IS CONSISTENT with the rules of a DIAGONAL TRIANGLE 5-wave impulse structure.")
    else:
        print("The macro trend IS NOT CONSISTENT with a clear 5-wave impulse structure (neither standard nor diagonal). ")
        print("While it represents a significant bull market, its internal structure does not fit neatly into the Elliott Wave impulse model based on the major pivots.")

if __name__ == '__main__':
    data_file = os.path.join(os.path.dirname(__file__), 'stock_data', '000831_long_term.parquet')
    # Using the dates found in the previous step
    start_date = '2010-07-02'
    end_date = '2021-09-01'
    analyze_macro_wave(data_file, start_date, end_date)
