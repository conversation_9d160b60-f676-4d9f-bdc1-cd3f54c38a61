#!/usr/bin/env python3
"""
完整的Web界面应用
集成所有ElliottAgents功能的Streamlit界面
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
from datetime import datetime, timedelta
import os
import sys
import numpy as np
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

# 导入可观测性模块
from web_interface.observability import observability_manager, ObservabilityDashboard

# 页面配置
st.set_page_config(
    page_title="🌊 ElliottAgents - 艾略特波浪分析系统",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .risk-warning {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        padding: 1rem;
        border-radius: 5px;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# 初始化会话状态
if 'initialized' not in st.session_state:
    st.session_state.initialized = True
    st.session_state.current_symbol = "AAPL"
    st.session_state.current_market = "美股"
    st.session_state.current_timeframe = "daily"
    st.session_state.last_analysis = None
    st.session_state.use_demo_data = True
    st.session_state.data_cache = {}
    st.session_state.force_refresh = False

def generate_demo_data(symbol: str, days: int = 365) -> pd.DataFrame:
    """生成演示数据"""
    np.random.seed(42)
    dates = pd.date_range(start=datetime.now() - timedelta(days=days), 
                         end=datetime.now(), freq='D')
    
    n = len(dates)
    returns = np.random.normal(0.001, 0.02, n)
    prices = 100 * np.exp(np.cumsum(returns))
    
    # 添加艾略特波浪模式
    wave_pattern = 15 * np.sin(np.linspace(0, 12*np.pi, n))
    prices += wave_pattern
    
    # 添加趋势
    trend = np.linspace(0, 20, n)
    prices += trend
    
    # 计算OHLCV
    open_prices = prices + np.random.normal(0, 0.5, n)
    high_prices = np.maximum(prices, open_prices) + np.abs(np.random.normal(0, 1, n))
    low_prices = np.minimum(prices, open_prices) - np.abs(np.random.normal(0, 1, n))
    close_prices = prices
    volumes = np.random.randint(1000000, 10000000, n)
    
    data = pd.DataFrame({
        'open': open_prices,
        'high': high_prices,
        'low': low_prices,
        'close': close_prices,
        'volume': volumes
    }, index=dates)
    
    return data

def calculate_technical_indicators(data: pd.DataFrame) -> dict:
    """计算技术指标"""
    close = data['close']
    
    # RSI
    delta = close.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    
    # MACD
    exp1 = close.ewm(span=12).mean()
    exp2 = close.ewm(span=26).mean()
    macd = exp1 - exp2
    signal = macd.ewm(span=9).mean()
    
    # 布林带
    bb_middle = close.rolling(window=20).mean()
    bb_std = close.rolling(window=20).std()
    bb_upper = bb_middle + (bb_std * 2)
    bb_lower = bb_middle - (bb_std * 2)
    
    return {
        'rsi': rsi.iloc[-1] if len(rsi) > 0 else 50,
        'macd': macd.iloc[-1] if len(macd) > 0 else 0,
        'macd_signal': signal.iloc[-1] if len(signal) > 0 else 0,
        'bb_upper': bb_upper.iloc[-1] if len(bb_upper) > 0 else close.iloc[-1],
        'bb_lower': bb_lower.iloc[-1] if len(bb_lower) > 0 else close.iloc[-1]
    }

def identify_elliott_waves(data: pd.DataFrame) -> dict:
    """识别艾略特波浪"""
    prices = data['close'].values
    
    # 简单的波浪识别算法
    peaks = []
    troughs = []
    
    for i in range(1, len(prices)-1):
        if prices[i] > prices[i-1] and prices[i] > prices[i+1]:
            peaks.append(i)
        elif prices[i] < prices[i-1] and prices[i] < prices[i+1]:
            troughs.append(i)
    
    # 识别可能的5浪结构
    waves = []
    if len(peaks) >= 2 and len(troughs) >= 2:
        # 简化的5浪识别
        waves = [
            {"type": "1", "start": 0, "end": peaks[0], "direction": "up"},
            {"type": "2", "start": peaks[0], "end": troughs[0], "direction": "down"},
            {"type": "3", "start": troughs[0], "end": peaks[1], "direction": "up"},
            {"type": "4", "start": peaks[1], "end": troughs[1], "direction": "down"},
            {"type": "5", "start": troughs[1], "end": len(prices)-1, "direction": "up"}
        ]
    
    return {
        "waves": waves,
        "current_wave": len(waves),
        "pattern": "5浪上升" if len(waves) >= 5 else "未识别",
        "confidence": min(0.8, len(waves) * 0.15)
    }

def main():
    """主应用"""
    
    # 标题
    st.markdown('<div class="main-header">🌊 ElliottAgents - 艾略特波浪分析系统</div>', unsafe_allow_html=True)
    
    # 侧边栏
    with st.sidebar:
        st.header("📊 控制面板")
        
        # 数据源选择
        use_demo_data = st.toggle("使用演示数据", value=st.session_state.use_demo_data)
        if use_demo_data != st.session_state.use_demo_data:
            st.session_state.use_demo_data = use_demo_data
            st.session_state.last_analysis = None
            st.session_state.data_cache = {}
            st.rerun()
        
        # 市场选择
        market_options = ["A股", "美股", "港股", "加密货币"]
        current_market = st.selectbox(
            "选择市场",
            market_options,
            index=market_options.index(st.session_state.current_market)
        )
        if current_market != st.session_state.current_market:
            st.session_state.current_market = current_market
            st.session_state.last_analysis = None
            st.session_state.data_cache = {}
            st.rerun()
        
        # 股票选择 - 支持自定义输入
        if st.session_state.current_market == "加密货币":
            default_symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT", "SOLUSDT"]
        elif st.session_state.current_market == "A股":
            default_symbols = ["000831", "600519", "000001", "300750"]
        elif st.session_state.current_market == "港股":
            default_symbols = ["00700", "03690", "09988", "01810"]
        else:  # 美股
            default_symbols = ["AAPL", "GOOGL", "MSFT", "TSLA", "NVDA"]
        
        # 使用文本输入框支持自定义股票代码
        current_symbol = st.text_input(
            "输入股票代码",
            value=st.session_state.current_symbol,
            placeholder="输入股票代码，如 600111"
        )
        
        # 也提供下拉选择作为参考
        st.write("或从列表选择:")
        selected_from_list = st.selectbox(
            "从列表选择",
            [""] + default_symbols,
            format_func=lambda x: "请选择..." if x == "" else x
        )
        
        # 处理选择变化
        new_symbol = current_symbol.strip().upper()
        if selected_from_list and selected_from_list != "":
            new_symbol = selected_from_list
        
        if new_symbol and new_symbol != st.session_state.current_symbol:
            st.session_state.current_symbol = new_symbol
            st.session_state.last_analysis = None
            st.session_state.data_cache = {}
            st.rerun()
        
        # 时间框架
        timeframe_options = {
            "1分钟": "1min",
            "5分钟": "5min",
            "15分钟": "15min",
            "1小时": "1h",
            "4小时": "4h",
            "日线": "daily",
            "周线": "weekly",
            "月线": "monthly"
        }
        
        selected_timeframe = st.selectbox(
            "时间框架",
            list(timeframe_options.keys()),
            index=list(timeframe_options.values()).index(st.session_state.current_timeframe)
        )
        new_timeframe = timeframe_options[selected_timeframe]
        if new_timeframe != st.session_state.current_timeframe:
            st.session_state.current_timeframe = new_timeframe
            st.session_state.last_analysis = None
            st.session_state.data_cache = {}
            st.rerun()
        
        # 分析按钮
        analyze_button = st.button("🔍 开始分析", type="primary", use_container_width=True)
        
        # 记录用户交互
        if analyze_button:
            observability_manager.log_user_interaction("analyze_button_click", "analyze_button", {
                "symbol": st.session_state.current_symbol,
                "market": st.session_state.current_market,
                "timeframe": st.session_state.current_timeframe,
                "use_demo": st.session_state.use_demo_data
            })
        
        # 风险警告
        st.markdown("""
        <div class="risk-warning">
        <strong>⚠️ 风险提示</strong><br>
        本分析仅供参考，不构成投资建议。<br>
        投资有风险，入市需谨慎。
        </div>
        """, unsafe_allow_html=True)
    
    # 主要内容区域
    if analyze_button or st.session_state.last_analysis is None:
        with st.spinner(f"正在分析 {st.session_state.current_symbol}..."):
            try:
                if st.session_state.use_demo_data:
                    data = generate_demo_data(st.session_state.current_symbol)
                    is_demo = True
                else:
                    # 这里应该调用实时数据获取
                    data = generate_demo_data(st.session_state.current_symbol)  # 暂时用演示数据
                    is_demo = True
                
                st.session_state.last_analysis = {
                    "symbol": st.session_state.current_symbol,
                    "data": data,
                    "is_demo": is_demo
                }
                
            except Exception as e:
                st.error(f"分析失败: {str(e)}")
                data = generate_demo_data(st.session_state.current_symbol)
                st.session_state.last_analysis = {
                    "symbol": st.session_state.current_symbol,
                    "data": data,
                    "is_demo": True
                }
    
    # 显示分析结果
    if st.session_state.last_analysis:
        display_analysis_results()
    
    # 页脚
    st.markdown("---")
    st.markdown("""
    <div style='text-align: center; color: #666;'>
    <p>基于学术论文 "Large Language Models and the Elliott Wave Principle" 实现</p>
    <p>ElliottAgents - 多智能体艾略特波浪分析系统</p>
    </div>
    """, unsafe_allow_html=True)

def display_analysis_results():
    """显示分析结果"""
    
    analysis = st.session_state.last_analysis
    symbol = analysis["symbol"]
    data = analysis["data"]
    
    # 创建标签页
    tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs([
        "📈 价格图表",
        "🌊 波浪分析",
        "📊 技术指标",
        "📋 分析报告",
        "🤖 AI建议",
        "🔍 可观测性"
    ])
    
    with tab1:
        start_time = time.time()
        display_price_chart(symbol, data)
        observability_manager.log_performance_metric("price_chart_render",
                                                   (time.time() - start_time) * 1000)
    
    with tab2:
        start_time = time.time()
        display_wave_analysis(symbol, data)
        observability_manager.log_performance_metric("wave_analysis_render",
                                                   (time.time() - start_time) * 1000)
    
    with tab3:
        start_time = time.time()
        display_technical_analysis(symbol, data)
        observability_manager.log_performance_metric("technical_analysis_render",
                                                   (time.time() - start_time) * 1000)
    
    with tab4:
        start_time = time.time()
        display_detailed_report(symbol, data)
        observability_manager.log_performance_metric("report_render",
                                                   (time.time() - start_time) * 1000)
    
    with tab5:
        start_time = time.time()
        display_ai_recommendations(symbol, data)
        observability_manager.log_performance_metric("ai_recommendations_render",
                                                   (time.time() - start_time) * 1000)
    
    with tab6:
        dashboard = ObservabilityDashboard(observability_manager)
        dashboard.render_dashboard()

def display_price_chart(symbol: str, data: pd.DataFrame):
    """显示价格图表"""
    st.header("📈 价格图表")
    
    # 创建蜡烛图
    fig = go.Figure()
    
    # 添加蜡烛图
    fig.add_trace(go.Candlestick(
        x=data.index,
        open=data['open'],
        high=data['high'],
        low=data['low'],
        close=data['close'],
        name="K线",
        increasing_line_color='#26a69a',
        decreasing_line_color='#ef5350'
    ))
    
    # 添加成交量
    fig.add_trace(go.Bar(
        x=data.index,
        y=data['volume'],
        name="成交量",
        yaxis="y2",
        marker_color='rgba(0,0,255,0.3)'
    ))
    
    # 更新布局
    fig.update_layout(
        title=f"{symbol} - 价格走势图",
        yaxis_title="价格",
        yaxis2=dict(
            title="成交量",
            overlaying="y",
            side="right",
            showgrid=False
        ),
        xaxis_rangeslider_visible=False,
        height=500,
        template="plotly_white",
        showlegend=True
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # 基本信息
    col1, col2, col3, col4 = st.columns(4)
    latest = data.iloc[-1]
    prev = data.iloc[-2]
    
    with col1:
        change = (latest['close'] - prev['close']) / prev['close'] * 100
        st.metric("当前价格", f"${latest['close']:.2f}", f"{change:+.2f}%")
    
    with col2:
        st.metric("最高价", f"${latest['high']:.2f}")
    
    with col3:
        st.metric("最低价", f"${latest['low']:.2f}")
    
    with col4:
        st.metric("成交量", f"{latest['volume']:,.0f}")

def display_wave_analysis(symbol: str, data: pd.DataFrame):
    """显示波浪分析"""
    st.header("🌊 艾略特波浪分析")
    
    # 识别波浪
    wave_result = identify_elliott_waves(data)
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        # 创建波浪图表
        fig = go.Figure()
        
        # 价格线
        fig.add_trace(go.Scatter(
            x=list(range(len(data))),
            y=data['close'],
            mode='lines',
            name='价格',
            line=dict(color='blue', width=2)
        ))
        
        # 标记波浪
        colors = ['red', 'orange', 'green', 'yellow', 'purple']
        for i, wave in enumerate(wave_result['waves'][:5]):
            start_idx = wave['start']
            end_idx = wave['end']
            
            fig.add_trace(go.Scatter(
                x=list(range(start_idx, end_idx+1)),
                y=data['close'].iloc[start_idx:end_idx+1],
                mode='lines+markers',
                name=f"浪{wave['type']}",
                line=dict(color=colors[i % len(colors)], width=3),
                marker=dict(size=8)
            ))
        
        fig.update_layout(
            title=f"{symbol} - 艾略特波浪结构",
            xaxis_title="时间",
            yaxis_title="价格",
            height=400,
            template="plotly_white"
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # 波浪信息
        st.subheader("波浪信息")
        
        st.metric("当前波浪", f"第{wave_result['current_wave']}浪")
        st.metric("识别模式", wave_result['pattern'])
        st.metric("置信度", f"{wave_result['confidence']*100:.1f}%")
        
        # 波浪阶段说明
        st.subheader("波浪阶段")
        wave_explanations = {
            "1": "初期上升，市场情绪谨慎",
            "2": "回调修正，但高于起点",
            "3": "最强上升浪，成交量放大",
            "4": "再次回调，通常较温和",
            "5": "最后上升，可能出现背离"
        }
        
        for wave in wave_result['waves'][:3]:
            st.write(f"**浪{wave['type']}**: {wave_explanations.get(wave['type'], '分析中')}")

def display_technical_analysis(symbol: str, data: pd.DataFrame):
    """显示技术分析"""
    st.header("📊 技术指标")
    
    # 计算技术指标
    indicators = calculate_technical_indicators(data)
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("RSI", f"{indicators['rsi']:.1f}")
        if indicators['rsi'] > 70:
            st.error("超买")
        elif indicators['rsi'] < 30:
            st.success("超卖")
        else:
            st.info("正常")
    
    with col2:
        st.metric("MACD", f"{indicators['macd']:.2f}")
        st.metric("MACD信号", f"{indicators['macd_signal']:.2f}")
    
    with col3:
        st.metric("布林带上轨", f"${indicators['bb_upper']:.2f}")
        st.metric("布林带下轨", f"${indicators['bb_lower']:.2f}")
    
    # 技术信号
    st.subheader("技术信号")
    signals = []
    
    if indicators['rsi'] > 70:
        signals.append("⚠️ RSI超买，可能回调")
    elif indicators['rsi'] < 30:
        signals.append("✅ RSI超卖，可能反弹")
    
    if indicators['macd'] > indicators['macd_signal']:
        signals.append("✅ MACD金叉，看涨信号")
    else:
        signals.append("⚠️ MACD死叉，看跌信号")
    
    for signal in signals:
        st.write(signal)

def display_detailed_report(symbol: str, data: pd.DataFrame):
    """显示详细报告"""
    st.header("📋 详细分析报告")
    
    # 波浪分析结果
    wave_result = identify_elliott_waves(data)
    indicators = calculate_technical_indicators(data)
    
    # 报告内容
    report = f"""
    ## {symbol} 艾略特波浪分析报告
    
    ### 1. 波浪结构分析
    - **当前波浪**: 第{wave_result['current_wave']}浪
    - **识别模式**: {wave_result['pattern']}
    - **置信度**: {wave_result['confidence']*100:.1f}%
    
    ### 2. 技术指标
    - **RSI**: {indicators['rsi']:.1f}
    - **MACD**: {indicators['macd']:.2f}
    - **MACD信号**: {indicators['macd_signal']:.2f}
    - **布林带上轨**: ${indicators['bb_upper']:.2f}
    - **布林带下轨**: ${indicators['bb_lower']:.2f}
    
    ### 3. 市场展望
    基于当前分析，市场可能处于上升周期的{wave_result['current_wave']}浪阶段。
    建议密切关注后续价格走势和技术指标变化。
    """
    
    st.markdown(report)

def display_ai_recommendations(symbol: str, data: pd.DataFrame):
    """显示AI建议"""
    st.header("🤖 AI投资建议")
    
    # 波浪分析结果
    wave_result = identify_elliott_waves(data)
    indicators = calculate_technical_indicators(data)
    
    # 生成建议
    recommendations = []
    
    if wave_result['current_wave'] <= 3:
        recommendations.append("✅ **建议**: 当前处于上升周期早期，可考虑逢低买入")
    elif wave_result['current_wave'] == 4:
        recommendations.append("⚠️ **建议**: 第4浪回调，等待第5浪启动")
    elif wave_result['current_wave'] == 5:
        recommendations.append("⚠️ **建议**: 第5浪末期，注意获利了结")
    
    if indicators['rsi'] < 30:
        recommendations.append("✅ **超卖机会**: RSI显示超卖，可能是买入时机")
    elif indicators['rsi'] > 70:
        recommendations.append("⚠️ **超买风险**: RSI显示超买，注意回调风险")
    
    # 显示建议
    for rec in recommendations:
        st.info(rec)
    
    # 风险提示
    st.warning("""
    **重要提醒**:
    - 本分析基于历史数据和技术指标
    - 不构成投资建议，投资有风险
    - 请结合基本面分析和风险管理
    """)

if __name__ == "__main__":
    main()