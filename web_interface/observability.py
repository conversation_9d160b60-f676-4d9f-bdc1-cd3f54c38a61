"""
全栈可观测性系统
实现前端用户交互、后端API调用、数据库事务的完整追踪
"""

import streamlit as st
import json
import time
import uuid
from datetime import datetime
from typing import Dict, Any, Optional
import logging
from pathlib import Path
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/observability.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ObservabilityManager:
    """全栈可观测性管理器"""
    
    def __init__(self):
        self.trace_id = str(uuid.uuid4())
        self.events = []
        self.start_time = time.time()
        
    def generate_trace_id(self) -> str:
        """生成新的追踪ID"""
        return str(uuid.uuid4())
    
    def log_event(self, event_type: str, data: Dict[str, Any], 
                  timestamp: Optional[float] = None) -> None:
        """记录事件"""
        if timestamp is None:
            timestamp = time.time()
            
        event = {
            "trace_id": self.trace_id,
            "event_type": event_type,
            "timestamp": timestamp,
            "relative_time": timestamp - self.start_time,
            "data": data
        }
        
        self.events.append(event)
        logger.info(json.dumps(event, ensure_ascii=False))
        
        # 在Streamlit会话中存储
        if 'observability_events' not in st.session_state:
            st.session_state.observability_events = []
        st.session_state.observability_events.append(event)
    
    def log_user_interaction(self, interaction_type: str, element_id: str, 
                           details: Dict[str, Any]) -> None:
        """记录用户交互"""
        self.log_event("user_interaction", {
            "type": interaction_type,
            "element_id": element_id,
            "details": details
        })
    
    def log_api_call(self, endpoint: str, method: str, 
                    request_data: Dict[str, Any], 
                    response_data: Optional[Dict[str, Any]] = None,
                    status_code: Optional[int] = None,
                    duration: Optional[float] = None) -> None:
        """记录API调用"""
        self.log_event("api_call", {
            "endpoint": endpoint,
            "method": method,
            "request": request_data,
            "response": response_data,
            "status_code": status_code,
            "duration": duration
        })
    
    def log_database_query(self, query: str, duration: float, 
                          rows_affected: int = 0) -> None:
        """记录数据库查询"""
        self.log_event("database_query", {
            "query": query,
            "duration": duration,
            "rows_affected": rows_affected
        })
    
    def log_error(self, error_type: str, error_message: str, 
                  context: Dict[str, Any]) -> None:
        """记录错误"""
        self.log_event("error", {
            "error_type": error_type,
            "message": error_message,
            "context": context
        })
    
    def log_performance_metric(self, metric_name: str, value: float, 
                             unit: str = "ms") -> None:
        """记录性能指标"""
        self.log_event("performance_metric", {
            "metric_name": metric_name,
            "value": value,
            "unit": unit
        })
    
    def get_events_by_type(self, event_type: str) -> list:
        """按类型获取事件"""
        return [e for e in self.events if e["event_type"] == event_type]
    
    def get_events_by_trace_id(self, trace_id: str) -> list:
        """按追踪ID获取事件"""
        return [e for e in self.events if e["trace_id"] == trace_id]
    
    def analyze_anomalies(self) -> Dict[str, Any]:
        """分析异常"""
        anomalies = {
            "slow_api_calls": [],
            "error_responses": [],
            "stale_data": [],
            "user_interaction_delays": []
        }
        
        # 分析慢API调用
        api_calls = self.get_events_by_type("api_call")
        for call in api_calls:
            if call["data"].get("duration", 0) > 2000:  # 超过2秒
                anomalies["slow_api_calls"].append(call)
        
        # 分析错误响应
        errors = self.get_events_by_type("error")
        anomalies["error_responses"].extend(errors)
        
        # 分析用户交互延迟
        interactions = self.get_events_by_type("user_interaction")
        for interaction in interactions:
            if interaction["relative_time"] > 10:  # 超过10秒
                anomalies["user_interaction_delays"].append(interaction)
        
        return anomalies

class ObservabilityDashboard:
    """可观测性仪表板"""
    
    def __init__(self, observability_manager: ObservabilityManager):
        self.manager = observability_manager
    
    def render_dashboard(self):
        """渲染可观测性仪表板"""
        st.header("🔍 全栈可观测性仪表板")
        
        # 实时事件流
        col1, col2 = st.columns([2, 1])
        
        with col1:
            st.subheader("📊 实时事件流")
            if 'observability_events' in st.session_state:
                events_df = pd.DataFrame(st.session_state.observability_events[-50:])
                if not events_df.empty:
                    events_df['timestamp'] = pd.to_datetime(events_df['timestamp'], unit='s')
                    st.dataframe(
                        events_df[['timestamp', 'event_type', 'data']].tail(10),
                        use_container_width=True
                    )
        
        with col2:
            st.subheader("📈 性能指标")
            if 'observability_events' in st.session_state:
                events_df = pd.DataFrame(st.session_state.observability_events)
                if not events_df.empty:
                    # 事件类型统计
                    event_counts = events_df['event_type'].value_counts()
                    fig = px.pie(values=event_counts.values, 
                                names=event_counts.index,
                                title="事件类型分布")
                    st.plotly_chart(fig, use_container_width=True)
        
        # 异常检测
        st.subheader("⚠️ 异常检测")
        anomalies = self.manager.analyze_anomalies()
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("慢API调用", len(anomalies["slow_api_calls"]))
        
        with col2:
            st.metric("错误响应", len(anomalies["error_responses"]))
        
        with col3:
            st.metric("数据过期", len(anomalies["stale_data"]))
        
        with col4:
            st.metric("交互延迟", len(anomalies["user_interaction_delays"]))
        
        # 详细异常信息
        if any(anomalies.values()):
            st.subheader("异常详情")
            
            for anomaly_type, anomaly_list in anomalies.items():
                if anomaly_list:
                    st.write(f"**{anomaly_type}**:")
                    for anomaly in anomaly_list:
                        st.json(anomaly)
        
        # 用户旅程回放
        st.subheader("🎬 用户旅程回放")
        if st.button("开始回放"):
            self.render_user_journey_replay()
    
    def render_user_journey_replay(self):
        """渲染用户旅程回放"""
        if 'observability_events' in st.session_state:
            events = st.session_state.observability_events
            
            # 创建时间轴
            timeline_data = []
            for event in events:
                timeline_data.append({
                    'time': event['relative_time'],
                    'event_type': event['event_type'],
                    'description': str(event['data'])[:50] + '...'
                })
            
            if timeline_data:
                timeline_df = pd.DataFrame(timeline_data)
                
                fig = px.scatter(timeline_df, 
                               x='time', 
                               y='event_type',
                               color='event_type',
                               size=[10] * len(timeline_df),
                               hover_data=['description'],
                               title="用户交互时间轴")
                
                st.plotly_chart(fig, use_container_width=True)
                
                # 逐步回放控制
                if 'replay_index' not in st.session_state:
                    st.session_state.replay_index = 0
                
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    if st.button("⏮️ 上一步"):
                        st.session_state.replay_index = max(0, st.session_state.replay_index - 1)
                
                with col2:
                    if st.button("▶️ 下一步"):
                        st.session_state.replay_index = min(len(events) - 1, st.session_state.replay_index + 1)
                
                with col3:
                    if st.button("⏭️ 最后一步"):
                        st.session_state.replay_index = len(events) - 1
                
                # 显示当前步骤详情
                if 0 <= st.session_state.replay_index < len(events):
                    current_event = events[st.session_state.replay_index]
                    st.write(f"**步骤 {st.session_state.replay_index + 1}/{len(events)}**")
                    st.json(current_event)

# 全局可观测性管理器实例
observability_manager = ObservabilityManager()

# 装饰器用于自动记录函数调用
def trace_function(func):
    """函数追踪装饰器"""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        trace_id = observability_manager.trace_id
        
        # 记录函数调用
        observability_manager.log_event("function_call", {
            "function_name": func.__name__,
            "args": str(args)[:100],
            "kwargs": str(kwargs)[:100]
        })
        
        try:
            result = func(*args, **kwargs)
            duration = (time.time() - start_time) * 1000
            
            observability_manager.log_event("function_return", {
                "function_name": func.__name__,
                "duration_ms": duration,
                "status": "success"
            })
            
            return result
            
        except Exception as e:
            duration = (time.time() - start_time) * 1000
            observability_manager.log_error(
                "function_error", 
                str(e), 
                {
                    "function_name": func.__name__,
                    "duration_ms": duration
                }
            )
            raise
    
    return wrapper