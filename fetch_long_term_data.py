import os
import sys

# Add the project root to sys.path to ensure imports work correctly
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from data_acquisition.stock_data import fetch_single_stock

def main():
    stock_code = '000831'
    start_date = '20100101'
    end_date = '20250701' # Use a recent date
    
    print(f"Fetching long-term data for {stock_code} from {start_date} to {end_date}...")
    
    # Fetch the data
    long_term_df = fetch_single_stock(stock_code, start_date, end_date)
    
    if long_term_df.empty:
        print("Failed to fetch long-term data. Aborting.")
        return

    # Define the output path
    output_dir = 'stock_data'
    os.makedirs(output_dir, exist_ok=True)
    output_path = os.path.join(output_dir, f'{stock_code}_long_term.parquet')
    
    # Save the DataFrame to a parquet file
    # The fetch_single_stock function already sets the date as index, which is what analyze_stock expects.
    try:
        long_term_df.to_parquet(output_path)
        print(f"Successfully saved long-term data to {output_path}")
    except Exception as e:
        print(f"Error saving data to parquet file: {e}")

if __name__ == '__main__':
    main()