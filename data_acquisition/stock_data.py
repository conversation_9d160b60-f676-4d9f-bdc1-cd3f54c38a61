import akshare as ak
import pandas as pd
import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed
from data_acquisition.data_quality import validate_ohlcv, detect_anomalies
import time

def _fetch_data_from_akshare(stock_code: str, period: str, start_date: str, end_date: str) -> pd.DataFrame:
    """Helper function to encapsulate akshare data fetching."""
    return ak.stock_zh_a_hist(symbol=stock_code, period=period,
                              start_date=start_date, end_date=end_date,
                              adjust="qfq")

def fetch_single_stock(stock_code: str, start_date: str, end_date: str, period: str = "daily", retries=3, delay=2) -> pd.DataFrame:
    """
    获取单只股票历史数据，包含重试机制
    
    :param stock_code: 股票代码
    :param start_date: 开始日期 (YYYYMMDD)
    :param end_date: 结束日期 (YYYYMMDD)
    :param period: 数据周期，如 "daily", "weekly", "monthly"
    :param retries: 重试次数
    :param delay: 重试延迟(秒)
    :return: 包含历史数据的DataFrame
    """
    for attempt in range(retries):
        try:
            df = _fetch_data_from_akshare(stock_code, period, start_date, end_date)
            if df.empty:
                print(f"警告: 股票 {stock_code} 无数据 (尝试 {attempt+1}/{retries})")
                continue
                
            # 列名标准化
            df.rename(columns={
                '日期': 'date',
                '开盘': 'open',
                '收盘': 'close',
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume'
            }, inplace=True)
            
            # 数据类型转换
            df['date'] = pd.to_datetime(df['date'])
            df.set_index('date', inplace=True)
            
            # 数据质量验证
            validated_df = validate_ohlcv(df)
            # 异常检测
            df_with_anomalies = detect_anomalies(validated_df)
            # Remove temporary anomaly detection columns before returning
            columns_to_drop = [col for col in ['returns', 'price_anomaly', 'vol_ma', 'volume_anomaly', 'anomaly'] if col in df_with_anomalies.columns]
            df_cleaned = df_with_anomalies.drop(columns=columns_to_drop)
            return df_cleaned
            
        except Exception as e:
            print(f"获取 {stock_code} 出错: {e} (尝试 {attempt+1}/{retries})")
            if attempt < retries - 1:
                time.sleep(delay)
    
    print(f"错误: 股票 {stock_code} 数据获取失败")
    return pd.DataFrame()

def get_stock_history(stock_codes, start_date: str, end_date: str, period: str = "daily", max_workers=5) -> dict:
    """
    获取多只股票历史数据
    
    :param stock_codes: 单个股票代码或代码列表
    :param start_date: 开始日期 (YYYYMMDD)
    :param end_date: 结束日期 (YYYYMMDD)
    :param period: 数据周期，如 "daily", "weekly", "monthly"
    :param max_workers: 最大并发数
    :return: 字典 {股票代码: DataFrame}
    """
    if isinstance(stock_codes, str):
        stock_codes = [stock_codes]
        
    results = {}
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_code = {
            executor.submit(fetch_single_stock, code, start_date, end_date, period): code
            for code in stock_codes
        }
        
        for future in as_completed(future_to_code):
            code = future_to_code[future]
            try:
                df = future.result()
                if not df.empty:
                    results[code] = df
                    print(f"成功获取 {code} 数据: {len(df)} 条记录")
                else:
                    print(f"警告: {code} 返回空数据集")
            except Exception as e:
                print(f"处理 {code} 时发生错误: {e}")
    
    return results

def get_all_stock_codes() -> list:
    """
    获取所有A股股票代码
    """
    try:
        stock_df = ak.stock_zh_a_spot_em()
        return stock_df['代码'].tolist()
    except Exception as e:
        print(f"获取所有股票代码时出错: {e}")
        return []

def save_to_parquet(data: dict, file_path: str):
    """
    Save stock data to a parquet file.
    
    :param data: Dictionary of {stock_code: DataFrame}
    :param file_path: Path to save the parquet file
    """
    if not data:
        print("No data to save.")
        return
        
    try:
        # Combine all dataframes into one with a 'code' column
        all_df = pd.concat([df.assign(code=code) for code, df in data.items()])
        all_df.to_parquet(file_path)
        print(f"Data saved to {file_path}")
    except Exception as e:
        print(f"Failed to save data to parquet: {e}")

if __name__ == '__main__':
    # 测试获取所有A股代码
    all_codes = get_all_stock_codes()
    if all_codes:
        print(f"成功获取 {len(all_codes)} 个A股代码，前10个: {all_codes[:10]}")

    # 测试获取多只股票数据
    stocks = ['600519', '000001', '601318']  # 贵州茅台, 平安银行, 中国平安
    stock_data = get_stock_history(stocks, '20230101', '20231231')
    
    # Save to parquet
    save_to_parquet(stock_data, 'temp_stock_data.parquet')
    
    for code, data in stock_data.items():
        print(f"\n{code} 数据预览 (前5行):")
        print(data.head())
        # This will fail now as anomaly is not in the dataframe anymore
        # print(f"异常检测: {data['anomaly'].sum()} 个异常点")

def get_first_trading_day(stock_symbol: str) -> pd.Timestamp:
    """
    Fetches the earliest available data point for a given stock to find its first trading day.
    """
    try:
        # Fetch a small amount of data from a very early date to get the first available record
        df = ak.stock_zh_a_hist(symbol=stock_symbol, period="daily", start_date="19900101", end_date="20000101", adjust="qfq")
        if not df.empty:
            return pd.to_datetime(df['日期'].iloc[0])
    except Exception as e:
        print(f"Could not determine first trading day for {stock_symbol}: {e}")
    # Return a sensible default if lookup fails
    return pd.to_datetime('2000-01-01')