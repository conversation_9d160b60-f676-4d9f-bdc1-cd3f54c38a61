"""
Data quality assurance and validation checks for financial data
"""

import pandas as pd

def validate_ohlcv(df: pd.DataFrame) -> pd.DataFrame:
    """
    Validate OHLCV data and handle common issues
    - Checks for missing values
    - Ensures chronological ordering
    - Validates OHLC relationships
    - Removes zero-volume days
    """
    # Create a copy to avoid modifying original
    validated_df = df.copy()
    
    # 1. Ensure chronological ordering
    validated_df = validated_df.sort_index()
    
    # 2. Handle missing values
    if validated_df.isnull().sum().sum() > 0:
        print(f"Warning: {validated_df.isnull().sum().sum()} missing values found")
        # Forward fill for OHLC, zero fill for volume
        validated_df[['open', 'high', 'low', 'close']] = validated_df[['open', 'high', 'low', 'close']].ffill()
        validated_df['volume'] = validated_df['volume'].fillna(0)
    
    # 3. Validate price relationships (high >= low, etc.)
    invalid_prices = validated_df.query('high < low or high < open or high < close or low > open or low > close')
    if not invalid_prices.empty:
        print(f"Warning: {len(invalid_prices)} rows with invalid price relationships")
        # Correct by setting high = max(open, close, high), low = min(open, close, low)
        validated_df['high'] = validated_df[['open', 'close', 'high']].max(axis=1)
        validated_df['low'] = validated_df[['open', 'close', 'low']].min(axis=1)
    
    # 4. Remove zero-volume days (market holidays)
    validated_df = validated_df[validated_df['volume'] > 0]
    
    return validated_df

def detect_anomalies(df: pd.DataFrame) -> pd.DataFrame:
    """
    Detect statistical anomalies in price and volume data
    Returns original DataFrame with 'anomaly' flag column
    """
    df['returns'] = df['close'].pct_change()
    
    # Identify price anomalies (3 standard deviations from mean)
    mean_return = df['returns'].mean()
    std_return = df['returns'].std()
    df['price_anomaly'] = abs(df['returns'] - mean_return) > 3 * std_return
    
    # Volume anomalies (volume > 3x 30-day average)
    df['vol_ma'] = df['volume'].rolling(30).mean()
    df['volume_anomaly'] = df['volume'] > 3 * df['vol_ma']
    
    # Combined anomaly flag
    df['anomaly'] = df['price_anomaly'] | df['volume_anomaly']
    return df