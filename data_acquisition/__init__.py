"""
数据获取模块
提供股票数据获取功能
"""

import pandas as pd
from .stock_data import (
    fetch_single_stock,
    get_stock_history,
    get_all_stock_codes,
    save_to_parquet,
    get_first_trading_day
)

# 向后兼容的类名
class StockDataFetcher:
    """股票数据获取器"""
    
    def __init__(self):
        pass
    
    def fetch_stock_data(self, symbol: str, start_date: str, end_date: str, period: str = "daily") -> pd.DataFrame:
        """获取股票数据"""
        return fetch_single_stock(symbol, start_date, end_date, period)
    
    def fetch_multiple_stocks(self, symbols: list, start_date: str, end_date: str, period: str = "daily") -> dict:
        """获取多只股票数据"""
        return get_stock_history(symbols, start_date, end_date, period)

__all__ = [
    'StockDataFetcher',
    'fetch_single_stock',
    'get_stock_history',
    'get_all_stock_codes',
    'save_to_parquet',
    'get_first_trading_day'
]