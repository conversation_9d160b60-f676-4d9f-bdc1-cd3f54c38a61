import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import pandas as pd
from data_acquisition.stock_data import get_stock_history, save_to_parquet
from data_preprocessing.pipeline import preprocess_stock_data
from elliott_wave.pattern_recognition import find_elliott_wave_patterns
from drl_backtesting.backtester import run_drl_backtest
from datetime import datetime

from agents.graph_setup import app # Import the LangGraph app
from database.neo4j_db import Neo4jDB # Import Neo4jDB for initialization

# 导入待实现的分析模块 (当前为占位)
# from elliott_wave_analysis import pattern_recognizer
# from llm_integration import analysis as llm_analysis
# from multi_agent_system import predictor


def run_analysis_pipeline(stock_code: str, preprocessed_df: pd.DataFrame):
    """
    运行单只股票的分析流程。
    """
    print(f"===== 开始分析股票: {stock_code} ====")

    # 步骤 1: 数据预处理 (已在外部完成，直接使用传入的DataFrame)
    print("\n--- 步骤 1: 使用预处理后的数据 ---")
    # preprocessed_df 已经包含了技术指标和归一化数据
    print("数据预览:")
    print(preprocessed_df.head())

    # 步骤 2: 艾略特波浪分析
    print("\n--- 步骤 2: 艾略特波浪分析 ---")
    wave_patterns = find_elliott_wave_patterns(preprocessed_df)
    print(f"已识别出潜在的波浪模式: {wave_patterns}")

    # 步骤 3: DRL 回测
    print("\n--- 步骤 3: DRL 回测 ---")
    backtest_results = run_drl_backtest(preprocessed_df, wave_patterns)
    print(f"回测结果: {backtest_results}")

    print(f"\n===== 股票 {stock_code} 分析完成 ====")
    return {"stock_code": stock_code, "wave_patterns": wave_patterns, "backtest_results": backtest_results}


if __name__ == '__main__':
    # --- 配置参数 ---
    TARGET_STOCK = '000831'  # 中国稀土股票代码
    START_DATE = '20230101'
    END_DATE = datetime.now().strftime('%Y%m%d')

    # Initialize Neo4j DB
    neo4j_db_instance = Neo4jDB()
    if not neo4j_db_instance.driver:
        print("Failed to connect to Neo4j, exiting.")
        exit()

    try:
        print(f"--- 运行多代理分析流程，目标股票: {TARGET_STOCK} ---")
        result = app.invoke({
            "stock_symbol": TARGET_STOCK,
            "timeframe": "daily",
            "messages": [],
            "neo4j_db": neo4j_db_instance
        })
        print("\n=== 代理执行结果 ===")
        print(result.get('report', '未生成报告。'))
    finally:
        neo4j_db_instance.close()
