import warnings
import pandas as pd

file_path = "/Users/<USER>/Projects/stock/ai_elliott/elliott_wave/ml_validator.py"

with open(file_path, 'r') as f:
    lines = f.readlines()

for i, line in enumerate(lines):
    if "import logging" in line:
        lines.insert(i + 1, "import warnings\n")
        break

for i, line in enumerate(lines):
    if "def train_model(self, X: pd.DataFrame, y: pd.Series):" in line:
        lines.insert(i + 2, "        with warnings.catch_warnings():\n")
        lines.insert(i + 3, "            warnings.simplefilter('ignore', category=UserWarning)\n")
        lines.insert(i + 4, "            warnings.simplefilter('ignore', category=FutureWarning)\n")
        break

with open(file_path, 'w') as f:
    f.writelines(lines)

print("Successfully added warning suppression to elliott_wave/ml_validator.py")
