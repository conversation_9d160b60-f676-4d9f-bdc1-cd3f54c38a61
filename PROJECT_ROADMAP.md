# ElliottAgents 项目官方路线图 (Official Project Roadmap)

本文件是指导项目开发的“单一事实来源”(Single Source of Truth)，用于确保开发过程的可持续性和可继承性。

## 第一阶段：架构升级与角色补全 (Architecture Upgrade & Role Completion)

- [x] **1.1. 补全智能体角色 (`agents/graph_setup.py`)**
  - [x] 在 `AgentState` 中添加 `investment_strategy` 和 `final_report` 字段。
  - [x] 创建 `investment_advisor_node` 节点。
  - [x] 创建 `report_writer_node` 节点。
  - [x] 重构 `analysis_expert_node` 的职责。
  - [x] 调整 LangGraph 的边，将七个智能体按论文逻辑连接。

- [x] **1.2. 实现RAG与知识库 (`context7_mcp/rag_integration.py`)**
  - [x] 创建一个基于 `docs/elliott_wave_theory.txt` 的向量知识库。
  - [x] 创建一个 `elliott_wave_theory_rag_tool` 工具。
  - [x] 将RAG工具集成到 `analysis_expert_node` 中。

## 第二阶段：实现持续学习闭环 (Continuous Learning Loop)

- [x] **2.1. 改造Backtester与数据库 (`drl_backtesting/backtester.py`, `database/neo4j_db.py`)**
  - [x] 在 `neo4j_db.py` 中增加 `save_backtest_result()` 函数。
  - [x] 修改 `drl_backtester_node` 以调用上述函数并存储回测结果。
  - [x] 修改 `elliott_wave_analyst_node` 以便能从Neo4j查询历史回测数据作为反馈。

## 第三阶段：完善与最终交付 (Refinement & Final Delivery)

- [x] **3.1. 最终报告与可视化 (`agents/graph_setup.py`, `app.py`)**
  - [x] 增强 `report_writer_node` 以生成高质量的综合报告。
  - [x] 重构 `app.py` 以清晰展示最终报告和所有相关的图表。

- [x] **3.2. 更新所有项目文档 (`docs/`, `project_plan.md`)**
  - [x] 在所有编码任务完成后，全面审查并更新所有项目文档，确保与最终代码一致。

## 下一步 (Next Steps)

- [x] **1. 改进枢轴检测**: 增强 `_find_local_extrema` 函数的鲁棒性，使其能够可靠地识别各种真实世界数据中的峰谷。这可能涉及探索更高级的信号处理或机器学习技术。
- [x] **2. 优化和细化现有模块**: 审查和优化数据获取、模式识别和DRL回测的性能。
- [x] **2. 实现高级艾略特波浪模式**: 扩展 `pattern_recognition.py` 以识别更复杂的调整模式（例如，平台形、三角形，双重/三重三浪）。
  - [x] **2.1. 定义X浪和复杂修正的斐波那契常数**
    - [x] 添加 `X_WAVE_RETRACEMENT_RATIOS` 到常数部分。
  - [x] **2.2. 实现WXY（双重三浪）模式的识别与验证**
    - [x] 在 `pattern_recognition.py` 中添加 `_validate_double_three_correction` 函数。
    - [x] 将WXY模式识别逻辑集成到主函数中。

## 第四阶段：可视化增强与用户体验提升 (Visualization & UX Enhancement)

- [x] **4.1. 增强艾略特波浪可视化 (`elliott_wave/elliott_wave_visualizer.py`)**
  - [x] 实现对 WXY 等复杂模式的图形化绘制和标记。
  - [x] 在图表上清晰地标注出每个波浪的起止点、名称（1, 2, 3, 4, 5, A, B, C, W, X, Y）和关键斐波那契水平。
  - [x] 优化图表的美观度和可读性。

- [x] **4.2. 改进 Streamlit UI (`app.py`)**
  - [x] 在 UI 中添加一个选项，允许用户选择不同的时间框架（如日线、周线、小时线）进行分析。
  - [x] 为检测到的每个模式提供更详细的交互式图表，而不仅仅是最终的报告。
  - [x] 优化日志输出，使其对用户更友好。

## 第五阶段：核心算法优化与未来扩展 (Core Algorithm & Future Expansion)
- [x] **5.1. 实现三重三浪模式 (`pattern_recognition.py`)**
  - [x] 扩展模式识别逻辑，以包含 WXYXZ（三重三浪）模式。

- [x] **5.2. 引入多时间框架分析 (Multi-Timeframe Analysis)**
  - [x] 研究并实现一个新模块，用于关联和验证在不同时间框架（例如，周线和日线）上识别出的波浪模式，以提高分析的准确性。

- [x] **5.3. 探索机器学习辅助识别**
  - [x] 对使用机器学习模型（如 LSTM 或 Transformer）辅助识别或验证波浪模式进行初步研究和实验。
  
## 第六阶段：高级预测与系统成熟 (Advanced Prediction & System Maturation)
- [x] **6.1. 预测路径生成 (Predictive Path Forecasting)**
  - [x] 创建一个新的 `Forecast_Generator_Agent`。
  - [x] 该代理接收确认的波浪计数，并根据斐波那契关系预测未来的价格路径。
- [x] **6.2. 全面文档和代码库审查**
  - [x] 完成分形引擎后，对所有项目文档和代码库进行全面审查，以反映新的、更强大的系统。

## 第七阶段：全面测试与验证 (Comprehensive Testing & Validation) - [x] Done
*目标：确保整个系统的质量、正确性、性能和可靠性。*

## 第八阶段：核心算法增强与验证 (Core Algorithm Enhancement & Validation)
*目标：大幅提升波浪识别的准确性、可靠性和最终分析的盈利能力。*

- **[🔴 核心优先] 8.1: 优化多时间框架波浪识别 (Optimize Multi-Timeframe Wave Recognition):** 波浪计数的准确性是本项目的基石，是当前最关键的任务。
    - **[✅ Done] 8.1.0: 实现分段分析 (Segmented Analysis):** 增加 `_split_df_by_gaps` 函数，以处理长期停牌等造成的数据不连续问题，确保分析的健壮性。
    - **[✅ Done] 8.1.1: 高级峰谷点检测 (Advanced Peak/Trough Detection):** 采用CWT小波变换 (`_find_pivots_cwt`) 作为主要的峰谷点检测方法，并保留ATR方法作为后备，提升了关键转折点识别的准确率。
    - **[🚧 In Progress] 8.1.2: 多假设情景生成 (Multi-Hypothesis Generation):** 重构主函数，使其能够生成、评分并返回多种可能的波浪划分方案，而不再是单一的、固定的结果。**当前问题：`_split_df_by_gaps` 仍然生成过多小片段，导致模式识别失败。**
    - **[🚧 In Progress] 8.1.3: 机器学习驱动的假设评分 (ML-Powered Hypothesis Scoring):** 集成 `ml_validator.py`，根据历史数据为每个生成的情景打分，并按可能性排序。最终报告将呈现置信度最高的1-2个情景。**当前问题：`UndefinedMetricWarning` 持续出现，需要抑制。**
    - **[🚧 In Progress] 8.1.4: 跨时间框架一致性引擎 (Cross-Timeframe Consistency Engine):** 开发并实现了 `multi_timeframe_analysis.py` 中的 `validate_and_score_by_subwaves` 函数，能够根据子浪结构验证和重新评分父浪假设，大幅提升分析可靠性。**当前问题：受限于 8.1.2 的模式识别问题，此功能尚未完全验证。**
    - **[🚧 In Progress] 8.1.5: 改进数据预处理和日志记录 (Improved Data Preprocessing and Logging):** 增加了 `_split_df_by_gaps` 和 `data_engineer_node` 中的日志，以诊断数据连续性问题。**当前问题：日志显示 `_split_df_by_gaps` 仍然生成过多小片段。**

- **[待办] 8.2: 完善DRL回测框架 (Develop DRL Backtesting Framework):**
    - **[✅ Done] 8.2.1: 增强交易环境 (Enhance Trading Environment):** 修改 `drl_backtesting/trading_env.py`，使其能够将艾略特波浪分析结果（包括置信度和模式类型）作为观测空间的一部分，为DRL智能体提供更丰富的决策信息。
    - **[✅ Done] 8.2.2: 执行完整回测 (Full Backtest Execution):** 成功在增强后的交易环境中运行DRL智能体回测，并生成了包括总回报、净值、最大回撤等关键性能指标的报告。

- **[待办] 8.3: 交互式应用与扩展 (Application & Usability):**
    - **[✅ Done] 8.3.1: 构建Streamlit应用 (Build Interactive UI):** 创建了 `app_simplified.py`，实现了用户友好的Web界面，可以直接调用核心分析功能并展示结果。
    - **[✅ Done] 8.3.2: 集成数据库 (Database Integration):** 实现了将分析结果（波浪计数、分数、转折点）存入图数据库（Neo4j）的功能，为未来的大规模模式挖掘做准备。