# ElliottAgents 项目操作使用文档

## 1. 项目概述

ElliottAgents 是一个基于多智能体深度学习方法分析金融市场大数据的项目，专注于艾略特波浪理论。它旨在通过自动化数据获取、预处理、模式识别、强化学习回测以及知识图谱集成，为用户提供深入的金融市场分析和潜在的交易策略建议。

**核心功能模块：**

*   **数据获取 (Data Acquisition)**: 从 Akshare 获取股票历史数据，并进行初步的数据质量验证。
*   **数据预处理 (Data Preprocessing)**: 对原始数据进行特征工程（如添加技术指标）和数据归一化。
*   **艾略特波浪识别 (Elliott Wave Recognition)**: 识别股票价格数据中的艾略特波浪模式，并进行斐波那契比率验证。系统能够识别包括推动浪、锯齿形、平台形、三角形以及WXY双重三浪在内的多种复杂模式。
*   **DRL 回测 (DRL Backtesting)**: 使用强化学习（DRL）模型对识别出的波浪模式进行历史回测，评估其潜在表现。
*   **知识存储 (Knowledge Storage)**: 将分析结果（如模式信息）存储到 Neo4j 图数据库中。
*   **Context7 MCP 集成 (Context7 MCP Integration)**: (占位符) 模拟与外部金融知识 RAG 系统的集成，以丰富分析。
*   **代理系统 (Agent System)**: 使用 LangGraph 框架协调上述各个模块，形成一个端到端的工作流。

## 2. 环境设置

在运行项目之前，请确保您的系统满足以下要求并正确设置环境。

### 2.1. Python 环境

本项目推荐使用 `uv` 进行 Python 环境管理。

1.  **安装 `uv`**:
    如果您尚未安装 `uv`，请通过 `pip` 安装：
    ```bash
    pip install uv
    ```

2.  **创建并激活虚拟环境**:
    在项目根目录下，使用 `uv` 创建并激活虚拟环境：
    ```bash
    uv venv
    source .venv/bin/activate
    ```

3.  **安装依赖**:
    激活环境后，安装 `requirements.txt` 中列出的所有依赖：
    ```bash
    uv pip install -r requirements.txt
    ```

### 2.2. Neo4j 数据库

本项目使用 Neo4j 作为知识图谱数据库。请确保您已安装并运行 Neo4j 数据库。

1.  **安装 Neo4j**:
    您可以从 Neo4j 官方网站下载并安装 Neo4j Desktop，或者使用 Docker 运行 Neo4j 实例。

2.  **配置 `.env` 文件**:
    在项目根目录下，找到 `.env` 文件。确保其中包含 Neo4j 的连接信息。如果 Neo4j 运行在默认配置下，以下配置应该适用：
    ```
    NEO4J_URI=bolt://localhost:7687
    NEO4J_USER=neo4j
    NEO4J_PASSWORD=neo4j
    ```
    如果您的 Neo4j 配置不同，请相应地修改这些值。

## 3. 功能使用

### 3.1. 运行 Web UI

本项目现在提供一个基于 Streamlit 的 Web 用户界面，方便用户进行交互式分析。

1.  **启动 Web 应用**:
    在激活的 Python 虚拟环境下，从项目根目录运行以下命令：
    ```bash
    streamlit run app.py
    ```
    命令执行后，您的浏览器将自动打开一个新的标签页，显示 ElliottAgents 的 Web 界面。

2.  **使用界面**:
    *   在左侧边栏输入您想要分析的**股票代码**（例如：`000831` 代表中国稀土，`600519` 代表贵州茅台）。
    *   选择**开始日期**和**结束日期**。
    *   点击 "**Run Analysis**" 按钮。

3.  **结果展示**:
    界面将按以下步骤显示分析结果：
    *   **数据获取**: 显示正在获取的股票数据。
    *   **艾略特波浪分析**: 显示识别到的潜在波浪模式，并在K线图上进行可视化标记。
    *   **DRL 回测**: 显示 DRL 模型的训练进度和回测结果，包括关键性能指标。

### 3.2. 命令行分析 (高级用户)

对于希望在命令行中运行分析或进行自动化脚本的用户，仍然可以通过 `data_acquisition/main.py` 文件执行核心分析流程。

1.  **修改要分析的股票代码**:
    打开 `data_acquisition/main.py` 文件，找到 `STOCKS_TO_FETCH` 变量。您可以修改此列表以指定您想要分析的股票代码。例如，要分析中国稀土 (000831)：

    ```python
    # --- 配置参数 ---
    STOCKS_TO_FETCH = ['000831']  # 示例股票代码
    START_DATE = '20230101'
    END_DATE = datetime.now().strftime('%Y%m%d')
    ```

2.  **运行脚本**:
    在激活的 Python 虚拟环境下，从项目根目录运行 `main.py` 脚本：
    ```bash
    uv run python -m data_acquisition.main
    ```

3.  **输出说明**:
    脚本将按以下步骤输出信息：
    *   **数据获取**: 显示正在获取的股票代码以及获取到的记录数量。
    *   **数据预处理**: 显示每只股票的预处理进度，以及预处理后的数据预览。
    *   **艾略特波浪分析**: 显示识别到的潜在波浪模式，包括模式名称、类型、置信度、开始/结束日期和描述。
    *   **DRL 回测**: 显示 DRL 模型的训练进度和回测结果，包括验证状态、总奖励、最终净值、盈亏、利润因子、最大回撤和剧集长度。

## 4. 关键模块说明

*   **`app.py`**: Streamlit Web 用户界面的主文件。
*   **`data_acquisition/stock_data.py`**: 负责从 Akshare 获取股票历史数据，并提供多股票获取和数据保存功能。
*   **`data_acquisition/data_quality.py`**: 包含数据质量验证函数，如 OHLCV 验证和异常检测。
*   **`data_preprocessing/pipeline.py`**: 实现数据预处理管道，包括技术指标计算和数据归一化。
*   **`elliott_wave/pattern_recognition.py`**: 包含艾略特波浪模式识别的核心逻辑，包括波峰波谷识别和斐波那契比率验证。
*   **`drl_backtesting/trading_env.py`**: 定义了用于强化学习回测的自定义 Gymnasium 交易环境。
*   **`drl_backtesting/backtester.py`**: 实现了 DRL 模型的训练和回测逻辑，使用 `stable-baselines3` 框架。
*   **`database/neo4j_db.py`**: 负责与 Neo4j 数据库的连接和数据操作，用于存储分析结果。
*   **`context7_mcp/rag_integration.py`**: (占位符) 模拟与外部金融知识 RAG 系统的集成。
*   **`elliott_wave/ml_validator.py`**: 包含用于辅助识别或验证波浪模式的机器学习模型。
*   **`elliott_wave/forecast_generator.py`**: 负责根据确认的波浪计数生成预测价格路径。
*   **`agents/graph_setup.py`**: 使用 LangGraph 框架定义了多智能体工作流的协调逻辑.