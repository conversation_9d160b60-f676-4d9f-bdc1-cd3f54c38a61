def _split_df_by_gaps(df: pd.DataFrame, max_days_gap: int = 10) -> list[pd.DataFrame]:
    """
    Splits a DataFrame into multiple smaller DataFrames if there are significant time gaps.
    This is crucial for handling data with long periods of no trading (e.g., suspensions).

    Args:
        df (pd.DataFrame): Input DataFrame with a DatetimeIndex.
        max_days_gap (int): The maximum number of days allowed in a gap before splitting.

    Returns:
        list[pd.DataFrame]: A list of DataFrame segments.
    """
    logger.info(f"Splitting DataFrame by gaps greater than {max_days_gap} days.")
    
    # Ensure the index is a DatetimeIndex
    if not isinstance(df.index, pd.DatetimeIndex):
        logger.warning("DataFrame index is not a DatetimeIndex. Cannot split by gaps.")
        return [df]

    # Calculate the difference in days between consecutive data points
    gaps = df.index.to_series().diff().dt.days.dropna()

    # Find the indices where the gap is larger than the threshold
    split_indices = gaps[gaps > max_days_gap].index

    if not split_indices.any():
        logger.info("No significant gaps found. Returning the original DataFrame as a single segment.")
        return [df]

    logger.info(f"Found {len(split_indices)} gaps to split on. Splitting DataFrame...")
    
    segments = []
    last_split_point = df.index[0]

    for split_point in split_indices:
        # Get the index location of the split point
        loc = df.index.get_loc(split_point)
        
        # The segment ends *before* the gap
        segment_end_date = df.index[loc - 1]
        
        segment = df.loc[last_split_point:segment_end_date]
        if not segment.empty:
            segments.append(segment)
        
        # The next segment starts *at* the split point
        last_split_point = split_point

    # Add the final segment after the last split point
    final_segment = df.loc[last_split_point:]
    if not final_segment.empty:
        segments.append(final_segment)
        
    logger.info(f"Successfully split DataFrame into {len(segments)} segments.")
    for i, segment in enumerate(segments):
        logger.debug(f"  Segment {i+1}: {len(segment)} rows, from {segment.index.min()} to {segment.index.max()}")

    return segments