"""
实时数据提供商
提供实时股票数据获取和更新功能
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
import logging
import akshare as ak
import pandas as pd
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class DataSource(Enum):
    """数据源枚举"""
    AKSHARE = "akshare"
    SINA = "sina"
    TENCENT = "tencent"
    EASTMONEY = "eastmoney"

@dataclass
class RealtimeQuote:
    """实时报价数据结构"""
    symbol: str
    name: str
    price: float
    change: float
    change_percent: float
    volume: int
    open: float
    high: float
    low: float
    pre_close: float
    timestamp: datetime
    
    @property
    def turnover(self) -> float:
        """成交额（估算）"""
        return self.price * self.volume

class LiveDataProvider:
    """实时数据提供商"""
    
    def __init__(self, 
                 update_interval: int = 5,
                 max_retries: int = 3,
                 timeout: int = 10):
        self.update_interval = update_interval  # 秒
        self.max_retries = max_retries
        self.timeout = timeout
        
        # 订阅管理
        self.subscribers: Dict[str, List[Callable]] = {}
        self.subscribed_symbols: set = set()
        
        # 数据缓存
        self.data_cache: Dict[str, RealtimeQuote] = {}
        self.last_update: Dict[str, datetime] = {}
        
        # 运行状态
        self.is_running = False
        self._update_task = None
    
    async def start(self, symbols: List[str] = None):
        """启动实时数据服务"""
        if self.is_running:
            logger.warning("Live data provider already running")
            return
        
        self.is_running = True
        
        if symbols:
            for symbol in symbols:
                await self.subscribe(symbol)
        
        # 启动更新任务
        self._update_task = asyncio.create_task(self._update_loop())
        logger.info("Live data provider started")
    
    async def stop(self):
        """停止实时数据服务"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        if self._update_task:
            self._update_task.cancel()
            try:
                await self._update_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Live data provider stopped")
    
    async def subscribe(self, symbol: str, callback: Callable = None):
        """订阅股票实时数据"""
        symbol = self._normalize_symbol(symbol)
        self.subscribed_symbols.add(symbol)
        
        if callback:
            if symbol not in self.subscribers:
                self.subscribers[symbol] = []
            self.subscribers[symbol].append(callback)
        
        # 立即获取一次数据
        await self._fetch_single_symbol(symbol)
    
    async def unsubscribe(self, symbol: str, callback: Callable = None):
        """取消订阅"""
        symbol = self._normalize_symbol(symbol)
        
        if symbol in self.subscribers:
            if callback:
                self.subscribers[symbol].remove(callback)
                if not self.subscribers[symbol]:
                    del self.subscribers[symbol]
            else:
                del self.subscribers[symbol]
        
        if symbol in self.subscribed_symbols:
            self.subscribed_symbols.remove(symbol)
    
    async def _update_loop(self):
        """更新循环"""
        while self.is_running:
            try:
                if self.subscribed_symbols:
                    await self._fetch_batch_data()
                
                await asyncio.sleep(self.update_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Update loop error: {e}")
                await asyncio.sleep(self.update_interval)
    
    async def _fetch_batch_data(self):
        """批量获取数据"""
        try:
            # 分批处理，避免API限制
            batch_size = 50
            symbols_list = list(self.subscribed_symbols)
            
            for i in range(0, len(symbols_list), batch_size):
                batch = symbols_list[i:i+batch_size]
                await self._fetch_batch_symbols(batch)
                
        except Exception as e:
            logger.error(f"Batch fetch error: {e}")
    
    async def _fetch_batch_symbols(self, symbols: List[str]):
        """获取一批股票的数据"""
        try:
            # 使用akshare获取实时数据
            if len(symbols) == 1:
                # 单只股票
                data = await self._fetch_single_stock(symbols[0])
            else:
                # 多只股票
                data = await self._fetch_multiple_stocks(symbols)
            
            # 更新缓存并通知订阅者
            for symbol, quote in data.items():
                self.data_cache[symbol] = quote
                self.last_update[symbol] = datetime.now()
                
                # 通知订阅者
                if symbol in self.subscribers:
                    for callback in self.subscribers[symbol]:
                        try:
                            if asyncio.iscoroutinefunction(callback):
                                await callback(quote)
                            else:
                                callback(quote)
                        except Exception as e:
                            logger.error(f"Callback error for {symbol}: {e}")
                            
        except Exception as e:
            logger.error(f"Fetch symbols error: {e}")
    
    async def _fetch_single_stock(self, symbol: str) -> Dict[str, RealtimeQuote]:
        """获取单只股票数据"""
        try:
            # 使用akshare获取实时数据
            stock_zh_a_spot_df = ak.stock_zh_a_spot()
            
            # 查找对应股票
            symbol_clean = symbol.replace('sh', '').replace('sz', '')
            stock_data = stock_zh_a_spot_df[
                stock_zh_a_spot_df['代码'].str.contains(symbol_clean)
            ]
            
            if not stock_data.empty:
                row = stock_data.iloc[0]
                quote = RealtimeQuote(
                    symbol=symbol,
                    name=row['名称'],
                    price=float(row['最新价']),
                    change=float(row['涨跌额']),
                    change_percent=float(row['涨跌幅']),
                    volume=int(row['成交量']),
                    open=float(row['今开']),
                    high=float(row['最高']),
                    low=float(row['最低']),
                    pre_close=float(row['昨收']),
                    timestamp=datetime.now()
                )
                return {symbol: quote}
            
        except Exception as e:
            logger.error(f"Fetch single stock error for {symbol}: {e}")
        
        return {}
    
    async def _fetch_multiple_stocks(self, symbols: List[str]) -> Dict[str, RealtimeQuote]:
        """获取多只股票数据"""
        try:
            # 获取市场快照
            stock_zh_a_spot_df = ak.stock_zh_a_spot()
            
            results = {}
            
            for symbol in symbols:
                symbol_clean = symbol.replace('sh', '').replace('sz', '')
                stock_data = stock_zh_a_spot_df[
                    stock_zh_a_spot_df['代码'].str.contains(symbol_clean)
                ]
                
                if not stock_data.empty:
                    row = stock_data.iloc[0]
                    quote = RealtimeQuote(
                        symbol=symbol,
                        name=row['名称'],
                        price=float(row['最新价']),
                        change=float(row['涨跌额']),
                        change_percent=float(row['涨跌幅']),
                        volume=int(row['成交量']),
                        open=float(row['今开']),
                        high=float(row['最高']),
                        low=float(row['最低']),
                        pre_close=float(row['昨收']),
                        timestamp=datetime.now()
                    )
                    results[symbol] = quote
            
            return results
            
        except Exception as e:
            logger.error(f"Fetch multiple stocks error: {e}")
            return {}
    
    async def _fetch_single_symbol(self, symbol: str):
        """获取单个股票数据（用于订阅时）"""
        try:
            data = await self._fetch_single_stock(symbol)
            for sym, quote in data.items():
                self.data_cache[sym] = quote
                self.last_update[sym] = datetime.now()
        except Exception as e:
            logger.error(f"Fetch single symbol error for {symbol}: {e}")
    
    def get_current_quote(self, symbol: str) -> Optional[RealtimeQuote]:
        """获取当前报价"""
        symbol = self._normalize_symbol(symbol)
        return self.data_cache.get(symbol)
    
    def get_all_quotes(self) -> Dict[str, RealtimeQuote]:
        """获取所有订阅股票的当前报价"""
        return self.data_cache.copy()
    
    def get_last_update_time(self, symbol: str) -> Optional[datetime]:
        """获取最后更新时间"""
        symbol = self._normalize_symbol(symbol)
        return self.last_update.get(symbol)
    
    def is_data_fresh(self, symbol: str, max_age: int = 60) -> bool:
        """检查数据是否新鲜"""
        last_update = self.get_last_update_time(symbol)
        if not last_update:
            return False
        
        return (datetime.now() - last_update).seconds <= max_age
    
    def _normalize_symbol(self, symbol: str) -> str:
        """标准化股票代码"""
        symbol = symbol.upper().strip()
        
        # 处理不同格式的代码
        if symbol.startswith(('6', '5', '9')) and not symbol.startswith(('SH', 'SZ')):
            return f"SH{symbol}"
        elif symbol.startswith(('0', '2', '3')) and not symbol.startswith(('SH', 'SZ')):
            return f"SZ{symbol}"
        
        return symbol
    
    async def get_market_overview(self) -> Dict[str, Any]:
        """获取市场概览"""
        try:
            # 获取主要指数
            index_codes = {
                "上证指数": "sh000001",
                "深证成指": "sz399001",
                "创业板指": "sz399006",
                "科创50": "sh000688"
            }
            
            overview = {}
            for name, code in index_codes.items():
                data = await self._fetch_single_stock(code)
                if data:
                    overview[name] = data[code]
            
            return {
                "indices": overview,
                "timestamp": datetime.now(),
                "total_stocks": len(self.subscribed_symbols)
            }
            
        except Exception as e:
            logger.error(f"Market overview error: {e}")
            return {}
    
    async def get_sector_performance(self) -> Dict[str, Any]:
        """获取板块表现"""
        try:
            # 获取板块数据
            sector_df = ak.stock_board_industry_name_em()
            
            # 计算板块涨跌幅
            performance = {}
            for _, row in sector_df.head(10).iterrows():
                performance[row['板块名称']] = {
                    "change_percent": float(row['涨跌幅']),
                    "volume": int(row['成交量']),
                    "amount": float(row['成交额'])
                }
            
            return {
                "sectors": performance,
                "timestamp": datetime.now()
            }
            
        except Exception as e:
            logger.error(f"Sector performance error: {e}")
            return {}
    
    def get_subscribed_symbols(self) -> List[str]:
        """获取已订阅的股票列表"""
        return list(self.subscribed_symbols)
    
    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return {
            "is_running": self.is_running,
            "subscribed_count": len(self.subscribed_symbols),
            "last_update": self.last_update,
            "cache_size": len(self.data_cache),
            "update_interval": self.update_interval
        }

# 全局实例
live_provider = LiveDataProvider()

# 便捷函数
async def start_live_data(symbols: List[str] = None):
    """启动实时数据服务"""
    await live_provider.start(symbols)

async def stop_live_data():
    """停止实时数据服务"""
    await live_provider.stop()

def subscribe_stock(symbol: str, callback: Callable = None):
    """订阅股票"""
    return live_provider.subscribe(symbol, callback)

def unsubscribe_stock(symbol: str, callback: Callable = None):
    """取消订阅"""
    return live_provider.unsubscribe(symbol, callback)

def get_realtime_quote(symbol: str) -> Optional[RealtimeQuote]:
    """获取实时报价"""
    return live_provider.get_current_quote(symbol)

if __name__ == "__main__":
    async def test():
        # 测试实时数据
        await start_live_data(["000001", "600000"])
        
        # 等待数据更新
        await asyncio.sleep(10)
        
        # 获取状态
        status = live_provider.get_service_status()
        print("服务状态:", status)
        
        # 获取报价
        quote = get_realtime_quote("000001")
        if quote:
            print(f"平安银行: {quote.price} ({quote.change_percent}%)")
        
        await stop_live_data()
    
    asyncio.run(test())