import os
from langchain_community.document_loaders import TextLoader
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_google_genai import GoogleGenerativeAIEmbeddings
from langchain_community.vectorstores import Chroma
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Directory to store ChromaDB persistent collection
CHROMA_DB_DIR = "./chroma_db"

def create_vector_store(file_path: str = "./docs/elliott_wave_theory.txt"):
    """
    Creates and persists a ChromaDB vector store from the given text file.
    """
    if os.path.exists(CHROMA_DB_DIR):
        print(f"ChromaDB already exists at {CHROMA_DB_DIR}. Skipping creation.")
        return Chroma(persist_directory=CHROMA_DB_DIR, embedding_function=GoogleGenerativeAIEmbeddings(model="embedding-001"))

    print(f"Creating ChromaDB from {file_path}...")
    # Load the document
    loader = TextLoader(file_path)
    documents = loader.load()

    # Split the document into chunks
    text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
    splits = text_splitter.split_documents(documents)

    # Initialize embeddings model
    embeddings = GoogleGenerativeAIEmbeddings(model="embedding-001")

    # Create and persist the vector store
    vectorstore = Chroma.from_documents(documents=splits, embedding=embeddings, persist_directory=CHROMA_DB_DIR)
    vectorstore.persist()
    print(f"ChromaDB created and persisted to {CHROMA_DB_DIR}")
    return vectorstore

def retrieve_financial_context(query: str) -> str:
    """
    Retrieves relevant financial context from the vector store based on a query.
    """
    # Ensure the vector store is created/loaded
    vectorstore = create_vector_store()
    
    # Perform similarity search
    docs = vectorstore.similarity_search(query)
    
    # Concatenate relevant document content
    context = "\n\n".join([doc.page_content for doc in docs])
    print(f"Retrieved context for query: '{query}'")
    return context

if __name__ == "__main__":
    # Example usage: Create the vector store and then retrieve context
    vector_db = create_vector_store()
    
    # Test retrieval
    query = "What are the rules for impulsive waves?"
    context = retrieve_financial_context(query)
    print("\n--- Retrieved Context ---")
    print(context)

    query = "Describe corrective wave patterns."
    context = retrieve_financial_context(query)
    print("\n--- Retrieved Context ---")
    print(context)