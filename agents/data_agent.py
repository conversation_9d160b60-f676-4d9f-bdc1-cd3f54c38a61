import pandas as pd
import os
import sys
from datetime import datetime

# Add project root to sys.path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir, os.pardir))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from data_acquisition.stock_data import get_first_trading_day, fetch_single_stock

def fetch_data_node(state: dict) -> dict:
    """An agent node responsible for fetching full historical data for a stock."""
    print("--- Entering Data Analyst Agent Node ---")
    stock_code = state.get('stock_code')
    if not stock_code:
        raise ValueError("Stock code not found in state.")

    print(f"Fetching full history for: {stock_code}")
    
    ipo_date = get_first_trading_day(stock_code)
    if not ipo_date:
        print(f"Could not determine IPO date for {stock_code}. Using default start date.")
        start_date = "19900101"
    else:
        start_date = ipo_date.strftime('%Y%m%d')

    end_date = datetime.now().strftime('%Y%m%d')
    
    data_df = fetch_single_stock(stock_code, start_date, end_date)
    
    if data_df.empty:
        print(f"Warning: No data fetched for {stock_code}")
        return { "data": pd.DataFrame() }

    print(f"Successfully fetched {len(data_df)} data points.")
    
    # The state is immutable, so we return a dictionary with the update
    return { "data": data_df }
