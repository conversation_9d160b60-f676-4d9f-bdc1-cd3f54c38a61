import pandas as pd
import os
import sys
from typing import List, Dict, Any

# Add project root to sys.path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir, os.pardir))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from elliott_wave.forecast_generator import ForecastGenerator
from elliott_wave.similarity_search import SimilaritySearcher

def generate_report_node(state: dict) -> dict:
    """Generates a comprehensive, human-readable analysis report."""
    print("--- Entering Report Writer Agent Node ---")
    
    patterns_data = state.get('patterns')
    stock_code = state.get('stock_code')
    full_df = state.get('data')

    if not patterns_data:
        raise ValueError("Pattern data not found in state.")

    # --- 1. Consolidate and Sort Patterns ---
    all_patterns = []
    for timeframe, hypotheses_dict in patterns_data.items():
        for patterns_list in hypotheses_dict.values():
            for p in patterns_list:
                p['timeframe'] = timeframe
                all_patterns.append(p)
    
    # Sort by end date to find the most recent pattern
    all_patterns.sort(key=lambda p: pd.to_datetime(p['end_date']), reverse=True)

    if not all_patterns:
        report_text = f"No significant Elliott Wave patterns were identified for {stock_code}."
        return { "report": report_text }

    # --- 2. Generate Forecast from the Most Recent Pattern ---
    most_recent_pattern = all_patterns[0]
    forecast_generator = ForecastGenerator()
    forecast = forecast_generator.generate_forecast(most_recent_pattern)

    # --- 3. Find Similar Historical Patterns ---
    similarity_searcher = SimilaritySearcher(full_df)
    similar_patterns = similarity_searcher.find_similar(most_recent_pattern)

    # --- 4. Construct the Report ---
    report_lines = []
    report_lines.append(f"# Elliott Wave Analysis & Forecast for {stock_code}\n")
    
    # A. Current Situation
    report_lines.append("## 1. Current Market Structure")
    mrp = most_recent_pattern
    report_lines.append(f"The most recent significant pattern identified is a **{mrp['pattern_name']}** on the **{mrp['timeframe']}** timeframe, which completed on **{mrp['end_date']}**.")
    report_lines.append("This indicates the market has just finished a corrective/impulsive phase and is poised for its next move.")

    # B. Forecast
    report_lines.append("\n## 2. Future Outlook & Forecast")
    if forecast and forecast.get('targets'):
        report_lines.append(f"Based on the completion of the recent pattern, the system forecasts a **{forecast['forecast_direction']}**.")
        report_lines.append("Key price and time targets are as follows:")
        for i, target in enumerate(forecast['targets']):
            report_lines.append(f"  - **Target {i+1}**: Price **{target['price']}** around **{target['date']}** ({target['type']})")
    else:
        report_lines.append("No high-confidence forecast could be generated at this time.")

    # C. Historical Analogs
    report_lines.append("\n## 3. Historical Similarity Analysis")
    if similar_patterns:
        report_lines.append("To provide historical context, we found past instances where the market shape was highly similar to the current pattern:")
        for i, p in enumerate(similar_patterns):
            report_lines.append(f"  - **Similar Period #{i+1}**: {pd.to_datetime(p['start_date']).strftime('%Y-%m-%d')} to {pd.to_datetime(p['end_date']).strftime('%Y-%m-%d')}")
        report_lines.append("Reviewing the market's behavior after these periods can provide valuable clues for the current forecast.")
    else:
        report_lines.append("No highly similar historical periods were found.")

    final_report = '\n'.join(report_lines)
    print("Successfully generated final report.")
    
    # We also need to pass the forecast and similar patterns for visualization
    return {
        "report": final_report,
        "forecast_data": forecast,
        "similar_patterns": similar_patterns,
        "final_patterns": all_patterns # Pass all patterns for plotting
    }
