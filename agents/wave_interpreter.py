"""
波浪解释器智能体
基于LLM的艾略特波浪理论解释和分析
"""

import json
from typing import Dict, Any, List, Optional
from datetime import datetime
from llm_client import create_llm_client, LLMResponse
import logging

logger = logging.getLogger(__name__)

class WaveInterpreterAgent:
    """波浪解释器智能体"""
    
    def __init__(self, llm_client=None):
        self.llm_client = llm_client or create_llm_client()
        
    async def analyze_wave_pattern(self, 
                                 wave_data: Dict[str, Any],
                                 stock_info: Dict[str, str],
                                 market_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """分析波浪模式"""
        
        # 构建分析提示词
        prompt = self._build_analysis_prompt(wave_data, stock_info, market_context)
        
        # 调用LLM进行分析
        response = await self.llm_client.generate(
            prompt=prompt,
            system_prompt=self._get_system_prompt(),
            temperature=0.3,  # 降低随机性以获得更一致的分析
            max_tokens=2000
        )
        
        # 解析响应
        analysis = self._parse_analysis_response(response.content, wave_data)
        
        return {
            "analysis": analysis,
            "metadata": {
                "model": response.model,
                "tokens": response.usage,
                "cost": response.cost,
                "timestamp": datetime.now().isoformat()
            }
        }
    
    def _build_analysis_prompt(self, 
                             wave_data: Dict[str, Any], 
                             stock_info: Dict[str, str],
                             market_context: Optional[Dict[str, Any]]) -> str:
        """构建分析提示词"""
        
        # 提取关键信息
        current_price = wave_data.get("current_price", 0)
        wave_structure = wave_data.get("wave_structure", {})
        technical_indicators = wave_data.get("technical_indicators", {})
        
        # 构建提示词
        prompt = f"""
请分析以下股票的艾略特波浪模式：

股票信息：
- 代码：{stock_info.get('symbol', '未知')}
- 名称：{stock_info.get('name', '未知')}
- 当前价格：{current_price}
- 时间周期：{wave_data.get('timeframe', '日线')}

波浪结构：
{json.dumps(wave_structure, ensure_ascii=False, indent=2)}

技术指标：
{json.dumps(technical_indicators, ensure_ascii=False, indent=2)}
"""
        
        if market_context:
            prompt += f"""
市场环境：
- 大盘趋势：{market_context.get('market_trend', '中性')}
- 行业表现：{market_context.get('sector_performance', '平均')}
- 成交量：{market_context.get('volume_analysis', '正常')}
"""
        
        prompt += """
请提供以下分析：

1. **当前波浪位置**：判断当前处于哪个浪级和浪型
2. **波浪计数**：提供完整的波浪计数方案
3. **关键价位**：识别重要的支撑和阻力位
4. **趋势判断**：分析当前趋势方向和强度
5. **风险提示**：指出潜在的风险因素
6. **操作建议**：基于波浪理论给出操作建议

请用中文回答，确保分析专业、准确、易懂。
"""
        
        return prompt
    
    def _get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """你是一个专业的艾略特波浪理论分析师，具备以下特质：

1. **专业权威**：精通艾略特波浪理论，能够准确识别各种波浪模式
2. **经验丰富**：具有多年金融市场分析经验，熟悉A股市场特点
3. **客观理性**：基于技术分析，避免主观臆断
4. **风险意识**：始终强调风险管理和资金保护
5. **清晰表达**：用简洁易懂的中文解释复杂的技术概念

分析原则：
- 严格遵循艾略特波浪理论的规则和指引
- 结合成交量、技术指标等多维度验证
- 考虑市场环境和个股基本面因素
- 提供多种可能性分析，避免绝对化判断
- 强调止损和风险控制的重要性

请确保分析的专业性和实用性，帮助投资者做出明智决策。"""
    
    def _parse_analysis_response(self, content: str, wave_data: Dict[str, Any]) -> Dict[str, Any]:
        """解析分析响应"""
        
        # 尝试从响应中提取结构化数据
        analysis = {
            "wave_position": self._extract_section(content, "当前波浪位置"),
            "wave_count": self._extract_section(content, "波浪计数"),
            "key_levels": self._extract_section(content, "关键价位"),
            "trend_analysis": self._extract_section(content, "趋势判断"),
            "risk_warning": self._extract_section(content, "风险提示"),
            "trading_suggestion": self._extract_section(content, "操作建议"),
            "raw_content": content
        }
        
        # 提取价格目标（如果存在）
        price_targets = self._extract_price_targets(content)
        if price_targets:
            analysis["price_targets"] = price_targets
        
        return analysis
    
    def _extract_section(self, content: str, section_name: str) -> str:
        """提取特定章节内容"""
        lines = content.split('\n')
        section_lines = []
        in_section = False
        
        for line in lines:
            if section_name in line:
                in_section = True
                continue
            elif in_section and line.strip().startswith(('1.', '2.', '3.', '4.', '5.', '6.')):
                # 遇到下一个章节
                break
            elif in_section and line.strip():
                section_lines.append(line.strip())
        
        return '\n'.join(section_lines).strip()
    
    def _extract_price_targets(self, content: str) -> List[Dict[str, Any]]:
        """提取价格目标"""
        import re
        
        targets = []
        
        # 匹配价格模式
        price_patterns = [
            r'(\d+(?:\.\d+)?)\s*元?\s*(?:目标|阻力|支撑)',
            r'目标价位?\s*:?\s*(\d+(?:\.\d+)?)',
            r'阻力位?\s*:?\s*(\d+(?:\.\d+)?)',
            r'支撑位?\s*:?\s*(\d+(?:\.\d+)?)'
        ]
        
        for pattern in price_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                try:
                    price = float(match)
                    targets.append({
                        "price": price,
                        "type": "target" if "目标" in pattern else ("resistance" if "阻力" in pattern else "support")
                    })
                except ValueError:
                    continue
        
        return targets
    
    async def generate_summary(self, 
                             analyses: List[Dict[str, Any]], 
                             stock_info: Dict[str, str]) -> Dict[str, Any]:
        """生成综合分析摘要"""
        
        # 收集所有分析
        summary_data = {
            "stock_info": stock_info,
            "analyses": analyses,
            "summary_time": datetime.now().isoformat()
        }
        
        prompt = f"""
请基于以下多个时间周期的波浪分析，生成一个综合的波浪分析报告：

{json.dumps(summary_data, ensure_ascii=False, indent=2)}

请提供：
1. **多周期一致性分析**：各周期波浪计数的协调性
2. **主要趋势判断**：基于多周期的主要趋势方向
3. **关键决策价位**：各周期共同的关键价位
4. **时间窗口预测**：重要转折点的时间预测
5. **综合操作建议**：基于多周期分析的操作策略

请用中文回答，确保分析全面且实用。
"""
        
        response = await self.llm_client.generate(
            prompt=prompt,
            system_prompt=self._get_summary_system_prompt(),
            temperature=0.2,
            max_tokens=1500
        )
        
        return {
            "summary": response.content,
            "metadata": {
                "model": response.model,
                "tokens": response.usage,
                "cost": response.cost
            }
        }
    
    def _get_summary_system_prompt(self) -> str:
        """获取摘要系统提示词"""
        return """你是一个资深的技术分析专家，擅长整合多周期的波浪分析。

请基于多个时间周期的波浪分析结果，提供：
1. **全面性**：整合不同周期的信息，避免片面判断
2. **一致性**：识别各周期分析的共同点和差异
3. **实用性**：给出具体可操作的交易建议
4. **风险平衡**：权衡不同时间周期的风险收益比

确保分析既有技术深度，又具备实战指导价值。"""
    
    async def generate_risk_assessment(self, 
                                     wave_analysis: Dict[str, Any],
                                     market_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成风险评估"""
        
        prompt = f"""
请基于以下波浪分析进行风险评估：

波浪分析：
{json.dumps(wave_analysis, ensure_ascii=False, indent=2)}

市场数据：
{json.dumps(market_data, ensure_ascii=False, indent=2)}

请评估以下风险因素：
1. **技术风险**：波浪计数错误的可能性
2. **市场风险**：市场环境变化的影响
3. **时间风险**：时间窗口预测的不确定性
4. **资金管理**：建议的资金配置比例
5. **止损设置**：合理的止损位置

请用中文回答，重点强调风险控制。
"""
        
        response = await self.llm_client.generate(
            prompt=prompt,
            system_prompt="你是一个风险管理专家，专注于金融市场的风险控制。请客观评估各种风险因素，提供实用的风险管理建议。",
            temperature=0.3,
            max_tokens=1000
        )
        
        return {
            "risk_assessment": response.content,
            "metadata": {
                "model": response.model,
                "tokens": response.usage,
                "cost": response.cost
            }
        }