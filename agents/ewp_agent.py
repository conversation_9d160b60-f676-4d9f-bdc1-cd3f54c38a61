import pandas as pd
import os
import sys
import logging

# Add project root to sys.path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir, os.pardir))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from elliott_wave.pattern_recognition import find_elliott_wave_patterns
from elliott_wave.ml_validator import MLValidator
from elliott_wave.multi_timeframe_analysis import validate_and_score_by_subwaves

def analyze_waves_node(state: dict) -> dict:
    """An agent node responsible for running the Elliott Wave pattern analysis."""
    print("--- Entering EWP Analyst Agent Node ---")
    data_df = state.get('data')
    if data_df is None or data_df.empty:
        raise ValueError("Dataframe not found in state.")

    # In a real multi-agent system, the ML model might be a shared resource.
    # For now, we load it within the agent node for simplicity.
    ml_validator = MLValidator()
    model_path = "drl_logs/ml_validator_model.joblib"
    if os.path.exists(model_path):
        ml_validator.load_model(model_path)
        print("ML Validator model loaded.")
    else:
        print("Warning: ML Validator model not found.")
        ml_validator = None

    # --- Run analysis on multiple timeframes ---
    # To keep the agent focused, we'll analyze weekly and monthly data here.
    daily_df = data_df.copy()
    weekly_df = daily_df.resample('W').agg({
        'open': 'first', 'high': 'max', 'low': 'min', 'close': 'last', 'volume': 'sum'
    }).dropna()
    monthly_df = daily_df.resample('ME').agg({
        'open': 'first', 'high': 'max', 'low': 'min', 'close': 'last', 'volume': 'sum'
    }).dropna()

    all_timeframes_data = {
        'weekly': weekly_df,
        'monthly': monthly_df
    }

    raw_hypotheses = {}
    for timeframe, df_timeframe in all_timeframes_data.items():
        print(f"--- Analyzing {timeframe} data ---")
        patterns_result = find_elliott_wave_patterns(df_timeframe, ml_validator=ml_validator)
        raw_hypotheses[timeframe] = patterns_result.get("pattern_hypotheses", {})
    
    # Perform MTA on monthly patterns using weekly sub-waves
    print("--- Performing Macro Multi-Timeframe Analysis (Monthly vs. Weekly) ---")
    raw_hypotheses['monthly'] = validate_and_score_by_subwaves(
        parent_hypotheses=raw_hypotheses.get('monthly', {}),
        full_df=all_timeframes_data['weekly'],
        ml_validator=ml_validator
    )
    print(f"EWP analysis complete. Found {sum(len(v) for tf in raw_hypotheses.values() for v in tf.values())} total patterns.")
    return { "patterns": raw_hypotheses }
