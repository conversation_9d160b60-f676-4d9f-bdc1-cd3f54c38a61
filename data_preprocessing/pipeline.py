"""
Data preprocessing pipeline for financial time series
"""
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split

def add_technical_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """
    Add technical indicators to OHLCV data
    Returns DataFrame with additional columns
    """
    df = df.copy()
    
    # 1. Simple Moving Averages
    df['SMA_20'] = df['close'].rolling(window=20).mean()
    df['SMA_50'] = df['close'].rolling(window=50).mean()
    
    # 2. Exponential Moving Averages
    df['EMA_12'] = df['close'].ewm(span=12, adjust=False).mean()
    df['EMA_26'] = df['close'].ewm(span=26, adjust=False).mean()
    
    # 3. MACD
    df['MACD'] = df['EMA_12'] - df['EMA_26']
    df['MACD_signal'] = df['MACD'].ewm(span=9, adjust=False).mean()
    df['MACD_hist'] = df['MACD'] - df['MACD_signal']
    
    # 4. RSI
    delta = df['close'].diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    avg_gain = gain.rolling(window=14).mean()
    avg_loss = loss.rolling(window=14).mean()
    rs = avg_gain / avg_loss
    df['RSI'] = 100 - (100 / (1 + rs))
    
    # 5. Bollinger Bands
    df['BB_MA'] = df['close'].rolling(window=20).mean()
    df['BB_std'] = df['close'].rolling(window=20).std()
    df['BB_upper'] = df['BB_MA'] + 2 * df['BB_std']
    df['BB_lower'] = df['BB_MA'] - 2 * df['BB_std']
    
    # 6. Volume features
    df['volume_pct_change'] = df['volume'].pct_change()
    df['volume_ma_10'] = df['volume'].rolling(window=10).mean()
    
    # Fill initial NaN values
    return df.bfill().dropna()

def create_lagged_features(df: pd.DataFrame, lags=5) -> pd.DataFrame:
    """
    Create lagged features for time series forecasting
    """
    df = df.copy()
    for i in range(1, lags + 1):
        df[f'close_lag_{i}'] = df['close'].shift(i)
        df[f'volume_lag_{i}'] = df['volume'].shift(i)
    
    # Forward fill initial missing values
    return df.ffill().dropna()

def normalize_data(df: pd.DataFrame, method='standard') -> (pd.DataFrame, object):
    """
    Normalize dataset with option to preserve scaler for inverse transform
    Returns normalized DataFrame and scaler object
    """
    if df.empty:
        return pd.DataFrame(), None

    if method == 'standard':
        scaler = StandardScaler()
    elif method == 'minmax':
        scaler = MinMaxScaler(feature_range=(-1, 1))
    else:
        raise ValueError("Invalid method. Choose 'standard' or 'minmax'")
    
    # Save column names and index
    columns = df.columns
    index = df.index
    
    # Fit and transform
    normalized = scaler.fit_transform(df)
    
    # Reconstruct DataFrame
    return pd.DataFrame(normalized, columns=columns, index=index), scaler

def create_sequences(data: np.array, seq_length: int, target_length=1):
    """
    Create sequences and targets for time series forecasting
    Returns X (samples, seq_length, features) and y (samples, target_length)
    """
    X, y = [], []
    for i in range(len(data) - seq_length - target_length + 1):
        X.append(data[i:i+seq_length])
        y.append(data[i+seq_length:i+seq_length+target_length, 0])  # Assume close price is first column
    return np.array(X), np.array(y)

def train_val_test_split(df: pd.DataFrame, val_size=0.15, test_size=0.15):
    """
    Time-series aware train/validation/test split
    Returns (train, val, test) DataFrames
    """
    n = len(df)
    test_end = n
    test_start = n - int(n * test_size)
    val_end = test_start
    val_start = val_end - int(n * val_size)
    
    train = df.iloc[:val_start]
    val = df.iloc[val_start:val_end]
    test = df.iloc[test_start:test_end]
    
    return train, val, test

def preprocess_stock_data(stock_data: dict) -> dict:
    """
    Applies full preprocessing pipeline to a dictionary of stock DataFrames.
    
    :param stock_data: Dictionary of {stock_code: DataFrame}
    :return: Dictionary of {stock_code: preprocessed_DataFrame}
    """
    preprocessed_data = {}
    for code, df in stock_data.items():
        print(f"Preprocessing data for {code}...")
        # Add technical indicators
        df_with_indicators = add_technical_indicators(df)
        
        if df_with_indicators.empty:
            print(f"Warning: {code} DataFrame became empty after adding technical indicators. Skipping normalization.")
            preprocessed_data[code] = pd.DataFrame() # Store an empty DataFrame
            continue

        # Normalize data
        # We will normalize each stock's data independently for now.
        # Scaler is not returned as it's not needed for this stage.
        normalized_df, _ = normalize_data(df_with_indicators)
        
        if normalized_df.empty:
            print(f"Warning: {code} DataFrame became empty after normalization. Skipping further processing for this stock.")
            preprocessed_data[code] = pd.DataFrame() # Store an empty DataFrame
            continue

        preprocessed_data[code] = normalized_df
        print(f"Finished preprocessing for {code}.")
    return preprocessed_data