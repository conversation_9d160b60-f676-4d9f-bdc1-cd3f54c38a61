import streamlit as st
import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from langchain_core.messages import HumanMessage
from agents.graph_setup import app as elliott_agent_app
from data_acquisition.stock_data import get_first_trading_day
from elliott_wave.elliott_wave_visualizer import plot_elliott_wave_patterns
from datetime import datetime

# --- UI Configuration ---
st.set_page_config(layout="wide", page_title="ElliottAgents - AI Stock Analysis")
st.title("ElliottAgents: AI-Powered Elliott Wave Analysis")
st.markdown("Powered by LangGraph & Gemini")


# --- Sidebar for User Inputs ---
st.sidebar.header("Input Parameters")
stock_symbol = st.sidebar.text_input("Stock Symbol (e.g., 000831, 600519)", "000831")

# Get the first trading day for the selected stock
# We use a simple cache to avoid re-fetching this every time the script reruns
@st.cache_data
def cached_get_first_day(symbol):
    return get_first_trading_day(symbol)

first_day = cached_get_first_day(stock_symbol)

start_date = st.sidebar.date_input("Start Date", value=first_day, min_value=first_day)
end_date = st.sidebar.date_input("End Date", value=datetime.now())


run_analysis_button = st.sidebar.button("Run Analysis")

# --- Main Content Area ---
if run_analysis_button:
    st.subheader(f"Analyzing {stock_symbol} from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")

    inputs = {
        "stock_symbol": stock_symbol,
        "start_date": start_date.strftime('%Y%m%d'),
        "end_date": end_date.strftime('%Y%m%d'),
        "timeframe": "daily", # Assuming daily for now
        "messages": [HumanMessage(content=f"Analyze stock {stock_symbol}")]
    }

    with st.container():
        st.subheader("Agent Execution Log")
        log_container = st.empty()
        log_messages = []
        final_result = None

        with st.spinner("Agents are at work..."):
            for output in elliott_agent_app.stream(inputs, stream_mode="values"):
                node_name = list(output.keys())[0]
                log_messages.append(f"- Executing Agent: `{node_name}`")
                log_container.markdown("\n".join(log_messages))
                final_result = output

    st.success("Agent workflow finished!")

    # Display final results
    if final_result:
        st.subheader("Final Analysis Report")
        final_report = final_result.get('final_report', "No final report generated.")
        st.markdown(final_report)

        # --- Display AI Decision Process ---
        st.subheader("AI Analyst's Decision Process")
        with st.expander("Show Details"):
            st.markdown("**Chief Analyst (LLM) Justification:**")
            st.info(final_result.get('llm_justification', "No justification was provided."))

            st.markdown("**ML Validator Confidence Score:**")
            ml_confidence = final_result.get('ml_confidence', 0.0)
            st.progress(ml_confidence, text=f"{ml_confidence:.2f}")
            
            st.markdown("**Junior Analyst (Algorithm) Candidates:**")
            
            all_candidates = []
            candidates_dict = final_result.get('pattern_candidates', {})
            if isinstance(candidates_dict, dict):
                # Handle the nested structure of {'weekly': [...], 'daily_subwaves': {...}}
                if 'weekly' in candidates_dict:
                    weekly_pattern = candidates_dict['weekly']
                    weekly_pattern['timeframe'] = 'weekly'
                    all_candidates.append(weekly_pattern)

                if 'daily_subwaves' in candidates_dict:
                    for subwave, patterns in candidates_dict['daily_subwaves'].items():
                        for p in patterns:
                            p['timeframe'] = f"daily ({subwave})"
                            all_candidates.append(p)
            
            if all_candidates:
                candidates_df = pd.DataFrame(all_candidates)
                # Display relevant columns, handling potential missing 'points'
                display_cols = ['timeframe', 'pattern_name', 'confidence', 'start_date', 'end_date']
                existing_cols = [col for col in display_cols if col in candidates_df.columns]
                st.dataframe(candidates_df[existing_cols])
            else:
                st.write("No candidates were generated by the algorithm.")

        df_result = final_result.get('df')
        patterns = final_result.get('patterns')
        backtest_results = final_result.get('backtest_results')
        pattern_candidates = final_result.get('pattern_candidates', {})

        # --- Robustly determine the dataframe to use for visualization ---
        df_for_viz = None
        daily_df_for_ui = pd.DataFrame()

        if isinstance(df_result, dict):
            df_for_viz = df_result
            daily_df_for_ui = df_result.get('daily', pd.DataFrame())
        elif isinstance(df_result, pd.DataFrame):
            # In fallback cases, the result might be a single dataframe.
            df_for_viz = {'daily': df_result} # Wrap it for the visualizer
            daily_df_for_ui = df_result

        if not daily_df_for_ui.empty:
            # Generate and display the charts using the new visualizer function signature
            main_fig, net_worth_fig = plot_elliott_wave_patterns(df_for_viz, pattern_candidates, backtest_results, stock_symbol)
            st.plotly_chart(main_fig, use_container_width=True)

            if backtest_results and backtest_results.get('backtest_results'):
                st.subheader("DRL回测指标")
                st.dataframe(pd.DataFrame([backtest_results.get('backtest_results', {})]))
                if net_worth_fig:
                    st.plotly_chart(net_worth_fig, use_container_width=True)
            
            # --- Display Forecast ---
            forecast = final_result.get('forecast', {})
            if forecast and forecast.get('targets'):
                st.subheader("价格预测")
                st.metric(label="方向", value=forecast.get('direction', 'N/A').upper())
                
                targets = forecast.get('targets', {})
                cols = st.columns(len(targets))
                for i, (level, price) in enumerate(targets.items()):
                    cols[i].metric(label=level, value=f"¥{price:,.2f}")

                # Re-display the chart with forecast lines
                st.subheader("包含预测目标的图表")
                for level, price in targets.items():
                    main_fig.add_hline(y=price, line_dash="dash", line_color="purple",
                                      annotation_text=f"目标: {level}", 
                                      annotation_position="bottom right",
                                      annotation_font_color="purple")
                st.plotly_chart(main_fig, use_container_width=True)
            
        else:
            st.warning("无法检索到足够的数据进行可视化。")

else:
    st.info("Enter stock symbol and dates, then click 'Run Analysis' to start.")