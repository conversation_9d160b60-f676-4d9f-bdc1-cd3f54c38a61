2025-07-08 21:17:39,957 - INFO - Data for 000831 is already up to date.
2025-07-08 21:17:39,972 - INFO - Data Engineer: Weekly DataFrame head:
            open  high   low  close  volume
date                                       
2000-04-23  2.82  2.92  2.81   2.82   34792
2000-04-30  2.82  2.86  2.67   2.74   61158
2000-05-14  2.77  2.78  2.50   2.50   36313
2000-05-21  2.50  2.60  2.47   2.58   24042
2000-05-28  2.59  2.77  2.58   2.71   51439
2025-07-08 21:17:39,975 - INFO - Data Engineer: Weekly DataFrame index diffs:
date
2000-04-23        NaT
2000-04-30     7 days
2000-05-14    14 days
2000-05-21     7 days
2000-05-28     7 days
2000-06-04     7 days
2000-06-11     7 days
2000-06-18     7 days
2000-06-25     7 days
2000-07-02     7 days
2000-07-09     7 days
2000-07-16     7 days
2000-07-23     7 days
2000-07-30     7 days
2000-08-06     7 days
2000-08-13     7 days
2000-08-20     7 days
2000-08-27     7 days
2000-09-03     7 days
2000-09-10     7 days
2000-09-17     7 days
2000-09-24     7 days
2000-10-01     7 days
2000-10-15    14 days
2000-10-22     7 days
2000-10-29     7 days
2000-11-05     7 days
2000-11-12     7 days
2000-11-19     7 days
2000-11-26     7 days
2000-12-03     7 days
2000-12-10     7 days
2000-12-17     7 days
2000-12-24     7 days
2000-12-31     7 days
2001-01-07     7 days
2001-01-14     7 days
2001-01-21     7 days
2001-02-11    21 days
2001-02-18     7 days
2001-02-25     7 days
2001-03-04     7 days
2001-03-11     7 days
2001-03-18     7 days
2001-03-25     7 days
2001-04-01     7 days
2001-04-08     7 days
2001-04-15     7 days
2001-04-22     7 days
2001-04-29     7 days
2001-05-06     7 days
2001-05-13     7 days
2001-05-20     7 days
2001-05-27     7 days
2001-06-03     7 days
2001-06-10     7 days
2001-06-17     7 days
2001-06-24     7 days
2001-07-01     7 days
2001-07-08     7 days
2001-07-15     7 days
2001-07-22     7 days
2001-07-29     7 days
2001-08-05     7 days
2001-08-12     7 days
2001-08-19     7 days
2001-08-26     7 days
2001-09-02     7 days
2001-09-09     7 days
2001-09-16     7 days
2001-09-23     7 days
2001-09-30     7 days
2001-10-14    14 days
2001-10-21     7 days
2001-10-28     7 days
2001-11-04     7 days
2001-11-11     7 days
2001-11-18     7 days
2001-11-25     7 days
2001-12-02     7 days
2001-12-09     7 days
2001-12-16     7 days
2001-12-23     7 days
2001-12-30     7 days
2002-01-06     7 days
2002-01-13     7 days
2002-01-20     7 days
2002-01-27     7 days
2002-02-03     7 days
2002-02-10     7 days
2002-03-03    21 days
2002-03-10     7 days
2002-03-17     7 days
2002-03-24     7 days
2002-03-31     7 days
2002-04-07     7 days
2002-04-14     7 days
2002-04-21     7 days
2002-04-28     7 days
2002-05-05     7 days
2002-05-12     7 days
2002-05-19     7 days
2002-05-26     7 days
2002-06-02     7 days
2002-06-09     7 days
2002-06-16     7 days
2002-06-23     7 days
2002-06-30     7 days
2002-07-07     7 days
2002-07-14     7 days
2002-07-21     7 days
2002-07-28     7 days
2002-08-04     7 days
2002-08-11     7 days
2002-08-18     7 days
2002-08-25     7 days
2002-09-01     7 days
2002-09-08     7 days
2002-09-15     7 days
2002-09-22     7 days
2002-09-29     7 days
2002-10-13    14 days
2002-10-20     7 days
2002-10-27     7 days
2002-11-03     7 days
2002-11-10     7 days
2002-11-17     7 days
2002-11-24     7 days
2002-12-01     7 days
2002-12-08     7 days
2002-12-15     7 days
2002-12-22     7 days
2002-12-29     7 days
2003-01-05     7 days
2003-01-12     7 days
2003-01-19     7 days
2003-01-26     7 days
2003-02-02     7 days
2003-02-16    14 days
2003-02-23     7 days
2003-03-02     7 days
2003-03-09     7 days
2003-03-16     7 days
2003-03-23     7 days
2003-03-30     7 days
2003-04-06     7 days
2003-04-13     7 days
2003-04-20     7 days
2003-04-27     7 days
2003-05-04     7 days
2003-05-18    14 days
2003-05-25     7 days
2003-06-01     7 days
2003-06-08     7 days
2003-06-15     7 days
2003-06-22     7 days
2003-06-29     7 days
2003-07-06     7 days
2003-07-13     7 days
2003-07-20     7 days
2003-07-27     7 days
2003-08-03     7 days
2003-08-10     7 days
2003-08-17     7 days
2003-08-24     7 days
2003-08-31     7 days
2003-09-07     7 days
2003-09-14     7 days
2003-09-21     7 days
2003-09-28     7 days
2003-10-05     7 days
2003-10-12     7 days
2003-10-19     7 days
2003-10-26     7 days
2003-11-02     7 days
2003-11-09     7 days
2003-11-16     7 days
2003-11-23     7 days
2003-11-30     7 days
2003-12-07     7 days
2003-12-14     7 days
2003-12-21     7 days
2003-12-28     7 days
2004-01-04     7 days
2004-01-11     7 days
2004-01-18     7 days
2004-02-01    14 days
2004-02-08     7 days
2004-02-15     7 days
2004-02-22     7 days
2004-02-29     7 days
2004-03-07     7 days
2004-03-14     7 days
2004-03-21     7 days
2004-03-28     7 days
2004-04-04     7 days
2004-04-11     7 days
2004-04-18     7 days
2004-04-25     7 days
2004-05-02     7 days
2004-05-16    14 days
2004-05-23     7 days
2004-05-30     7 days
2004-06-06     7 days
2004-06-13     7 days
2004-06-20     7 days
2004-06-27     7 days
2004-07-04     7 days
2004-07-11     7 days
2004-07-18     7 days
2004-07-25     7 days
2004-08-01     7 days
2004-08-08     7 days
2004-08-15     7 days
2004-08-22     7 days
2004-08-29     7 days
2004-09-05     7 days
2004-09-12     7 days
2004-09-19     7 days
2004-09-26     7 days
2004-10-03     7 days
2004-10-10     7 days
2004-10-17     7 days
2004-10-24     7 days
2004-10-31     7 days
2004-11-07     7 days
2004-11-14     7 days
2004-11-21     7 days
2004-11-28     7 days
2004-12-05     7 days
2004-12-12     7 days
2004-12-19     7 days
2004-12-26     7 days
2005-01-02     7 days
2005-01-09     7 days
2005-01-16     7 days
2005-01-23     7 days
2005-01-30     7 days
2005-02-06     7 days
2005-02-20    14 days
2005-02-27     7 days
2005-03-06     7 days
2005-03-13     7 days
2005-03-20     7 days
2005-03-27     7 days
2005-04-03     7 days
2005-04-10     7 days
2005-04-17     7 days
2005-04-24     7 days
2005-05-01     7 days
2005-05-15    14 days
2005-05-22     7 days
2005-05-29     7 days
2005-06-05     7 days
2005-06-12     7 days
2005-06-19     7 days
2005-06-26     7 days
2005-07-03     7 days
2005-07-10     7 days
2005-07-17     7 days
2005-07-24     7 days
2005-07-31     7 days
2005-08-07     7 days
2005-08-14     7 days
2005-08-21     7 days
2005-08-28     7 days
2005-09-04     7 days
2005-09-11     7 days
2005-09-18     7 days
2005-09-25     7 days
2005-10-02     7 days
2005-10-16    14 days
2005-10-23     7 days
2005-10-30     7 days
2005-11-06     7 days
2005-11-13     7 days
2005-11-20     7 days
2005-11-27     7 days
2005-12-04     7 days
2005-12-11     7 days
2005-12-18     7 days
2005-12-25     7 days
2006-01-01     7 days
2006-01-08     7 days
2006-01-15     7 days
2006-01-22     7 days
2006-01-29     7 days
2006-02-12    14 days
2006-02-19     7 days
2006-02-26     7 days
2006-03-05     7 days
2006-03-19    14 days
2006-03-26     7 days
2006-04-16    21 days
2006-04-23     7 days
2006-04-30     7 days
2006-05-14    14 days
2006-05-21     7 days
2006-05-28     7 days
2006-06-04     7 days
2006-06-11     7 days
2006-06-18     7 days
2006-06-25     7 days
2006-07-02     7 days
2006-07-09     7 days
2006-07-16     7 days
2006-07-23     7 days
2006-07-30     7 days
2006-08-06     7 days
2006-08-13     7 days
2006-08-20     7 days
2006-08-27     7 days
2006-09-03     7 days
2006-09-10     7 days
2006-09-17     7 days
2006-09-24     7 days
2006-10-01     7 days
2006-10-15    14 days
2006-10-22     7 days
2006-10-29     7 days
2006-11-05     7 days
2006-11-12     7 days
2006-11-19     7 days
2006-11-26     7 days
2006-12-03     7 days
2006-12-10     7 days
2006-12-17     7 days
2006-12-24     7 days
2006-12-31     7 days
2007-01-07     7 days
2007-01-14     7 days
2007-01-21     7 days
2007-01-28     7 days
2007-02-04     7 days
2007-02-11     7 days
2007-02-18     7 days
2007-03-04    14 days
2007-03-11     7 days
2007-03-18     7 days
2007-03-25     7 days
2007-04-01     7 days
2007-04-08     7 days
2007-04-15     7 days
2007-04-22     7 days
2007-04-29     7 days
2007-05-06     7 days
2007-05-13     7 days
2007-05-20     7 days
2007-05-27     7 days
2007-06-03     7 days
2007-06-10     7 days
2007-06-17     7 days
2007-06-24     7 days
2007-07-01     7 days
2007-07-08     7 days
2007-07-15     7 days
2007-07-22     7 days
2007-07-29     7 days
2007-08-05     7 days
2007-08-12     7 days
2007-08-19     7 days
2007-08-26     7 days
2007-09-02     7 days
2007-09-09     7 days
2007-09-16     7 days
2007-09-23     7 days
2007-09-30     7 days
2007-10-14    14 days
2007-10-21     7 days
2007-10-28     7 days
2007-11-04     7 days
2007-11-11     7 days
2007-11-18     7 days
2007-11-25     7 days
2007-12-02     7 days
2007-12-09     7 days
2007-12-16     7 days
2007-12-23     7 days
2007-12-30     7 days
2008-01-06     7 days
2008-01-13     7 days
2008-01-20     7 days
2008-01-27     7 days
2008-02-03     7 days
2008-02-10     7 days
2008-02-17     7 days
2008-02-24     7 days
2008-03-02     7 days
2008-03-09     7 days
2008-03-16     7 days
2008-03-23     7 days
2008-03-30     7 days
2008-04-06     7 days
2008-04-13     7 days
2008-04-20     7 days
2008-04-27     7 days
2008-05-04     7 days
2008-05-11     7 days
2008-05-18     7 days
2008-05-25     7 days
2008-06-01     7 days
2008-06-08     7 days
2008-06-15     7 days
2008-06-22     7 days
2008-06-29     7 days
2008-07-06     7 days
2008-07-13     7 days
2008-07-20     7 days
2008-07-27     7 days
2008-08-03     7 days
2008-08-10     7 days
2008-08-17     7 days
2008-08-24     7 days
2008-08-31     7 days
2008-09-07     7 days
2008-09-14     7 days
2008-09-21     7 days
2008-09-28     7 days
2008-10-12    14 days
2008-10-19     7 days
2008-10-26     7 days
2008-11-02     7 days
2008-11-09     7 days
2008-11-16     7 days
2008-11-23     7 days
2008-11-30     7 days
2008-12-07     7 days
2008-12-14     7 days
2008-12-21     7 days
2008-12-28     7 days
2009-01-04     7 days
2009-01-11     7 days
2009-01-18     7 days
2009-01-25     7 days
2009-02-08    14 days
2009-02-15     7 days
2009-02-22     7 days
2009-03-01     7 days
2009-03-08     7 days
2009-03-15     7 days
2009-03-22     7 days
2009-03-29     7 days
2009-04-05     7 days
2009-04-12     7 days
2009-04-19     7 days
2009-04-26     7 days
2009-05-03     7 days
2009-05-10     7 days
2009-05-17     7 days
2009-05-24     7 days
2009-05-31     7 days
2009-06-07     7 days
2009-06-14     7 days
2009-06-21     7 days
2009-06-28     7 days
2009-07-05     7 days
2009-07-12     7 days
2009-07-19     7 days
2009-07-26     7 days
2009-08-02     7 days
2009-08-09     7 days
2009-08-16     7 days
2009-08-23     7 days
2009-08-30     7 days
2009-09-06     7 days
2009-09-13     7 days
2009-09-20     7 days
2009-09-27     7 days
2009-10-04     7 days
2009-10-11     7 days
2009-10-18     7 days
2009-10-25     7 days
2009-11-01     7 days
2009-11-08     7 days
2009-11-15     7 days
2009-11-22     7 days
2009-11-29     7 days
2009-12-06     7 days
2009-12-13     7 days
2009-12-20     7 days
2009-12-27     7 days
2010-01-03     7 days
2010-01-10     7 days
2010-01-17     7 days
2010-01-24     7 days
2010-01-31     7 days
2010-02-07     7 days
2010-02-14     7 days
2010-02-28    14 days
2010-03-07     7 days
2010-03-14     7 days
2010-03-21     7 days
2010-03-28     7 days
2010-04-04     7 days
2010-04-11     7 days
2010-04-18     7 days
2010-04-25     7 days
2010-05-02     7 days
2010-05-09     7 days
2010-05-16     7 days
2010-05-23     7 days
2010-05-30     7 days
2010-06-06     7 days
2010-06-13     7 days
2010-06-20     7 days
2010-06-27     7 days
2010-07-04     7 days
2010-07-11     7 days
2010-07-18     7 days
2010-07-25     7 days
2010-08-01     7 days
2010-08-08     7 days
2010-08-15     7 days
2010-08-22     7 days
2010-08-29     7 days
2010-09-05     7 days
2010-09-12     7 days
2010-09-19     7 days
2010-09-26     7 days
2010-10-03     7 days
2010-10-10     7 days
2010-10-17     7 days
2010-10-24     7 days
2010-10-31     7 days
2010-11-07     7 days
2010-11-14     7 days
2010-11-21     7 days
2010-11-28     7 days
2010-12-05     7 days
2010-12-12     7 days
2010-12-19     7 days
2010-12-26     7 days
2011-01-02     7 days
2011-01-09     7 days
2011-01-16     7 days
2011-01-23     7 days
2011-01-30     7 days
2011-02-06     7 days
2011-02-13     7 days
2011-02-20     7 days
2011-02-27     7 days
2011-03-06     7 days
2011-03-13     7 days
2011-03-20     7 days
2013-02-10   693 days
2013-02-24    14 days
2013-03-03     7 days
2013-03-10     7 days
2013-03-17     7 days
2013-03-24     7 days
2013-03-31     7 days
2013-04-07     7 days
2013-04-14     7 days
2013-04-21     7 days
2013-04-28     7 days
2013-05-05     7 days
2013-05-12     7 days
2013-05-19     7 days
2013-05-26     7 days
2013-06-02     7 days
2013-06-09     7 days
2013-06-16     7 days
2013-06-23     7 days
2013-06-30     7 days
2013-07-07     7 days
2013-07-14     7 days
2013-07-21     7 days
2013-07-28     7 days
2013-08-04     7 days
2013-08-11     7 days
2013-08-18     7 days
2013-08-25     7 days
2013-09-01     7 days
2013-09-08     7 days
2013-09-15     7 days
2013-09-22     7 days
2013-09-29     7 days
2013-10-06     7 days
2013-10-13     7 days
2013-10-20     7 days
2013-10-27     7 days
2013-11-03     7 days
2013-11-10     7 days
2013-11-17     7 days
2013-11-24     7 days
2013-12-01     7 days
2013-12-08     7 days
2013-12-15     7 days
2013-12-22     7 days
2013-12-29     7 days
2014-01-05     7 days
2014-01-12     7 days
2014-01-19     7 days
2014-01-26     7 days
2014-02-02     7 days
2014-02-09     7 days
2014-02-16     7 days
2014-02-23     7 days
2014-03-02     7 days
2014-03-09     7 days
2014-03-16     7 days
2014-03-23     7 days
2014-03-30     7 days
2014-04-06     7 days
2014-04-13     7 days
2014-04-20     7 days
2014-04-27     7 days
2014-05-04     7 days
2014-05-11     7 days
2014-05-18     7 days
2014-05-25     7 days
2014-06-01     7 days
2014-06-08     7 days
2014-06-15     7 days
2014-06-22     7 days
2014-06-29     7 days
2014-07-06     7 days
2014-07-13     7 days
2014-07-20     7 days
2014-07-27     7 days
2014-08-03     7 days
2014-08-10     7 days
2014-08-17     7 days
2014-08-24     7 days
2014-08-31     7 days
2014-09-07     7 days
2014-09-14     7 days
2014-09-21     7 days
2014-09-28     7 days
2014-10-05     7 days
2014-10-12     7 days
2014-10-19     7 days
2014-10-26     7 days
2014-11-02     7 days
2014-11-09     7 days
2014-11-16     7 days
2014-11-23     7 days
2014-11-30     7 days
2014-12-07     7 days
2014-12-14     7 days
2014-12-21     7 days
2014-12-28     7 days
2015-01-04     7 days
2015-01-11     7 days
2015-01-18     7 days
2015-01-25     7 days
2015-02-01     7 days
2015-02-08     7 days
2015-02-15     7 days
2015-02-22     7 days
2015-03-01     7 days
2015-03-08     7 days
2015-03-15     7 days
2015-03-22     7 days
2015-03-29     7 days
2015-04-05     7 days
2015-04-12     7 days
2015-04-19     7 days
2015-04-26     7 days
2015-05-03     7 days
2015-05-10     7 days
2015-05-17     7 days
2015-05-24     7 days
2015-05-31     7 days
2015-06-07     7 days
2015-06-14     7 days
2015-06-21     7 days
2015-06-28     7 days
2015-07-05     7 days
2015-07-12     7 days
2015-07-19     7 days
2015-07-26     7 days
2015-08-02     7 days
2015-08-09     7 days
2015-08-16     7 days
2015-08-23     7 days
2015-08-30     7 days
2015-09-06     7 days
2015-09-13     7 days
2015-09-20     7 days
2015-09-27     7 days
2015-10-04     7 days
2015-10-11     7 days
2015-10-18     7 days
2015-10-25     7 days
2015-11-01     7 days
2015-11-08     7 days
2015-11-15     7 days
2015-11-22     7 days
2015-11-29     7 days
2015-12-06     7 days
2015-12-13     7 days
2015-12-20     7 days
2015-12-27     7 days
2016-01-03     7 days
2016-01-10     7 days
2016-01-17     7 days
2016-01-24     7 days
2016-01-31     7 days
2016-02-07     7 days
2016-02-21    14 days
2016-02-28     7 days
2016-03-06     7 days
2016-03-13     7 days
2016-03-20     7 days
2016-03-27     7 days
2016-04-03     7 days
2016-04-10     7 days
2016-04-17     7 days
2016-04-24     7 days
2016-05-01     7 days
2016-05-08     7 days
2016-05-15     7 days
2016-05-22     7 days
2016-05-29     7 days
2016-06-05     7 days
2016-06-12     7 days
2016-06-19     7 days
2016-06-26     7 days
2016-07-03     7 days
2016-07-10     7 days
2016-07-17     7 days
2016-07-24     7 days
2016-07-31     7 days
2016-08-07     7 days
2016-08-14     7 days
2016-08-21     7 days
2016-08-28     7 days
2016-09-04     7 days
2016-09-11     7 days
2016-09-18     7 days
2016-09-25     7 days
2016-10-02     7 days
2016-10-16    14 days
2016-10-23     7 days
2016-10-30     7 days
2016-11-06     7 days
2016-11-13     7 days
2016-11-20     7 days
2016-11-27     7 days
2016-12-04     7 days
2016-12-11     7 days
2016-12-18     7 days
2016-12-25     7 days
2017-01-01     7 days
2017-01-08     7 days
2017-01-15     7 days
2017-01-22     7 days
2017-01-29     7 days
2017-02-05     7 days
2017-02-12     7 days
2017-02-19     7 days
2017-02-26     7 days
2017-03-05     7 days
2017-03-12     7 days
2017-03-19     7 days
2017-03-26     7 days
2017-04-02     7 days
2017-04-09     7 days
2017-04-16     7 days
2017-04-23     7 days
2017-04-30     7 days
2017-05-07     7 days
2017-05-14     7 days
2017-05-21     7 days
2017-05-28     7 days
2017-06-04     7 days
2017-06-11     7 days
2017-06-18     7 days
2017-06-25     7 days
2017-07-02     7 days
2017-07-09     7 days
2017-07-16     7 days
2017-07-23     7 days
2017-07-30     7 days
2017-08-06     7 days
2017-08-13     7 days
2017-08-20     7 days
2017-08-27     7 days
2017-09-03     7 days
2017-09-10     7 days
2017-09-17     7 days
2017-09-24     7 days
2017-10-01     7 days
2017-10-15    14 days
2017-10-22     7 days
2017-10-29     7 days
2017-11-05     7 days
2017-11-12     7 days
2017-11-19     7 days
2017-11-26     7 days
2017-12-03     7 days
2017-12-10     7 days
2017-12-17     7 days
2017-12-24     7 days
2017-12-31     7 days
2018-01-07     7 days
2018-01-14     7 days
2018-01-21     7 days
2018-01-28     7 days
2018-02-04     7 days
2018-02-11     7 days
2018-02-18     7 days
2018-02-25     7 days
2018-03-04     7 days
2018-03-11     7 days
2018-03-18     7 days
2018-03-25     7 days
2018-04-01     7 days
2018-04-08     7 days
2018-04-15     7 days
2018-04-22     7 days
2018-04-29     7 days
2018-05-06     7 days
2018-05-13     7 days
2018-05-20     7 days
2018-05-27     7 days
2018-06-03     7 days
2018-06-10     7 days
2018-06-17     7 days
2018-06-24     7 days
2018-07-01     7 days
2018-07-08     7 days
2018-07-15     7 days
2018-07-22     7 days
2018-07-29     7 days
2018-08-05     7 days
2018-08-12     7 days
2018-08-19     7 days
2018-08-26     7 days
2018-09-02     7 days
2018-09-09     7 days
2018-09-16     7 days
2018-09-23     7 days
2018-09-30     7 days
2018-10-14    14 days
2018-10-21     7 days
2018-10-28     7 days
2018-11-04     7 days
2018-11-11     7 days
2018-11-18     7 days
2018-11-25     7 days
2018-12-02     7 days
2018-12-09     7 days
2018-12-16     7 days
2018-12-23     7 days
2018-12-30     7 days
2019-01-06     7 days
2019-01-13     7 days
2019-01-20     7 days
2019-01-27     7 days
2019-02-03     7 days
2019-02-17    14 days
2019-02-24     7 days
2019-03-03     7 days
2019-03-10     7 days
2019-03-17     7 days
2019-03-24     7 days
2019-03-31     7 days
2019-04-07     7 days
2019-04-14     7 days
2019-04-21     7 days
2019-04-28     7 days
2019-05-05     7 days
2019-05-12     7 days
2019-05-19     7 days
2019-05-26     7 days
2019-06-02     7 days
2019-06-09     7 days
2019-06-16     7 days
2019-06-23     7 days
2019-06-30     7 days
2019-07-07     7 days
2019-07-14     7 days
2019-07-21     7 days
2019-07-28     7 days
2019-08-04     7 days
2019-08-11     7 days
2019-08-18     7 days
2019-08-25     7 days
2019-09-01     7 days
2019-09-08     7 days
2019-09-15     7 days
2019-09-22     7 days
2019-09-29     7 days
2019-10-06     7 days
2019-10-13     7 days
2019-10-20     7 days
2019-10-27     7 days
2019-11-03     7 days
2019-11-10     7 days
2019-11-17     7 days
2019-11-24     7 days
2019-12-01     7 days
2019-12-08     7 days
2019-12-15     7 days
2019-12-22     7 days
2019-12-29     7 days
2020-01-05     7 days
2020-01-12     7 days
2020-01-19     7 days
2020-01-26     7 days
2020-02-09    14 days
2020-02-16     7 days
2020-02-23     7 days
2020-03-01     7 days
2020-03-08     7 days
2020-03-15     7 days
2020-03-22     7 days
2020-03-29     7 days
2020-04-05     7 days
2020-04-12     7 days
2020-04-19     7 days
2020-04-26     7 days
2020-05-03     7 days
2020-05-10     7 days
2020-05-17     7 days
2020-05-24     7 days
2020-05-31     7 days
2020-06-07     7 days
2020-06-14     7 days
2020-06-21     7 days
2020-06-28     7 days
2020-07-05     7 days
2020-07-12     7 days
2020-07-19     7 days
2020-07-26     7 days
2020-08-02     7 days
2020-08-09     7 days
2020-08-16     7 days
2020-08-23     7 days
2020-08-30     7 days
2020-09-06     7 days
2020-09-13     7 days
2020-09-20     7 days
2020-09-27     7 days
2020-10-04     7 days
2020-10-11     7 days
2020-10-18     7 days
2020-10-25     7 days
2020-11-01     7 days
2020-11-08     7 days
2020-11-15     7 days
2020-11-22     7 days
2020-11-29     7 days
2020-12-06     7 days
2020-12-13     7 days
2020-12-20     7 days
2020-12-27     7 days
2021-01-03     7 days
2021-01-10     7 days
2021-01-17     7 days
2021-01-24     7 days
2021-01-31     7 days
2021-02-07     7 days
2021-02-14     7 days
2021-02-21     7 days
2021-02-28     7 days
2021-03-07     7 days
2021-03-14     7 days
2021-03-21     7 days
2021-03-28     7 days
2021-04-04     7 days
2021-04-11     7 days
2021-04-18     7 days
2021-04-25     7 days
2021-05-02     7 days
2021-05-09     7 days
2021-05-16     7 days
2021-05-23     7 days
2021-05-30     7 days
2021-06-06     7 days
2021-06-13     7 days
2021-06-20     7 days
2021-06-27     7 days
2021-07-04     7 days
2021-07-11     7 days
2021-07-18     7 days
2021-07-25     7 days
2021-08-01     7 days
2021-08-08     7 days
2021-08-15     7 days
2021-08-22     7 days
2021-08-29     7 days
2021-09-05     7 days
2021-09-12     7 days
2021-09-19     7 days
2021-09-26     7 days
2021-10-03     7 days
2021-10-10     7 days
2021-10-17     7 days
2021-10-24     7 days
2021-10-31     7 days
2021-11-07     7 days
2021-11-14     7 days
2021-11-21     7 days
2021-11-28     7 days
2021-12-05     7 days
2021-12-12     7 days
2021-12-19     7 days
2021-12-26     7 days
2022-01-02     7 days
2022-01-09     7 days
2022-01-16     7 days
2022-01-23     7 days
2022-01-30     7 days
2022-02-13    14 days
2022-02-20     7 days
2022-02-27     7 days
2022-03-06     7 days
2022-03-13     7 days
2022-03-20     7 days
2022-03-27     7 days
2022-04-03     7 days
2022-04-10     7 days
2022-04-17     7 days
2022-04-24     7 days
2022-05-01     7 days
2022-05-08     7 days
2022-05-15     7 days
2022-05-22     7 days
2022-05-29     7 days
2022-06-05     7 days
2022-06-12     7 days
2022-06-19     7 days
2022-06-26     7 days
2022-07-03     7 days
2022-07-10     7 days
2022-07-17     7 days
2022-07-24     7 days
2022-07-31     7 days
2022-08-07     7 days
2022-08-14     7 days
2022-08-21     7 days
2022-08-28     7 days
2022-09-04     7 days
2022-09-11     7 days
2022-09-18     7 days
2022-09-25     7 days
2022-10-02     7 days
2022-10-16    14 days
2022-10-23     7 days
2022-10-30     7 days
2022-11-06     7 days
2022-11-13     7 days
2022-11-20     7 days
2022-11-27     7 days
2022-12-04     7 days
2022-12-11     7 days
2022-12-18     7 days
2022-12-25     7 days
2023-01-01     7 days
2023-01-08     7 days
2023-01-15     7 days
2023-01-22     7 days
2023-02-05    14 days
2023-02-12     7 days
2023-02-19     7 days
2023-02-26     7 days
2023-03-05     7 days
2023-03-12     7 days
2023-03-19     7 days
2023-03-26     7 days
2023-04-02     7 days
2023-04-09     7 days
2023-04-16     7 days
2023-04-23     7 days
2023-04-30     7 days
2023-05-07     7 days
2023-05-14     7 days
2023-05-21     7 days
2023-05-28     7 days
2023-06-04     7 days
2023-06-11     7 days
2023-06-18     7 days
2023-06-25     7 days
2023-07-02     7 days
2023-07-09     7 days
2023-07-16     7 days
2023-07-23     7 days
2023-07-30     7 days
2023-08-06     7 days
2023-08-13     7 days
2023-08-20     7 days
2023-08-27     7 days
2023-09-03     7 days
2023-09-10     7 days
2023-09-17     7 days
2023-09-24     7 days
2023-10-01     7 days
2023-10-15    14 days
2023-10-22     7 days
2023-10-29     7 days
2023-11-05     7 days
2023-11-12     7 days
2023-11-19     7 days
2023-11-26     7 days
2023-12-03     7 days
2023-12-10     7 days
2023-12-17     7 days
2023-12-24     7 days
2023-12-31     7 days
2024-01-07     7 days
2024-01-14     7 days
2024-01-21     7 days
2024-01-28     7 days
2024-02-04     7 days
2024-02-11     7 days
2024-02-25    14 days
2024-03-03     7 days
2024-03-10     7 days
2024-03-17     7 days
2024-03-24     7 days
2024-03-31     7 days
2024-04-07     7 days
2024-04-14     7 days
2024-04-21     7 days
2024-04-28     7 days
2024-05-05     7 days
2024-05-12     7 days
2024-05-19     7 days
2024-05-26     7 days
2024-06-02     7 days
2024-06-09     7 days
2024-06-16     7 days
2024-06-23     7 days
2024-06-30     7 days
2024-07-07     7 days
2024-07-14     7 days
2024-07-21     7 days
2024-07-28     7 days
2024-08-04     7 days
2024-08-11     7 days
2024-08-18     7 days
2024-08-25     7 days
2024-09-01     7 days
2024-09-08     7 days
2024-09-15     7 days
2024-09-22     7 days
2024-09-29     7 days
2024-10-06     7 days
2024-10-13     7 days
2024-10-20     7 days
2024-10-27     7 days
2024-11-03     7 days
2024-11-10     7 days
2024-11-17     7 days
2024-11-24     7 days
2024-12-01     7 days
2024-12-08     7 days
2024-12-15     7 days
2024-12-22     7 days
2024-12-29     7 days
2025-01-05     7 days
2025-01-12     7 days
2025-01-19     7 days
2025-01-26     7 days
2025-02-02     7 days
2025-02-09     7 days
2025-02-16     7 days
2025-02-23     7 days
2025-03-02     7 days
2025-03-09     7 days
2025-03-16     7 days
2025-03-23     7 days
2025-03-30     7 days
2025-04-06     7 days
2025-04-13     7 days
2025-04-20     7 days
2025-04-27     7 days
2025-05-04     7 days
2025-05-11     7 days
2025-05-18     7 days
2025-05-25     7 days
2025-06-01     7 days
2025-06-08     7 days
2025-06-15     7 days
2025-06-22     7 days
2025-06-29     7 days
2025-07-06     7 days
2025-07-13     7 days
2025-07-08 21:17:39,977 - INFO - Data Engineer: Monthly DataFrame head:
            open  high   low  close  volume
date                                       
2000-04-30  2.82  2.92  2.67   2.74   95950
2000-05-31  2.77  2.89  2.47   2.76  172827
2000-06-30  2.76  2.99  2.71   2.91  348117
2000-07-31  2.91  3.39  2.86   3.17  488856
2000-08-31  3.18  3.38  2.92   2.97  350170
2025-07-08 21:17:39,978 - INFO - Data Engineer: Monthly DataFrame index diffs:
date
2000-04-30        NaT
2000-05-31    31 days
2000-06-30    30 days
2000-07-31    31 days
2000-08-31    31 days
2000-09-30    30 days
2000-10-31    31 days
2000-11-30    30 days
2000-12-31    31 days
2001-01-31    31 days
2001-02-28    28 days
2001-03-31    31 days
2001-04-30    30 days
2001-05-31    31 days
2001-06-30    30 days
2001-07-31    31 days
2001-08-31    31 days
2001-09-30    30 days
2001-10-31    31 days
2001-11-30    30 days
2001-12-31    31 days
2002-01-31    31 days
2002-02-28    28 days
2002-03-31    31 days
2002-04-30    30 days
2002-05-31    31 days
2002-06-30    30 days
2002-07-31    31 days
2002-08-31    31 days
2002-09-30    30 days
2002-10-31    31 days
2002-11-30    30 days
2002-12-31    31 days
2003-01-31    31 days
2003-02-28    28 days
2003-03-31    31 days
2003-04-30    30 days
2003-05-31    31 days
2003-06-30    30 days
2003-07-31    31 days
2003-08-31    31 days
2003-09-30    30 days
2003-10-31    31 days
2003-11-30    30 days
2003-12-31    31 days
2004-01-31    31 days
2004-02-29    29 days
2004-03-31    31 days
2004-04-30    30 days
2004-05-31    31 days
2004-06-30    30 days
2004-07-31    31 days
2004-08-31    31 days
2004-09-30    30 days
2004-10-31    31 days
2004-11-30    30 days
2004-12-31    31 days
2005-01-31    31 days
2005-02-28    28 days
2005-03-31    31 days
2005-04-30    30 days
2005-05-31    31 days
2005-06-30    30 days
2005-07-31    31 days
2005-08-31    31 days
2005-09-30    30 days
2005-10-31    31 days
2005-11-30    30 days
2005-12-31    31 days
2006-01-31    31 days
2006-02-28    28 days
2006-03-31    31 days
2006-04-30    30 days
2006-05-31    31 days
2006-06-30    30 days
2006-07-31    31 days
2006-08-31    31 days
2006-09-30    30 days
2006-10-31    31 days
2006-11-30    30 days
2006-12-31    31 days
2007-01-31    31 days
2007-02-28    28 days
2007-03-31    31 days
2007-04-30    30 days
2007-05-31    31 days
2007-06-30    30 days
2007-07-31    31 days
2007-08-31    31 days
2007-09-30    30 days
2007-10-31    31 days
2007-11-30    30 days
2007-12-31    31 days
2008-01-31    31 days
2008-02-29    29 days
2008-03-31    31 days
2008-04-30    30 days
2008-05-31    31 days
2008-06-30    30 days
2008-07-31    31 days
2008-08-31    31 days
2008-09-30    30 days
2008-10-31    31 days
2008-11-30    30 days
2008-12-31    31 days
2009-01-31    31 days
2009-02-28    28 days
2009-03-31    31 days
2009-04-30    30 days
2009-05-31    31 days
2009-06-30    30 days
2009-07-31    31 days
2009-08-31    31 days
2009-09-30    30 days
2009-10-31    31 days
2009-11-30    30 days
2009-12-31    31 days
2010-01-31    31 days
2010-02-28    28 days
2010-03-31    31 days
2010-04-30    30 days
2010-05-31    31 days
2010-06-30    30 days
2010-07-31    31 days
2010-08-31    31 days
2010-09-30    30 days
2010-10-31    31 days
2010-11-30    30 days
2010-12-31    31 days
2011-01-31    31 days
2011-02-28    28 days
2011-03-31    31 days
2013-02-28   700 days
2013-03-31    31 days
2013-04-30    30 days
2013-05-31    31 days
2013-06-30    30 days
2013-07-31    31 days
2013-08-31    31 days
2013-09-30    30 days
2013-10-31    31 days
2013-11-30    30 days
2013-12-31    31 days
2014-01-31    31 days
2014-02-28    28 days
2014-03-31    31 days
2014-04-30    30 days
2014-05-31    31 days
2014-06-30    30 days
2014-07-31    31 days
2014-08-31    31 days
2014-09-30    30 days
2014-10-31    31 days
2014-11-30    30 days
2014-12-31    31 days
2015-01-31    31 days
2015-02-28    28 days
2015-03-31    31 days
2015-04-30    30 days
2015-05-31    31 days
2015-06-30    30 days
2015-07-31    31 days
2015-08-31    31 days
2015-09-30    30 days
2015-10-31    31 days
2015-11-30    30 days
2015-12-31    31 days
2016-01-31    31 days
2016-02-29    29 days
2016-03-31    31 days
2016-04-30    30 days
2016-05-31    31 days
2016-06-30    30 days
2016-07-31    31 days
2016-08-31    31 days
2016-09-30    30 days
2016-10-31    31 days
2016-11-30    30 days
2016-12-31    31 days
2017-01-31    31 days
2017-02-28    28 days
2017-03-31    31 days
2017-04-30    30 days
2017-05-31    31 days
2017-06-30    30 days
2017-07-31    31 days
2017-08-31    31 days
2017-09-30    30 days
2017-10-31    31 days
2017-11-30    30 days
2017-12-31    31 days
2018-01-31    31 days
2018-02-28    28 days
2018-03-31    31 days
2018-04-30    30 days
2018-05-31    31 days
2018-06-30    30 days
2018-07-31    31 days
2018-08-31    31 days
2018-09-30    30 days
2018-10-31    31 days
2018-11-30    30 days
2018-12-31    31 days
2019-01-31    31 days
2019-02-28    28 days
2019-03-31    31 days
2019-04-30    30 days
2019-05-31    31 days
2019-06-30    30 days
2019-07-31    31 days
2019-08-31    31 days
2019-09-30    30 days
2019-10-31    31 days
2019-11-30    30 days
2019-12-31    31 days
2020-01-31    31 days
2020-02-29    29 days
2020-03-31    31 days
2020-04-30    30 days
2020-05-31    31 days
2020-06-30    30 days
2020-07-31    31 days
2020-08-31    31 days
2020-09-30    30 days
2020-10-31    31 days
2020-11-30    30 days
2020-12-31    31 days
2021-01-31    31 days
2021-02-28    28 days
2021-03-31    31 days
2021-04-30    30 days
2021-05-31    31 days
2021-06-30    30 days
2021-07-31    31 days
2021-08-31    31 days
2021-09-30    30 days
2021-10-31    31 days
2021-11-30    30 days
2021-12-31    31 days
2022-01-31    31 days
2022-02-28    28 days
2022-03-31    31 days
2022-04-30    30 days
2022-05-31    31 days
2022-06-30    30 days
2022-07-31    31 days
2022-08-31    31 days
2022-09-30    30 days
2022-10-31    31 days
2022-11-30    30 days
2022-12-31    31 days
2023-01-31    31 days
2023-02-28    28 days
2023-03-31    31 days
2023-04-30    30 days
2023-05-31    31 days
2023-06-30    30 days
2023-07-31    31 days
2023-08-31    31 days
2023-09-30    30 days
2023-10-31    31 days
2023-11-30    30 days
2023-12-31    31 days
2024-01-31    31 days
2024-02-29    29 days
2024-03-31    31 days
2024-04-30    30 days
2024-05-31    31 days
2024-06-30    30 days
2024-07-31    31 days
2024-08-31    31 days
2024-09-30    30 days
2024-10-31    31 days
2024-11-30    30 days
2024-12-31    31 days
2025-01-31    31 days
2025-02-28    28 days
2025-03-31    31 days
2025-04-30    30 days
2025-05-31    31 days
2025-06-30    30 days
2025-07-31    31 days
2025-07-08 21:17:39,978 - INFO - --- Agent: Elliott Wave Analyst (Deep Recursive) ---
2025-07-08 21:17:39,978 - INFO - Performing Top-Down Analysis: Starting with Monthly timeframe.
2025-07-08 21:17:39,978 - INFO - [find_elliott_wave_patterns] Starting analysis for dataframe of size 282.
2025-07-08 21:17:39,978 - INFO - [_split_df_by_gaps] Time differences between consecutive data points:
date
2000-04-30       NaT
2000-05-31   31 days
2000-06-30   30 days
2000-07-31   31 days
2000-08-31   31 days
               ...  
2025-03-31   31 days
2025-04-30   30 days
2025-05-31   31 days
2025-06-30   30 days
2025-07-31   31 days
Name: date, Length: 282, dtype: timedelta64[ns]
2025-07-08 21:17:39,979 - INFO - [_split_df_by_gaps] Gaps identified (True if gap > 30 days):
date
2000-04-30    False
2000-05-31     True
2000-06-30    False
2000-07-31     True
2000-08-31     True
              ...  
2025-03-31     True
2025-04-30    False
2025-05-31     True
2025-06-30    False
2025-07-31     True
Name: date, Length: 282, dtype: bool
2025-07-08 21:17:39,980 - INFO - Split dataframe into 166 segments due to trading gaps > 30 days.
2025-07-08 21:17:39,980 - INFO - --- Analyzing Trading Segment 1/166 ---
2025-07-08 21:17:39,982 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:39,982 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:39,982 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:39,982 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:39,982 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:39,982 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:39,982 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:39,982 - INFO - --- Analyzing Trading Segment 2/166 ---
2025-07-08 21:17:39,983 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:39,983 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:39,983 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:39,983 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:39,983 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:39,983 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:39,983 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:39,984 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:39,985 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:39,985 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:39,985 - INFO - --- Analyzing Trading Segment 3/166 ---
2025-07-08 21:17:39,985 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:39,985 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:39,985 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:39,985 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:39,985 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:39,985 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:39,985 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:39,985 - INFO - --- Analyzing Trading Segment 4/166 ---
2025-07-08 21:17:39,986 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:39,986 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:39,986 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:39,986 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:39,986 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:39,986 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:39,986 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:39,987 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:39,987 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:39,987 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:39,987 - INFO - --- Analyzing Trading Segment 5/166 ---
2025-07-08 21:17:39,987 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:39,987 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:39,987 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:39,987 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:39,988 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:39,988 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:39,988 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:39,988 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:39,988 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:39,988 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:39,988 - INFO - --- Analyzing Trading Segment 6/166 ---
2025-07-08 21:17:39,989 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:39,989 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:39,989 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:39,989 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:39,989 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:39,989 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:39,989 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:39,989 - INFO - --- Analyzing Trading Segment 7/166 ---
2025-07-08 21:17:39,989 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:39,989 - INFO - Analyzing sub-segment from 0 to 29 with Hurst: 0.50
2025-07-08 21:17:39,989 - INFO - [_find_local_extrema] Received segment of size 29
2025-07-08 21:17:39,989 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:39,990 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:39,990 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:39,990 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:39,990 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:39,990 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:39,990 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:39,990 - INFO - --- Analyzing Trading Segment 8/166 ---
2025-07-08 21:17:39,991 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:39,991 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:39,991 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:39,991 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:39,991 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:39,991 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:39,991 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:39,991 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:39,991 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:39,991 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:39,991 - INFO - --- Analyzing Trading Segment 9/166 ---
2025-07-08 21:17:39,992 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:39,992 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:39,992 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:39,992 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:39,992 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:39,992 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:39,992 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:39,993 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:39,993 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:39,993 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:39,993 - INFO - --- Analyzing Trading Segment 10/166 ---
2025-07-08 21:17:39,993 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:39,993 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:39,993 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:39,993 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:39,993 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:39,993 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:39,993 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:39,993 - INFO - --- Analyzing Trading Segment 11/166 ---
2025-07-08 21:17:39,994 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:39,994 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:39,994 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:39,994 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:39,994 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:39,994 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:39,994 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:39,995 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:39,995 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:39,995 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:39,995 - INFO - --- Analyzing Trading Segment 12/166 ---
2025-07-08 21:17:39,996 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:39,996 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:39,996 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:39,996 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:39,996 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:39,996 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:39,996 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:39,996 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:39,996 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:39,996 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:39,996 - INFO - --- Analyzing Trading Segment 13/166 ---
2025-07-08 21:17:39,997 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:39,997 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:39,997 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:39,997 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:39,997 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:39,997 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:39,997 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:39,997 - INFO - --- Analyzing Trading Segment 14/166 ---
2025-07-08 21:17:39,998 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:39,998 - INFO - Analyzing sub-segment from 0 to 29 with Hurst: 0.50
2025-07-08 21:17:39,998 - INFO - [_find_local_extrema] Received segment of size 29
2025-07-08 21:17:39,998 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:39,998 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:39,998 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:39,998 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:39,998 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:39,998 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:39,998 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:39,998 - INFO - --- Analyzing Trading Segment 15/166 ---
2025-07-08 21:17:39,999 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:39,999 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:39,999 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:39,999 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:39,999 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:39,999 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:39,999 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:39,999 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:39,999 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:39,999 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,000 - INFO - --- Analyzing Trading Segment 16/166 ---
2025-07-08 21:17:40,000 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,000 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,000 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,000 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,001 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,001 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,001 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,001 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,001 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,001 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,001 - INFO - --- Analyzing Trading Segment 17/166 ---
2025-07-08 21:17:40,001 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,001 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,001 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,001 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,001 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,001 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,001 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,002 - INFO - --- Analyzing Trading Segment 18/166 ---
2025-07-08 21:17:40,002 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,002 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,002 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,002 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,003 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,003 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,003 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,003 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,003 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,003 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,003 - INFO - --- Analyzing Trading Segment 19/166 ---
2025-07-08 21:17:40,004 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,004 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,004 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,004 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,004 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,004 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,004 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,004 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,004 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,004 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,004 - INFO - --- Analyzing Trading Segment 20/166 ---
2025-07-08 21:17:40,005 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,005 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,005 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,005 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,005 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,005 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,005 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,005 - INFO - --- Analyzing Trading Segment 21/166 ---
2025-07-08 21:17:40,006 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,006 - INFO - Analyzing sub-segment from 0 to 29 with Hurst: 0.50
2025-07-08 21:17:40,006 - INFO - [_find_local_extrema] Received segment of size 29
2025-07-08 21:17:40,006 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,006 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,006 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,006 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,006 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,006 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,006 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,006 - INFO - --- Analyzing Trading Segment 22/166 ---
2025-07-08 21:17:40,007 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,007 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,007 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,007 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,007 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,007 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,007 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,008 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,008 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,008 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,008 - INFO - --- Analyzing Trading Segment 23/166 ---
2025-07-08 21:17:40,008 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,008 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,008 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,008 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,009 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,009 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,009 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,009 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,009 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,009 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,009 - INFO - --- Analyzing Trading Segment 24/166 ---
2025-07-08 21:17:40,010 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,010 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,010 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,010 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,010 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,010 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,010 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,010 - INFO - --- Analyzing Trading Segment 25/166 ---
2025-07-08 21:17:40,010 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,010 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,010 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,010 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,011 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,011 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,011 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,011 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,011 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,011 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,011 - INFO - --- Analyzing Trading Segment 26/166 ---
2025-07-08 21:17:40,012 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,012 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,012 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,012 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,012 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,012 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,012 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,012 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,012 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,012 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,012 - INFO - --- Analyzing Trading Segment 27/166 ---
2025-07-08 21:17:40,013 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,013 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,013 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,013 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,013 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,013 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,013 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,013 - INFO - --- Analyzing Trading Segment 28/166 ---
2025-07-08 21:17:40,014 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,014 - INFO - Analyzing sub-segment from 0 to 30 with Hurst: 0.50
2025-07-08 21:17:40,014 - INFO - [_find_local_extrema] Received segment of size 30
2025-07-08 21:17:40,014 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,014 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,014 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,014 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,014 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,014 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,014 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,014 - INFO - --- Analyzing Trading Segment 29/166 ---
2025-07-08 21:17:40,015 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,015 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,015 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,015 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,015 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,015 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,015 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,016 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,016 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,016 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,016 - INFO - --- Analyzing Trading Segment 30/166 ---
2025-07-08 21:17:40,016 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,017 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,017 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,017 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,017 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,017 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,017 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,017 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,017 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,017 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,017 - INFO - --- Analyzing Trading Segment 31/166 ---
2025-07-08 21:17:40,018 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,018 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,018 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,018 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,018 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,018 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,018 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,018 - INFO - --- Analyzing Trading Segment 32/166 ---
2025-07-08 21:17:40,019 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,019 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,019 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,019 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,019 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,019 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,019 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,019 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,019 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,019 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,019 - INFO - --- Analyzing Trading Segment 33/166 ---
2025-07-08 21:17:40,020 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,020 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,020 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,020 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,020 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,020 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,020 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,020 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,020 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,020 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,020 - INFO - --- Analyzing Trading Segment 34/166 ---
2025-07-08 21:17:40,021 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,021 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,021 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,021 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,021 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,021 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,021 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,021 - INFO - --- Analyzing Trading Segment 35/166 ---
2025-07-08 21:17:40,022 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,022 - INFO - Analyzing sub-segment from 0 to 29 with Hurst: 0.50
2025-07-08 21:17:40,022 - INFO - [_find_local_extrema] Received segment of size 29
2025-07-08 21:17:40,022 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,022 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,022 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,022 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,022 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,022 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,022 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,022 - INFO - --- Analyzing Trading Segment 36/166 ---
2025-07-08 21:17:40,023 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,023 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,023 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,023 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,023 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,023 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,023 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,023 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,024 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,024 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,024 - INFO - --- Analyzing Trading Segment 37/166 ---
2025-07-08 21:17:40,024 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,024 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,024 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,024 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,025 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,025 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,025 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,025 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,025 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,025 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,025 - INFO - --- Analyzing Trading Segment 38/166 ---
2025-07-08 21:17:40,025 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,025 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,025 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,026 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,026 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,026 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,026 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,026 - INFO - --- Analyzing Trading Segment 39/166 ---
2025-07-08 21:17:40,026 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,026 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,026 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,026 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,027 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,027 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,027 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,027 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,027 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,027 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,027 - INFO - --- Analyzing Trading Segment 40/166 ---
2025-07-08 21:17:40,028 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,028 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,028 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,028 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,028 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,028 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,028 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,028 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,028 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,028 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,028 - INFO - --- Analyzing Trading Segment 41/166 ---
2025-07-08 21:17:40,029 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,029 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,029 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,029 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,029 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,029 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,029 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,029 - INFO - --- Analyzing Trading Segment 42/166 ---
2025-07-08 21:17:40,030 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,030 - INFO - Analyzing sub-segment from 0 to 29 with Hurst: 0.50
2025-07-08 21:17:40,030 - INFO - [_find_local_extrema] Received segment of size 29
2025-07-08 21:17:40,030 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,030 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,030 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,030 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,030 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,030 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,030 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,030 - INFO - --- Analyzing Trading Segment 43/166 ---
2025-07-08 21:17:40,031 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,031 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,031 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,031 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,031 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,031 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,031 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,032 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,032 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,032 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,032 - INFO - --- Analyzing Trading Segment 44/166 ---
2025-07-08 21:17:40,032 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,032 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,032 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,032 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,033 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,033 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,033 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,033 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,033 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,033 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,033 - INFO - --- Analyzing Trading Segment 45/166 ---
2025-07-08 21:17:40,034 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,034 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,034 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,034 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,034 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,034 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,034 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,034 - INFO - --- Analyzing Trading Segment 46/166 ---
2025-07-08 21:17:40,034 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,034 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,034 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,035 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,035 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,035 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,035 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,035 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,035 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,035 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,035 - INFO - --- Analyzing Trading Segment 47/166 ---
2025-07-08 21:17:40,036 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,036 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,036 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,036 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,036 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,036 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,036 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,036 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,036 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,036 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,036 - INFO - --- Analyzing Trading Segment 48/166 ---
2025-07-08 21:17:40,037 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,037 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,037 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,037 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,037 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,037 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,037 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,037 - INFO - --- Analyzing Trading Segment 49/166 ---
2025-07-08 21:17:40,038 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,038 - INFO - Analyzing sub-segment from 0 to 29 with Hurst: 0.50
2025-07-08 21:17:40,038 - INFO - [_find_local_extrema] Received segment of size 29
2025-07-08 21:17:40,038 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,038 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,038 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,038 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,038 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,038 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,038 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,038 - INFO - --- Analyzing Trading Segment 50/166 ---
2025-07-08 21:17:40,039 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,039 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,039 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,039 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,039 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,039 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,039 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,040 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,040 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,040 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,040 - INFO - --- Analyzing Trading Segment 51/166 ---
2025-07-08 21:17:40,041 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,041 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,041 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,041 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,041 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,041 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,041 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,041 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,041 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,041 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,041 - INFO - --- Analyzing Trading Segment 52/166 ---
2025-07-08 21:17:40,042 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,042 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,042 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,042 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,042 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,042 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,042 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,042 - INFO - --- Analyzing Trading Segment 53/166 ---
2025-07-08 21:17:40,043 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,043 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,043 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,043 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,043 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,043 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,043 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,043 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,043 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,043 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,043 - INFO - --- Analyzing Trading Segment 54/166 ---
2025-07-08 21:17:40,044 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,044 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,044 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,044 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,044 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,044 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,044 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,044 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,044 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,044 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,044 - INFO - --- Analyzing Trading Segment 55/166 ---
2025-07-08 21:17:40,045 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,045 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,045 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,045 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,045 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,045 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,045 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,045 - INFO - --- Analyzing Trading Segment 56/166 ---
2025-07-08 21:17:40,046 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,046 - INFO - Analyzing sub-segment from 0 to 30 with Hurst: 0.50
2025-07-08 21:17:40,046 - INFO - [_find_local_extrema] Received segment of size 30
2025-07-08 21:17:40,046 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,046 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,046 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,046 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,046 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,046 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,046 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,046 - INFO - --- Analyzing Trading Segment 57/166 ---
2025-07-08 21:17:40,047 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,047 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,047 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,047 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,047 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,047 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,047 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,048 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,048 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,048 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,048 - INFO - --- Analyzing Trading Segment 58/166 ---
2025-07-08 21:17:40,049 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,049 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,049 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,049 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,049 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,049 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,049 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,049 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,049 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,049 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,049 - INFO - --- Analyzing Trading Segment 59/166 ---
2025-07-08 21:17:40,050 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,050 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,050 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,050 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,050 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,050 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,050 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,050 - INFO - --- Analyzing Trading Segment 60/166 ---
2025-07-08 21:17:40,051 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,051 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,051 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,051 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,051 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,051 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,051 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,051 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,051 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,051 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,051 - INFO - --- Analyzing Trading Segment 61/166 ---
2025-07-08 21:17:40,052 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,052 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,052 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,052 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,052 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,052 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,052 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,053 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,053 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,053 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,053 - INFO - --- Analyzing Trading Segment 62/166 ---
2025-07-08 21:17:40,053 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,053 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,053 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,053 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,053 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,053 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,053 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,053 - INFO - --- Analyzing Trading Segment 63/166 ---
2025-07-08 21:17:40,054 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,054 - INFO - Analyzing sub-segment from 0 to 29 with Hurst: 0.50
2025-07-08 21:17:40,054 - INFO - [_find_local_extrema] Received segment of size 29
2025-07-08 21:17:40,054 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,054 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,054 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,054 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,055 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,055 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,055 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,055 - INFO - --- Analyzing Trading Segment 64/166 ---
2025-07-08 21:17:40,055 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,055 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,055 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,055 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,056 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,056 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,056 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,056 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,056 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,056 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,056 - INFO - --- Analyzing Trading Segment 65/166 ---
2025-07-08 21:17:40,057 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,057 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,057 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,057 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,057 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,057 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,057 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,057 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,057 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,057 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,057 - INFO - --- Analyzing Trading Segment 66/166 ---
2025-07-08 21:17:40,058 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,058 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,058 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,058 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,058 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,058 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,058 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,058 - INFO - --- Analyzing Trading Segment 67/166 ---
2025-07-08 21:17:40,059 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,059 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,059 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,059 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,059 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,059 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,059 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,059 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,059 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,059 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,059 - INFO - --- Analyzing Trading Segment 68/166 ---
2025-07-08 21:17:40,060 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,060 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,060 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,060 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,060 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,060 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,060 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,060 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,060 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,060 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,060 - INFO - --- Analyzing Trading Segment 69/166 ---
2025-07-08 21:17:40,061 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,061 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,061 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,061 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,061 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,061 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,061 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,061 - INFO - --- Analyzing Trading Segment 70/166 ---
2025-07-08 21:17:40,062 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,062 - INFO - Analyzing sub-segment from 0 to 29 with Hurst: 0.50
2025-07-08 21:17:40,062 - INFO - [_find_local_extrema] Received segment of size 29
2025-07-08 21:17:40,062 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,062 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,062 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,062 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,062 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,062 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,062 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,062 - INFO - --- Analyzing Trading Segment 71/166 ---
2025-07-08 21:17:40,063 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,063 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,063 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,063 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,063 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,063 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,063 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,064 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,064 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,064 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,064 - INFO - --- Analyzing Trading Segment 72/166 ---
2025-07-08 21:17:40,065 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,065 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,065 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,065 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,065 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,065 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,065 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,065 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,065 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,065 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,065 - INFO - --- Analyzing Trading Segment 73/166 ---
2025-07-08 21:17:40,066 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,066 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,066 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,066 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,066 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,066 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,066 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,066 - INFO - --- Analyzing Trading Segment 74/166 ---
2025-07-08 21:17:40,067 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,067 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,067 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,067 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,067 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,067 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,067 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,067 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,067 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,067 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,067 - INFO - --- Analyzing Trading Segment 75/166 ---
2025-07-08 21:17:40,068 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,068 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,068 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,068 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,068 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,068 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,068 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,068 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,068 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,068 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,068 - INFO - --- Analyzing Trading Segment 76/166 ---
2025-07-08 21:17:40,069 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,069 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,069 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,069 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,069 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,069 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,069 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,069 - INFO - --- Analyzing Trading Segment 77/166 ---
2025-07-08 21:17:40,070 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,070 - INFO - Analyzing sub-segment from 0 to 29 with Hurst: 0.50
2025-07-08 21:17:40,070 - INFO - [_find_local_extrema] Received segment of size 29
2025-07-08 21:17:40,070 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,070 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,070 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,070 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,070 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,070 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,070 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,070 - INFO - --- Analyzing Trading Segment 78/166 ---
2025-07-08 21:17:40,071 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,071 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,071 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,071 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,071 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,071 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,071 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,071 - INFO - --- Analyzing Trading Segment 79/166 ---
2025-07-08 21:17:40,072 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,072 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,072 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,072 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,072 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,072 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,072 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,072 - INFO - --- Analyzing Trading Segment 80/166 ---
2025-07-08 21:17:40,073 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,073 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,073 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,073 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,073 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,073 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,073 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,073 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,073 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,073 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,073 - INFO - --- Analyzing Trading Segment 81/166 ---
2025-07-08 21:17:40,074 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,074 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,074 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,074 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,074 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,074 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,074 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,074 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,074 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,074 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,074 - INFO - --- Analyzing Trading Segment 82/166 ---
2025-07-08 21:17:40,075 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,075 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,075 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,075 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,075 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,075 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,075 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,075 - INFO - --- Analyzing Trading Segment 83/166 ---
2025-07-08 21:17:40,076 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,076 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,076 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,076 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,076 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,076 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,076 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,076 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,076 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,076 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,076 - INFO - --- Analyzing Trading Segment 84/166 ---
2025-07-08 21:17:40,077 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,077 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,077 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,077 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,077 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,077 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,077 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,078 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,078 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,078 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,078 - INFO - --- Analyzing Trading Segment 85/166 ---
2025-07-08 21:17:40,078 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,078 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,078 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,078 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,078 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,078 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,078 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,078 - INFO - --- Analyzing Trading Segment 86/166 ---
2025-07-08 21:17:40,079 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,079 - INFO - Analyzing sub-segment from 0 to 29 with Hurst: 0.50
2025-07-08 21:17:40,079 - INFO - [_find_local_extrema] Received segment of size 29
2025-07-08 21:17:40,079 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,079 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,079 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,079 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,080 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,080 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,080 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,080 - INFO - --- Analyzing Trading Segment 87/166 ---
2025-07-08 21:17:40,081 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,081 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,081 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,081 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,081 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,081 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,081 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,081 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,081 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,081 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,081 - INFO - --- Analyzing Trading Segment 88/166 ---
2025-07-08 21:17:40,082 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,082 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,082 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,082 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,082 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,082 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,082 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,082 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,083 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,083 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,083 - INFO - --- Analyzing Trading Segment 89/166 ---
2025-07-08 21:17:40,083 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,083 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,083 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,083 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,083 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,083 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,083 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,083 - INFO - --- Analyzing Trading Segment 90/166 ---
2025-07-08 21:17:40,084 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,084 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,084 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,084 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,084 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,084 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,084 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,085 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,085 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,085 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,085 - INFO - --- Analyzing Trading Segment 91/166 ---
2025-07-08 21:17:40,086 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,086 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,086 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,086 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,086 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,086 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,086 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,086 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,086 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,086 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,086 - INFO - --- Analyzing Trading Segment 92/166 ---
2025-07-08 21:17:40,087 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,087 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,087 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,087 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,087 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,087 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,087 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,087 - INFO - --- Analyzing Trading Segment 93/166 ---
2025-07-08 21:17:40,088 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,088 - INFO - Analyzing sub-segment from 0 to 29 with Hurst: 0.50
2025-07-08 21:17:40,088 - INFO - [_find_local_extrema] Received segment of size 29
2025-07-08 21:17:40,088 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,088 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,088 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,088 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,088 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,088 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,088 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,088 - INFO - --- Analyzing Trading Segment 94/166 ---
2025-07-08 21:17:40,089 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,089 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,089 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,089 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,090 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,090 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,090 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,090 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,090 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,090 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,090 - INFO - --- Analyzing Trading Segment 95/166 ---
2025-07-08 21:17:40,091 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,091 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,091 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,091 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,091 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,091 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,091 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,091 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,091 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,091 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,091 - INFO - --- Analyzing Trading Segment 96/166 ---
2025-07-08 21:17:40,092 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,092 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,092 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,092 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,092 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,092 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,092 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,092 - INFO - --- Analyzing Trading Segment 97/166 ---
2025-07-08 21:17:40,093 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,093 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,093 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,093 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,093 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,093 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,093 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,093 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,093 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,093 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,093 - INFO - --- Analyzing Trading Segment 98/166 ---
2025-07-08 21:17:40,094 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,094 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,094 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,094 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,094 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,094 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,094 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,095 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,095 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,095 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,095 - INFO - --- Analyzing Trading Segment 99/166 ---
2025-07-08 21:17:40,095 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,095 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,095 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,095 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,095 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,095 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,095 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,095 - INFO - --- Analyzing Trading Segment 100/166 ---
2025-07-08 21:17:40,096 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,096 - INFO - Analyzing sub-segment from 0 to 30 with Hurst: 0.50
2025-07-08 21:17:40,096 - INFO - [_find_local_extrema] Received segment of size 30
2025-07-08 21:17:40,096 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,096 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,096 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,096 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,097 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,097 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,097 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,097 - INFO - --- Analyzing Trading Segment 101/166 ---
2025-07-08 21:17:40,097 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,097 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,097 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,098 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,098 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,098 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,098 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,098 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,098 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,098 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,098 - INFO - --- Analyzing Trading Segment 102/166 ---
2025-07-08 21:17:40,099 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,099 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,099 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,099 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,099 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,099 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,099 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,099 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,099 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,099 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,099 - INFO - --- Analyzing Trading Segment 103/166 ---
2025-07-08 21:17:40,100 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,100 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,100 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,100 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,100 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,100 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,100 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,100 - INFO - --- Analyzing Trading Segment 104/166 ---
2025-07-08 21:17:40,101 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,101 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,101 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,101 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,101 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,101 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,101 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,101 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,101 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,101 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,101 - INFO - --- Analyzing Trading Segment 105/166 ---
2025-07-08 21:17:40,102 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,102 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,102 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,102 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,102 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,102 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,102 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,103 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,103 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,103 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,103 - INFO - --- Analyzing Trading Segment 106/166 ---
2025-07-08 21:17:40,103 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,103 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,103 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,103 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,103 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,103 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,103 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,103 - INFO - --- Analyzing Trading Segment 107/166 ---
2025-07-08 21:17:40,104 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,104 - INFO - Analyzing sub-segment from 0 to 29 with Hurst: 0.50
2025-07-08 21:17:40,104 - INFO - [_find_local_extrema] Received segment of size 29
2025-07-08 21:17:40,104 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,104 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,104 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,104 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,105 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,105 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,105 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,105 - INFO - --- Analyzing Trading Segment 108/166 ---
2025-07-08 21:17:40,106 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,106 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,106 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,106 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,106 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,106 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,106 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,106 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,106 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,106 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,106 - INFO - --- Analyzing Trading Segment 109/166 ---
2025-07-08 21:17:40,107 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,107 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,107 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,107 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,107 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,107 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,107 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,107 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,108 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,108 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,108 - INFO - --- Analyzing Trading Segment 110/166 ---
2025-07-08 21:17:40,108 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,108 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,108 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,108 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,108 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,108 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,108 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,108 - INFO - --- Analyzing Trading Segment 111/166 ---
2025-07-08 21:17:40,109 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,109 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,109 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,109 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,109 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,109 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,109 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,110 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,110 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,110 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,110 - INFO - --- Analyzing Trading Segment 112/166 ---
2025-07-08 21:17:40,110 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,110 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,110 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,110 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,111 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,111 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,111 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,111 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,111 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,111 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,111 - INFO - --- Analyzing Trading Segment 113/166 ---
2025-07-08 21:17:40,111 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,112 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,112 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,112 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,112 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,112 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,112 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,112 - INFO - --- Analyzing Trading Segment 114/166 ---
2025-07-08 21:17:40,112 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,112 - INFO - Analyzing sub-segment from 0 to 29 with Hurst: 0.50
2025-07-08 21:17:40,112 - INFO - [_find_local_extrema] Received segment of size 29
2025-07-08 21:17:40,112 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,113 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,113 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,113 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,113 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,113 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,113 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,113 - INFO - --- Analyzing Trading Segment 115/166 ---
2025-07-08 21:17:40,114 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,114 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,114 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,114 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,114 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,114 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,114 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,114 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,114 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,114 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,114 - INFO - --- Analyzing Trading Segment 116/166 ---
2025-07-08 21:17:40,115 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,115 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,115 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,115 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,115 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,115 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,115 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,116 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,116 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,116 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,116 - INFO - --- Analyzing Trading Segment 117/166 ---
2025-07-08 21:17:40,116 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,116 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,116 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,116 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,116 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,116 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,116 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,116 - INFO - --- Analyzing Trading Segment 118/166 ---
2025-07-08 21:17:40,117 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,117 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,117 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,117 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,117 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,117 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,117 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,118 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,118 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,118 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,118 - INFO - --- Analyzing Trading Segment 119/166 ---
2025-07-08 21:17:40,118 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,118 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,119 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,119 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,119 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,119 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,119 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,119 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,119 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,119 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,119 - INFO - --- Analyzing Trading Segment 120/166 ---
2025-07-08 21:17:40,120 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,120 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,120 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,120 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,120 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,120 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,120 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,120 - INFO - --- Analyzing Trading Segment 121/166 ---
2025-07-08 21:17:40,120 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,120 - INFO - Analyzing sub-segment from 0 to 29 with Hurst: 0.50
2025-07-08 21:17:40,120 - INFO - [_find_local_extrema] Received segment of size 29
2025-07-08 21:17:40,121 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,121 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,121 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,121 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,121 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,121 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,121 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,121 - INFO - --- Analyzing Trading Segment 122/166 ---
2025-07-08 21:17:40,122 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,122 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,122 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,122 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,122 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,122 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,122 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,122 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,122 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,122 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,122 - INFO - --- Analyzing Trading Segment 123/166 ---
2025-07-08 21:17:40,123 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,123 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,123 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,123 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,123 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,123 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,123 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,124 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,124 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,124 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,124 - INFO - --- Analyzing Trading Segment 124/166 ---
2025-07-08 21:17:40,124 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,124 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,124 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,124 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,124 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,124 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,124 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,124 - INFO - --- Analyzing Trading Segment 125/166 ---
2025-07-08 21:17:40,125 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,125 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,125 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,125 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,125 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,125 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,125 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,126 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,126 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,126 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,126 - INFO - --- Analyzing Trading Segment 126/166 ---
2025-07-08 21:17:40,127 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,127 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,127 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,127 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,127 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,127 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,127 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,127 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,127 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,127 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,127 - INFO - --- Analyzing Trading Segment 127/166 ---
2025-07-08 21:17:40,128 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,128 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,128 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,128 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,128 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,128 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,128 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,128 - INFO - --- Analyzing Trading Segment 128/166 ---
2025-07-08 21:17:40,129 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,129 - INFO - Analyzing sub-segment from 0 to 30 with Hurst: 0.50
2025-07-08 21:17:40,129 - INFO - [_find_local_extrema] Received segment of size 30
2025-07-08 21:17:40,129 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,129 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,129 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,129 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,129 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,129 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,129 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,129 - INFO - --- Analyzing Trading Segment 129/166 ---
2025-07-08 21:17:40,130 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,130 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,130 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,130 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,130 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,130 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,130 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,130 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,131 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,131 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,131 - INFO - --- Analyzing Trading Segment 130/166 ---
2025-07-08 21:17:40,131 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,131 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,131 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,131 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,132 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,132 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,132 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,132 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,132 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,132 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,132 - INFO - --- Analyzing Trading Segment 131/166 ---
2025-07-08 21:17:40,132 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,132 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,133 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,133 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,133 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,133 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,133 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,133 - INFO - --- Analyzing Trading Segment 132/166 ---
2025-07-08 21:17:40,133 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,133 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,133 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,133 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,134 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,134 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,134 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,134 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,134 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,134 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,134 - INFO - --- Analyzing Trading Segment 133/166 ---
2025-07-08 21:17:40,135 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,135 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,135 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,135 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,135 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,135 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,135 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,135 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,135 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,135 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,135 - INFO - --- Analyzing Trading Segment 134/166 ---
2025-07-08 21:17:40,136 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,136 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,136 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,136 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,136 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,136 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,136 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,136 - INFO - --- Analyzing Trading Segment 135/166 ---
2025-07-08 21:17:40,137 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,137 - INFO - Analyzing sub-segment from 0 to 29 with Hurst: 0.50
2025-07-08 21:17:40,137 - INFO - [_find_local_extrema] Received segment of size 29
2025-07-08 21:17:40,137 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,137 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,137 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,137 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,137 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,137 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,137 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,137 - INFO - --- Analyzing Trading Segment 136/166 ---
2025-07-08 21:17:40,138 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,138 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,138 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,138 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,138 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,138 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,138 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,139 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,139 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,139 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,139 - INFO - --- Analyzing Trading Segment 137/166 ---
2025-07-08 21:17:40,139 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,139 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,139 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,140 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,140 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,140 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,140 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,140 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,140 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,140 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,140 - INFO - --- Analyzing Trading Segment 138/166 ---
2025-07-08 21:17:40,141 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,141 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,141 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,141 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,141 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,141 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,141 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,141 - INFO - --- Analyzing Trading Segment 139/166 ---
2025-07-08 21:17:40,142 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,142 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,142 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,142 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,142 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,142 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,142 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,142 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,142 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,142 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,142 - INFO - --- Analyzing Trading Segment 140/166 ---
2025-07-08 21:17:40,143 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,143 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,143 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,143 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,143 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,143 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,143 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,144 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,144 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,144 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,144 - INFO - --- Analyzing Trading Segment 141/166 ---
2025-07-08 21:17:40,144 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,144 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,144 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,144 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,144 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,144 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,144 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,144 - INFO - --- Analyzing Trading Segment 142/166 ---
2025-07-08 21:17:40,145 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,145 - INFO - Analyzing sub-segment from 0 to 29 with Hurst: 0.50
2025-07-08 21:17:40,145 - INFO - [_find_local_extrema] Received segment of size 29
2025-07-08 21:17:40,145 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,145 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,145 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,145 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,146 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,146 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,146 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,146 - INFO - --- Analyzing Trading Segment 143/166 ---
2025-07-08 21:17:40,146 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,146 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,146 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,146 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,147 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,147 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,147 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,147 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,147 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,147 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,147 - INFO - --- Analyzing Trading Segment 144/166 ---
2025-07-08 21:17:40,148 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,148 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,148 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,148 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,148 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,148 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,148 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,148 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,148 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,148 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,148 - INFO - --- Analyzing Trading Segment 145/166 ---
2025-07-08 21:17:40,149 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,149 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,149 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,149 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,149 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,149 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,149 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,149 - INFO - --- Analyzing Trading Segment 146/166 ---
2025-07-08 21:17:40,150 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,150 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,150 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,150 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,150 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,150 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,150 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,150 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,150 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,150 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,150 - INFO - --- Analyzing Trading Segment 147/166 ---
2025-07-08 21:17:40,151 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,151 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,151 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,151 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,151 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,151 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,151 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,152 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,152 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,152 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,152 - INFO - --- Analyzing Trading Segment 148/166 ---
2025-07-08 21:17:40,152 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,152 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,152 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,152 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,152 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,152 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,152 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,152 - INFO - --- Analyzing Trading Segment 149/166 ---
2025-07-08 21:17:40,153 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,153 - INFO - Analyzing sub-segment from 0 to 29 with Hurst: 0.50
2025-07-08 21:17:40,153 - INFO - [_find_local_extrema] Received segment of size 29
2025-07-08 21:17:40,153 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,153 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,153 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,153 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,154 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,154 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,154 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,154 - INFO - --- Analyzing Trading Segment 150/166 ---
2025-07-08 21:17:40,154 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,155 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,155 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,155 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,155 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,155 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,155 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,155 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,155 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,155 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,155 - INFO - --- Analyzing Trading Segment 151/166 ---
2025-07-08 21:17:40,156 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,156 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,156 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,156 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,156 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,156 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,156 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,156 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,156 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,156 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,156 - INFO - --- Analyzing Trading Segment 152/166 ---
2025-07-08 21:17:40,157 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,157 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,157 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,157 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,157 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,157 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,157 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,157 - INFO - --- Analyzing Trading Segment 153/166 ---
2025-07-08 21:17:40,158 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,158 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,158 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,158 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,158 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,158 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,158 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,158 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,158 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,158 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,158 - INFO - --- Analyzing Trading Segment 154/166 ---
2025-07-08 21:17:40,159 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,159 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,159 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,159 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,159 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,159 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,159 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,160 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,160 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,160 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,160 - INFO - --- Analyzing Trading Segment 155/166 ---
2025-07-08 21:17:40,160 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,160 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,160 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,160 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,160 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,160 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,160 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,160 - INFO - --- Analyzing Trading Segment 156/166 ---
2025-07-08 21:17:40,161 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,161 - INFO - Analyzing sub-segment from 0 to 30 with Hurst: 0.50
2025-07-08 21:17:40,161 - INFO - [_find_local_extrema] Received segment of size 30
2025-07-08 21:17:40,161 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,161 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,161 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,161 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,162 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,162 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,162 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,162 - INFO - --- Analyzing Trading Segment 157/166 ---
2025-07-08 21:17:40,163 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,163 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,163 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,163 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,163 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,163 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,163 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,163 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,163 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,163 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,163 - INFO - --- Analyzing Trading Segment 158/166 ---
2025-07-08 21:17:40,164 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,164 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,164 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,164 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,164 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,164 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,164 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,165 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,165 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,165 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,165 - INFO - --- Analyzing Trading Segment 159/166 ---
2025-07-08 21:17:40,165 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,165 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,165 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,165 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,165 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,165 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,165 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,165 - INFO - --- Analyzing Trading Segment 160/166 ---
2025-07-08 21:17:40,166 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,166 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,166 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,166 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,166 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,166 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,166 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,167 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,167 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,167 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,167 - INFO - --- Analyzing Trading Segment 161/166 ---
2025-07-08 21:17:40,167 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,167 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,167 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,167 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,168 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,168 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,168 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,168 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,168 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,168 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,168 - INFO - --- Analyzing Trading Segment 162/166 ---
2025-07-08 21:17:40,169 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,169 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,169 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,169 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,169 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,169 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,169 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,169 - INFO - --- Analyzing Trading Segment 163/166 ---
2025-07-08 21:17:40,169 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,169 - INFO - Analyzing sub-segment from 0 to 29 with Hurst: 0.50
2025-07-08 21:17:40,169 - INFO - [_find_local_extrema] Received segment of size 29
2025-07-08 21:17:40,169 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,170 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,170 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,170 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,170 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,170 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,170 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,170 - INFO - --- Analyzing Trading Segment 164/166 ---
2025-07-08 21:17:40,171 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,171 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,171 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,171 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,171 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,171 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,171 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,171 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,171 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,171 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,171 - INFO - --- Analyzing Trading Segment 165/166 ---
2025-07-08 21:17:40,172 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,172 - INFO - Analyzing sub-segment from 0 to 31 with Hurst: 0.50
2025-07-08 21:17:40,172 - INFO - [_find_local_extrema] Received segment of size 31
2025-07-08 21:17:40,172 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,172 - INFO - [_find_pivots_cwt] Found 0 raw pivots via CWT.
2025-07-08 21:17:40,172 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,172 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,173 - WARNING - No extrema found with ATR. Falling back to smaller percentage.
2025-07-08 21:17:40,173 - ERROR - No extrema found even after fallback.
2025-07-08 21:17:40,173 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,173 - INFO - --- Analyzing Trading Segment 166/166 ---
2025-07-08 21:17:40,173 - WARNING - No candidate wave structures found. Analyzing full segment as fallback.
2025-07-08 21:17:40,173 - INFO - Analyzing sub-segment from 0 to 1 with Hurst: 0.50
2025-07-08 21:17:40,173 - INFO - [_find_local_extrema] Received segment of size 1
2025-07-08 21:17:40,173 - WARNING - Segment too small to find extrema, using fallback.
2025-07-08 21:17:40,173 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,173 - WARNING - Price range is zero. Cannot find extrema.
2025-07-08 21:17:40,173 - INFO - Found 0 extrema for this sub-segment.
2025-07-08 21:17:40,173 - INFO - Applying ML Validator to all generated hypotheses.
2025-07-08 21:17:40,174 - WARNING - No monthly patterns found. Cannot perform multi-timeframe analysis. Falling back to daily.
2025-07-08 21:17:40,174 - INFO - [find_elliott_wave_patterns] Starting analysis for dataframe of size 5582.
2025-07-08 21:17:40,174 - INFO - [_split_df_by_gaps] Time differences between consecutive data points:
date
2000-04-19      NaT
2000-04-20   1 days
2000-04-21   1 days
2000-04-24   3 days
2000-04-25   1 days
              ...  
2025-07-02   1 days
2025-07-03   1 days
2025-07-04   1 days
2025-07-07   3 days
2025-07-08   1 days
Name: date, Length: 5582, dtype: timedelta64[ns]
2025-07-08 21:17:40,174 - INFO - [_split_df_by_gaps] Gaps identified (True if gap > 30 days):
date
2000-04-19    False
2000-04-20    False
2000-04-21    False
2000-04-24    False
2000-04-25    False
              ...  
2025-07-02    False
2025-07-03    False
2025-07-04    False
2025-07-07    False
2025-07-08    False
Name: date, Length: 5582, dtype: bool
2025-07-08 21:17:40,174 - INFO - Split dataframe into 2 segments due to trading gaps > 30 days.
2025-07-08 21:17:40,174 - INFO - --- Analyzing Trading Segment 1/2 ---
2025-07-08 21:17:40,267 - INFO - Analyzing sub-segment from 2688 to 2866 with Hurst: 0.82
2025-07-08 21:17:40,267 - INFO - [_find_local_extrema] Received segment of size 178
2025-07-08 21:17:40,267 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,268 - INFO - [_find_pivots_cwt] Found 30 raw pivots via CWT.
2025-07-08 21:17:40,268 - INFO - [_find_local_extrema] Returning 23 alternating extrema.
2025-07-08 21:17:40,268 - INFO - Found 23 extrema for this sub-segment.
2025-07-08 21:17:40,272 - INFO - Analyzing sub-segment from 2868 to 2928 with Hurst: 0.90
2025-07-08 21:17:40,272 - INFO - [_find_local_extrema] Received segment of size 60
2025-07-08 21:17:40,272 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,272 - INFO - [_find_pivots_cwt] Found 16 raw pivots via CWT.
2025-07-08 21:17:40,272 - INFO - [_find_local_extrema] Returning 10 alternating extrema.
2025-07-08 21:17:40,272 - INFO - Found 10 extrema for this sub-segment.
2025-07-08 21:17:40,274 - INFO - Analyzing sub-segment from 2938 to 2965 with Hurst: 0.60
2025-07-08 21:17:40,274 - INFO - [_find_local_extrema] Received segment of size 27
2025-07-08 21:17:40,274 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,274 - INFO - [_find_pivots_cwt] Found 9 raw pivots via CWT.
2025-07-08 21:17:40,274 - INFO - [_find_local_extrema] Returning 7 alternating extrema.
2025-07-08 21:17:40,274 - INFO - Found 7 extrema for this sub-segment.
2025-07-08 21:17:40,274 - INFO - Analyzing sub-segment from 3011 to 3032 with Hurst: 0.70
2025-07-08 21:17:40,274 - INFO - [_find_local_extrema] Received segment of size 21
2025-07-08 21:17:40,275 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,275 - INFO - [_find_pivots_cwt] Found 5 raw pivots via CWT.
2025-07-08 21:17:40,275 - INFO - [_find_local_extrema] Returning 4 alternating extrema.
2025-07-08 21:17:40,275 - INFO - Found 4 extrema for this sub-segment.
2025-07-08 21:17:40,275 - INFO - Analyzing sub-segment from 3923 to 3985 with Hurst: 0.84
2025-07-08 21:17:40,275 - INFO - [_find_local_extrema] Received segment of size 62
2025-07-08 21:17:40,275 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,275 - INFO - [_find_pivots_cwt] Found 12 raw pivots via CWT.
2025-07-08 21:17:40,275 - INFO - [_find_local_extrema] Returning 10 alternating extrema.
2025-07-08 21:17:40,275 - INFO - Found 10 extrema for this sub-segment.
2025-07-08 21:17:40,276 - INFO - --- Analyzing Trading Segment 2/2 ---
2025-07-08 21:17:40,305 - INFO - Analyzing sub-segment from 1 to 34 with Hurst: 0.70
2025-07-08 21:17:40,305 - INFO - [_find_local_extrema] Received segment of size 33
2025-07-08 21:17:40,305 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,306 - INFO - [_find_pivots_cwt] Found 6 raw pivots via CWT.
2025-07-08 21:17:40,306 - INFO - [_find_local_extrema] Returning 6 alternating extrema.
2025-07-08 21:17:40,306 - INFO - Found 6 extrema for this sub-segment.
2025-07-08 21:17:40,306 - INFO - Analyzing sub-segment from 817 to 867 with Hurst: 0.86
2025-07-08 21:17:40,306 - INFO - [_find_local_extrema] Received segment of size 50
2025-07-08 21:17:40,306 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,306 - INFO - [_find_pivots_cwt] Found 13 raw pivots via CWT.
2025-07-08 21:17:40,306 - INFO - [_find_local_extrema] Returning 11 alternating extrema.
2025-07-08 21:17:40,306 - INFO - Found 11 extrema for this sub-segment.
2025-07-08 21:17:40,308 - INFO - Analyzing sub-segment from 874 to 899 with Hurst: 0.70
2025-07-08 21:17:40,308 - INFO - [_find_local_extrema] Received segment of size 25
2025-07-08 21:17:40,308 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,308 - INFO - [_find_pivots_cwt] Found 2 raw pivots via CWT.
2025-07-08 21:17:40,308 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,308 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,308 - INFO - [_find_local_extrema] Returning 1 alternating extrema.
2025-07-08 21:17:40,308 - INFO - Found 1 extrema for this sub-segment.
2025-07-08 21:17:40,308 - INFO - Analyzing sub-segment from 904 to 939 with Hurst: 0.83
2025-07-08 21:17:40,308 - INFO - [_find_local_extrema] Received segment of size 35
2025-07-08 21:17:40,308 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,309 - INFO - [_find_pivots_cwt] Found 8 raw pivots via CWT.
2025-07-08 21:17:40,309 - INFO - [_find_local_extrema] Returning 7 alternating extrema.
2025-07-08 21:17:40,309 - INFO - Found 7 extrema for this sub-segment.
2025-07-08 21:17:40,309 - INFO - Analyzing sub-segment from 3104 to 3142 with Hurst: 0.78
2025-07-08 21:17:40,309 - INFO - [_find_local_extrema] Received segment of size 38
2025-07-08 21:17:40,309 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,310 - INFO - [_find_pivots_cwt] Found 9 raw pivots via CWT.
2025-07-08 21:17:40,310 - INFO - [_find_local_extrema] Returning 7 alternating extrema.
2025-07-08 21:17:40,310 - INFO - Found 7 extrema for this sub-segment.
2025-07-08 21:17:40,310 - INFO - Analyzing sub-segment from 3143 to 3191 with Hurst: 0.75
2025-07-08 21:17:40,310 - INFO - [_find_local_extrema] Received segment of size 48
2025-07-08 21:17:40,310 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,310 - INFO - [_find_pivots_cwt] Found 8 raw pivots via CWT.
2025-07-08 21:17:40,310 - INFO - [_find_local_extrema] Returning 5 alternating extrema.
2025-07-08 21:17:40,310 - INFO - Found 5 extrema for this sub-segment.
2025-07-08 21:17:40,311 - INFO - Analyzing sub-segment from 3212 to 3238 with Hurst: 0.67
2025-07-08 21:17:40,311 - INFO - [_find_local_extrema] Received segment of size 26
2025-07-08 21:17:40,311 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,311 - INFO - [_find_pivots_cwt] Found 5 raw pivots via CWT.
2025-07-08 21:17:40,311 - INFO - [_find_local_extrema] Returning 4 alternating extrema.
2025-07-08 21:17:40,311 - INFO - Found 4 extrema for this sub-segment.
2025-07-08 21:17:40,311 - INFO - Analyzing sub-segment from 3257 to 3285 with Hurst: 0.71
2025-07-08 21:17:40,311 - INFO - [_find_local_extrema] Received segment of size 28
2025-07-08 21:17:40,311 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,311 - INFO - [_find_pivots_cwt] Found 8 raw pivots via CWT.
2025-07-08 21:17:40,311 - INFO - [_find_local_extrema] Returning 6 alternating extrema.
2025-07-08 21:17:40,311 - INFO - Found 6 extrema for this sub-segment.
2025-07-08 21:17:40,312 - INFO - Analyzing sub-segment from 3300 to 3320 with Hurst: 0.62
2025-07-08 21:17:40,312 - INFO - [_find_local_extrema] Received segment of size 20
2025-07-08 21:17:40,312 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,312 - INFO - [_find_pivots_cwt] Found 6 raw pivots via CWT.
2025-07-08 21:17:40,312 - INFO - [_find_local_extrema] Returning 6 alternating extrema.
2025-07-08 21:17:40,312 - INFO - Found 6 extrema for this sub-segment.
2025-07-08 21:17:40,312 - INFO - Analyzing sub-segment from 4461 to 4534 with Hurst: 0.89
2025-07-08 21:17:40,312 - INFO - [_find_local_extrema] Received segment of size 73
2025-07-08 21:17:40,312 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,313 - INFO - [_find_pivots_cwt] Found 11 raw pivots via CWT.
2025-07-08 21:17:40,313 - INFO - [_find_local_extrema] Returning 8 alternating extrema.
2025-07-08 21:17:40,313 - INFO - Found 8 extrema for this sub-segment.
2025-07-08 21:17:40,313 - WARNING - No primary monthly pattern found. Falling back to best daily pattern analysis.
2025-07-08 21:17:40,313 - INFO - [find_elliott_wave_patterns] Starting analysis for dataframe of size 5582.
2025-07-08 21:17:40,314 - INFO - [_split_df_by_gaps] Time differences between consecutive data points:
date
2000-04-19      NaT
2000-04-20   1 days
2000-04-21   1 days
2000-04-24   3 days
2000-04-25   1 days
              ...  
2025-07-02   1 days
2025-07-03   1 days
2025-07-04   1 days
2025-07-07   3 days
2025-07-08   1 days
Name: date, Length: 5582, dtype: timedelta64[ns]
2025-07-08 21:17:40,314 - INFO - [_split_df_by_gaps] Gaps identified (True if gap > 30 days):
date
2000-04-19    False
2000-04-20    False
2000-04-21    False
2000-04-24    False
2000-04-25    False
              ...  
2025-07-02    False
2025-07-03    False
2025-07-04    False
2025-07-07    False
2025-07-08    False
Name: date, Length: 5582, dtype: bool
2025-07-08 21:17:40,314 - INFO - Split dataframe into 2 segments due to trading gaps > 30 days.
2025-07-08 21:17:40,314 - INFO - --- Analyzing Trading Segment 1/2 ---
2025-07-08 21:17:40,337 - INFO - Analyzing sub-segment from 2688 to 2866 with Hurst: 0.82
2025-07-08 21:17:40,337 - INFO - [_find_local_extrema] Received segment of size 178
2025-07-08 21:17:40,337 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,338 - INFO - [_find_pivots_cwt] Found 30 raw pivots via CWT.
2025-07-08 21:17:40,338 - INFO - [_find_local_extrema] Returning 23 alternating extrema.
2025-07-08 21:17:40,338 - INFO - Found 23 extrema for this sub-segment.
2025-07-08 21:17:40,341 - INFO - Analyzing sub-segment from 2868 to 2928 with Hurst: 0.90
2025-07-08 21:17:40,341 - INFO - [_find_local_extrema] Received segment of size 60
2025-07-08 21:17:40,341 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,342 - INFO - [_find_pivots_cwt] Found 16 raw pivots via CWT.
2025-07-08 21:17:40,342 - INFO - [_find_local_extrema] Returning 10 alternating extrema.
2025-07-08 21:17:40,342 - INFO - Found 10 extrema for this sub-segment.
2025-07-08 21:17:40,343 - INFO - Analyzing sub-segment from 2938 to 2965 with Hurst: 0.60
2025-07-08 21:17:40,343 - INFO - [_find_local_extrema] Received segment of size 27
2025-07-08 21:17:40,344 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,344 - INFO - [_find_pivots_cwt] Found 9 raw pivots via CWT.
2025-07-08 21:17:40,344 - INFO - [_find_local_extrema] Returning 7 alternating extrema.
2025-07-08 21:17:40,344 - INFO - Found 7 extrema for this sub-segment.
2025-07-08 21:17:40,344 - INFO - Analyzing sub-segment from 3011 to 3032 with Hurst: 0.70
2025-07-08 21:17:40,344 - INFO - [_find_local_extrema] Received segment of size 21
2025-07-08 21:17:40,345 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,345 - INFO - [_find_pivots_cwt] Found 5 raw pivots via CWT.
2025-07-08 21:17:40,345 - INFO - [_find_local_extrema] Returning 4 alternating extrema.
2025-07-08 21:17:40,345 - INFO - Found 4 extrema for this sub-segment.
2025-07-08 21:17:40,345 - INFO - Analyzing sub-segment from 3923 to 3985 with Hurst: 0.84
2025-07-08 21:17:40,345 - INFO - [_find_local_extrema] Received segment of size 62
2025-07-08 21:17:40,345 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,345 - INFO - [_find_pivots_cwt] Found 12 raw pivots via CWT.
2025-07-08 21:17:40,345 - INFO - [_find_local_extrema] Returning 10 alternating extrema.
2025-07-08 21:17:40,345 - INFO - Found 10 extrema for this sub-segment.
2025-07-08 21:17:40,346 - INFO - --- Analyzing Trading Segment 2/2 ---
2025-07-08 21:17:40,375 - INFO - Analyzing sub-segment from 1 to 34 with Hurst: 0.70
2025-07-08 21:17:40,375 - INFO - [_find_local_extrema] Received segment of size 33
2025-07-08 21:17:40,375 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,375 - INFO - [_find_pivots_cwt] Found 6 raw pivots via CWT.
2025-07-08 21:17:40,375 - INFO - [_find_local_extrema] Returning 6 alternating extrema.
2025-07-08 21:17:40,375 - INFO - Found 6 extrema for this sub-segment.
2025-07-08 21:17:40,376 - INFO - Analyzing sub-segment from 817 to 867 with Hurst: 0.86
2025-07-08 21:17:40,376 - INFO - [_find_local_extrema] Received segment of size 50
2025-07-08 21:17:40,376 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,376 - INFO - [_find_pivots_cwt] Found 13 raw pivots via CWT.
2025-07-08 21:17:40,376 - INFO - [_find_local_extrema] Returning 11 alternating extrema.
2025-07-08 21:17:40,376 - INFO - Found 11 extrema for this sub-segment.
2025-07-08 21:17:40,378 - INFO - Analyzing sub-segment from 874 to 899 with Hurst: 0.70
2025-07-08 21:17:40,378 - INFO - [_find_local_extrema] Received segment of size 25
2025-07-08 21:17:40,378 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,378 - INFO - [_find_pivots_cwt] Found 2 raw pivots via CWT.
2025-07-08 21:17:40,378 - WARNING - CWT method found < 3 pivots. Switching to ATR-based fallback.
2025-07-08 21:17:40,378 - INFO - [_find_local_extrema_fallback_atr] Using ATR-based fallback.
2025-07-08 21:17:40,378 - INFO - [_find_local_extrema] Returning 1 alternating extrema.
2025-07-08 21:17:40,378 - INFO - Found 1 extrema for this sub-segment.
2025-07-08 21:17:40,378 - INFO - Analyzing sub-segment from 904 to 939 with Hurst: 0.83
2025-07-08 21:17:40,378 - INFO - [_find_local_extrema] Received segment of size 35
2025-07-08 21:17:40,378 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,379 - INFO - [_find_pivots_cwt] Found 8 raw pivots via CWT.
2025-07-08 21:17:40,379 - INFO - [_find_local_extrema] Returning 7 alternating extrema.
2025-07-08 21:17:40,379 - INFO - Found 7 extrema for this sub-segment.
2025-07-08 21:17:40,379 - INFO - Analyzing sub-segment from 3104 to 3142 with Hurst: 0.78
2025-07-08 21:17:40,379 - INFO - [_find_local_extrema] Received segment of size 38
2025-07-08 21:17:40,379 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,380 - INFO - [_find_pivots_cwt] Found 9 raw pivots via CWT.
2025-07-08 21:17:40,380 - INFO - [_find_local_extrema] Returning 7 alternating extrema.
2025-07-08 21:17:40,380 - INFO - Found 7 extrema for this sub-segment.
2025-07-08 21:17:40,380 - INFO - Analyzing sub-segment from 3143 to 3191 with Hurst: 0.75
2025-07-08 21:17:40,380 - INFO - [_find_local_extrema] Received segment of size 48
2025-07-08 21:17:40,380 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,380 - INFO - [_find_pivots_cwt] Found 8 raw pivots via CWT.
2025-07-08 21:17:40,380 - INFO - [_find_local_extrema] Returning 5 alternating extrema.
2025-07-08 21:17:40,380 - INFO - Found 5 extrema for this sub-segment.
2025-07-08 21:17:40,381 - INFO - Analyzing sub-segment from 3212 to 3238 with Hurst: 0.67
2025-07-08 21:17:40,381 - INFO - [_find_local_extrema] Received segment of size 26
2025-07-08 21:17:40,381 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,381 - INFO - [_find_pivots_cwt] Found 5 raw pivots via CWT.
2025-07-08 21:17:40,381 - INFO - [_find_local_extrema] Returning 4 alternating extrema.
2025-07-08 21:17:40,381 - INFO - Found 4 extrema for this sub-segment.
2025-07-08 21:17:40,381 - INFO - Analyzing sub-segment from 3257 to 3285 with Hurst: 0.71
2025-07-08 21:17:40,381 - INFO - [_find_local_extrema] Received segment of size 28
2025-07-08 21:17:40,381 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,381 - INFO - [_find_pivots_cwt] Found 8 raw pivots via CWT.
2025-07-08 21:17:40,381 - INFO - [_find_local_extrema] Returning 6 alternating extrema.
2025-07-08 21:17:40,381 - INFO - Found 6 extrema for this sub-segment.
2025-07-08 21:17:40,381 - INFO - Analyzing sub-segment from 3300 to 3320 with Hurst: 0.62
2025-07-08 21:17:40,381 - INFO - [_find_local_extrema] Received segment of size 20
2025-07-08 21:17:40,381 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,382 - INFO - [_find_pivots_cwt] Found 6 raw pivots via CWT.
2025-07-08 21:17:40,382 - INFO - [_find_local_extrema] Returning 6 alternating extrema.
2025-07-08 21:17:40,382 - INFO - Found 6 extrema for this sub-segment.
2025-07-08 21:17:40,382 - INFO - Analyzing sub-segment from 4461 to 4534 with Hurst: 0.89
2025-07-08 21:17:40,382 - INFO - [_find_local_extrema] Received segment of size 73
2025-07-08 21:17:40,382 - INFO - [_find_pivots_cwt] Starting CWT-based pivot detection with wavelet 'mexh'.
2025-07-08 21:17:40,382 - INFO - [_find_pivots_cwt] Found 11 raw pivots via CWT.
2025-07-08 21:17:40,382 - INFO - [_find_local_extrema] Returning 8 alternating extrema.
2025-07-08 21:17:40,382 - INFO - Found 8 extrema for this sub-segment.
