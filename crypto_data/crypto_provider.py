"""
加密货币数据提供商
提供主流加密货币的实时和历史数据
"""

import asyncio
import aiohttp
import pandas as pd
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import logging
import json
from dataclasses import dataclass
import ccxt

logger = logging.getLogger(__name__)

@dataclass
class CryptoSymbol:
    """加密货币符号信息"""
    symbol: str
    base: str
    quote: str
    exchange: str
    precision: Dict[str, int]
    limits: Dict[str, Any]

class CryptoDataProvider:
    """加密货币数据提供商"""
    
    def __init__(self):
        # 初始化交易所
        self.exchanges = {
            'binance': ccxt.binance({
                'enableRateLimit': True,
                'options': {'defaultType': 'spot'}
            }),
            'coinbase': ccxt.coinbase({
                'enableRateLimit': True
            }),
            'kraken': ccxt.kraken({
                'enableRateLimit': True
            })
        }
        
        # 支持的加密货币
        self.supported_symbols = {
            'BTC/USDT': {'name': '比特币', 'exchanges': ['binance', 'coinbase']},
            'ETH/USDT': {'name': '以太坊', 'exchanges': ['binance', 'coinbase']},
            'BNB/USDT': {'name': '币安币', 'exchanges': ['binance']},
            'ADA/USDT': {'name': '艾达币', 'exchanges': ['binance', 'coinbase']},
            'SOL/USDT': {'name': '索拉纳', 'exchanges': ['binance', 'coinbase']},
            'DOT/USDT': {'name': '波卡', 'exchanges': ['binance', 'kraken']},
            'LINK/USDT': {'name': '链环', 'exchanges': ['binance', 'coinbase']},
            'MATIC/USDT': {'name': '马蹄', 'exchanges': ['binance', 'coinbase']},
            'UNI/USDT': {'name': 'Uniswap', 'exchanges': ['binance', 'coinbase']},
            'AVAX/USDT': {'name': '雪崩', 'exchanges': ['binance', 'coinbase']}
        }
        
        # 缓存
        self.price_cache = {}
        self.ohlcv_cache = {}
        self.cache_timeout = 60  # 秒
    
    async def get_ohlcv(self, 
                       symbol: str, 
                       timeframe: str = '1d',
                       since: Optional[datetime] = None,
                       limit: int = 1000,
                       exchange: str = 'binance') -> pd.DataFrame:
        """获取OHLCV数据"""
        
        try:
            # 检查缓存
            cache_key = f"{exchange}_{symbol}_{timeframe}_{limit}"
            if cache_key in self.ohlcv_cache:
                cache_time, cached_data = self.ohlcv_cache[cache_key]
                if (datetime.now() - cache_time).seconds < self.cache_timeout:
                    return cached_data
            
            # 获取交易所实例
            if exchange not in self.exchanges:
                raise ValueError(f"不支持的交易所: {exchange}")
            
            ex = self.exchanges[exchange]
            
            # 转换时间格式
            since_timestamp = None
            if since:
                since_timestamp = int(since.timestamp() * 1000)
            
            # 获取数据
            ohlcv = ex.fetch_ohlcv(
                symbol=symbol,
                timeframe=timeframe,
                since=since_timestamp,
                limit=limit
            )
            
            # 转换为DataFrame
            df = pd.DataFrame(
                ohlcv,
                columns=['timestamp', 'open', 'high', 'low', 'close', 'volume']
            )
            
            # 转换时间戳
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            # 缓存数据
            self.ohlcv_cache[cache_key] = (datetime.now(), df)
            
            return df
            
        except Exception as e:
            logger.error(f"获取OHLCV数据失败: {symbol} - {e}")
            return pd.DataFrame()
    
    async def get_ticker(self, symbol: str, exchange: str = 'binance') -> Dict[str, Any]:
        """获取实时行情"""
        
        try:
            # 检查缓存
            cache_key = f"{exchange}_{symbol}_ticker"
            if cache_key in self.price_cache:
                cache_time, cached_data = self.price_cache[cache_key]
                if (datetime.now() - cache_time).seconds < self.cache_timeout:
                    return cached_data
            
            # 获取交易所实例
            if exchange not in self.exchanges:
                raise ValueError(f"不支持的交易所: {exchange}")
            
            ex = self.exchanges[exchange]
            
            # 获取行情
            ticker = ex.fetch_ticker(symbol)
            
            # 格式化数据
            formatted_ticker = {
                'symbol': symbol,
                'timestamp': datetime.fromtimestamp(ticker['timestamp'] / 1000),
                'bid': ticker['bid'],
                'ask': ticker['ask'],
                'last': ticker['last'],
                'high': ticker['high'],
                'low': ticker['low'],
                'volume': ticker['baseVolume'],
                'quote_volume': ticker['quoteVolume'],
                'change': ticker['change'],
                'change_percent': ticker['percentage'],
                'exchange': exchange
            }
            
            # 缓存数据
            self.price_cache[cache_key] = (datetime.now(), formatted_ticker)
            
            return formatted_ticker
            
        except Exception as e:
            logger.error(f"获取行情失败: {symbol} - {e}")
            return {}
    
    async def get_orderbook(self, symbol: str, limit: int = 100, exchange: str = 'binance') -> Dict[str, Any]:
        """获取订单簿"""
        
        try:
            # 获取交易所实例
            if exchange not in self.exchanges:
                raise ValueError(f"不支持的交易所: {exchange}")
            
            ex = self.exchanges[exchange]
            
            # 获取订单簿
            orderbook = ex.fetch_order_book(symbol, limit)
            
            return {
                'symbol': symbol,
                'timestamp': datetime.now(),
                'bids': orderbook['bids'],
                'asks': orderbook['asks'],
                'exchange': exchange
            }
            
        except Exception as e:
            logger.error(f"获取订单簿失败: {symbol} - {e}")
            return {}
    
    async def get_trades(self, symbol: str, limit: int = 100, exchange: str = 'binance') -> List[Dict[str, Any]]:
        """获取最新交易"""
        
        try:
            # 获取交易所实例
            if exchange not in self.exchanges:
                raise ValueError(f"不支持的交易所: {exchange}")
            
            ex = self.exchanges[exchange]
            
            # 获取交易数据
            trades = ex.fetch_trades(symbol, limit=limit)
            
            # 格式化数据
            formatted_trades = []
            for trade in trades:
                formatted_trades.append({
                    'id': trade['id'],
                    'timestamp': datetime.fromtimestamp(trade['timestamp'] / 1000),
                    'price': trade['price'],
                    'amount': trade['amount'],
                    'side': trade['side'],
                    'exchange': exchange
                })
            
            return formatted_trades
            
        except Exception as e:
            logger.error(f"获取交易数据失败: {symbol} - {e}")
            return []
    
    async def get_market_data(self, exchange: str = 'binance') -> Dict[str, Any]:
        """获取市场概览数据"""
        
        try:
            # 获取交易所实例
            if exchange not in self.exchanges:
                raise ValueError(f"不支持的交易所: {exchange}")
            
            ex = self.exchanges[exchange]
            
            # 获取市场数据
            tickers = ex.fetch_tickers()
            
            # 筛选主要交易对
            market_data = {}
            for symbol, info in tickers.items():
                if symbol in self.supported_symbols:
                    market_data[symbol] = {
                        'price': info['last'],
                        'change': info['change'],
                        'change_percent': info['percentage'],
                        'volume': info['baseVolume'],
                        'quote_volume': info['quoteVolume'],
                        'high': info['high'],
                        'low': info['low']
                    }
            
            return {
                'exchange': exchange,
                'timestamp': datetime.now(),
                'markets': market_data
            }
            
        except Exception as e:
            logger.error(f"获取市场数据失败: {e}")
            return {}
    
    def get_supported_symbols(self) -> Dict[str, Dict[str, Any]]:
        """获取支持的加密货币列表"""
        return self.supported_symbols
    
    def get_supported_exchanges(self) -> List[str]:
        """获取支持的交易所列表"""
        return list(self.exchanges.keys())
    
    async def get_historical_data(self, 
                                symbol: str,
                                start_date: datetime,
                                end_date: datetime,
                                timeframe: str = '1d',
                                exchange: str = 'binance') -> pd.DataFrame:
        """获取历史数据"""
        
        try:
            # 计算时间范围
            days = (end_date - start_date).days
            
            # 根据时间范围选择合适的limit
            if timeframe == '1d':
                limit = min(days, 1000)
            elif timeframe == '1h':
                limit = min(days * 24, 1000)
            elif timeframe == '15m':
                limit = min(days * 96, 1000)
            else:
                limit = 1000
            
            # 获取数据
            df = await self.get_ohlcv(
                symbol=symbol,
                timeframe=timeframe,
                limit=limit,
                exchange=exchange
            )
            
            # 过滤时间范围
            if not df.empty:
                df = df[(df.index >= start_date) & (df.index <= end_date)]
            
            return df
            
        except Exception as e:
            logger.error(f"获取历史数据失败: {symbol} - {e}")
            return pd.DataFrame()
    
    async def get_crypto_news(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取加密货币新闻"""
        
        # 这里可以集成加密货币新闻API
        # 暂时返回示例数据
        return [
            {
                'title': '比特币突破关键阻力位',
                'summary': '比特币价格突破45000美元关键阻力位，市场情绪转为看涨',
                'timestamp': datetime.now() - timedelta(hours=2),
                'source': 'CoinDesk',
                'url': 'https://example.com/news/1'
            },
            {
                'title': '以太坊升级完成',
                'summary': '以太坊网络成功完成最新升级，交易费用显著降低',
                'timestamp': datetime.now() - timedelta(hours=5),
                'source': 'CryptoNews',
                'url': 'https://example.com/news/2'
            }
        ]
    
    async def get_fear_greed_index(self) -> Dict[str, Any]:
        """获取恐惧贪婪指数"""
        
        # 这里可以集成恐惧贪婪指数API
        # 暂时返回示例数据
        return {
            'value': 65,
            'classification': '贪婪',
            'timestamp': datetime.now(),
            'description': '市场情绪偏向贪婪，投资者风险偏好较高'
        }

# 便捷函数
async def get_btc_data(timeframe: str = '1d', limit: int = 100) -> pd.DataFrame:
    """获取比特币数据"""
    provider = CryptoDataProvider()
    return await provider.get_ohlcv('BTC/USDT', timeframe, limit=limit)

async def get_eth_data(timeframe: str = '1d', limit: int = 100) -> pd.DataFrame:
    """获取以太坊数据"""
    provider = CryptoDataProvider()
    return await provider.get_ohlcv('ETH/USDT', timeframe, limit=limit)

async def get_crypto_market_overview() -> Dict[str, Any]:
    """获取加密货币市场概览"""
    provider = CryptoDataProvider()
    return await provider.get_market_data()

if __name__ == "__main__":
    import asyncio
    
    async def test_crypto():
        provider = CryptoDataProvider()
        
        # 测试获取比特币数据
        btc_data = await get_btc_data()
        print(f"比特币数据: {len(btc_data)} 条记录")
        
        # 测试获取以太坊数据
        eth_data = await get_eth_data()
        print(f"以太坊数据: {len(eth_data)} 条记录")
        
        # 测试获取市场概览
        market_data = await get_crypto_market_overview()
        print(f"市场概览: {len(market_data.get('markets', {}))} 个交易对")
        
        # 测试获取实时行情
        btc_ticker = await provider.get_ticker('BTC/USDT')
        print(f"比特币当前价格: ${btc_ticker.get('last', 'N/A')}")
    
    asyncio.run(test_crypto())