import pandas as pd
import os
import sys

# Add project root to sys.path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from elliott_wave.core_patterns import _find_local_extrema

def analyze_specific_period(data_path: str, start_date: str, end_date: str):
    """Analyzes a specific period of stock data to check for Elliott Wave impulse rules."""
    print(f"--- Analyzing Stock Data for period {start_date} to {end_date} ---")

    # 1. Load and filter data
    try:
        df = pd.read_parquet(data_path)
        df.index = pd.to_datetime(df.index)
        period_df = df.loc[start_date:end_date]
        if period_df.empty:
            print("Error: No data found for the specified period.")
            return
        print(f"Loaded {len(period_df)} data points for the period.")
    except Exception as e:
        print(f"Error loading or filtering data: {e}")
        return

    # 2. Find pivots
    extrema = _find_local_extrema(period_df)
    if len(extrema) < 6:
        print(f"Found only {len(extrema)} pivots. Not enough for a 5-wave impulse structure.")
        print("Pivots found:", extrema)
        return

    print(f"Found {len(extrema)} pivots in the period. Checking for a 5-wave impulse structure...")
    # We will check the first 6 pivots which form the 5 waves (0-1-2-3-4-5)
    points = extrema[:6]
    
    print("\n--- Potential 5-Wave Impulse Structure Points ---")
    for i, p in enumerate(points):
        print(f"  Point {i}: {p['date'].strftime('%Y-%m-%d')} at price {p['price']:.2f} ({p['type']})")

    # 3. Check Elliott Wave Rules
    print("\n--- Elliott Wave Rule Validation ---")
    p0, p1, p2, p3, p4, p5 = [p['price'] for p in points]

    # Rule 1: Wave 2 cannot retrace more than 100% of Wave 1
    wave1_len = abs(p1 - p0)
    wave2_retracement = abs(p2 - p1) / wave1_len
    rule1_pass = p2 > p0 if p1 > p0 else p2 < p0
    print(f"Rule 1 (Wave 2 Retracement): Wave 2 retraced {wave2_retracement:.2%} of Wave 1. {'PASS' if rule1_pass else 'FAIL'}")

    # Rule 2: Wave 4 cannot enter the price territory of Wave 1
    rule2_pass = (p4 > p1) if (p1 > p0) else (p4 < p1)
    print(f"Rule 2 (Wave 4 Overlap): Wave 4 end ({p4:.2f}) vs Wave 1 end ({p1:.2f}). {'PASS' if rule2_pass else 'FAIL'}")

    # Rule 3: Wave 3 is never the shortest impulse wave
    wave3_len = abs(p3 - p2)
    wave5_len = abs(p5 - p4)
    rule3_pass = wave3_len > wave1_len and wave3_len > wave5_len
    print(f"Rule 3 (Wave 3 Shortest): Wave 1={wave1_len:.2f}, Wave 3={wave3_len:.2f}, Wave 5={wave5_len:.2f}. {'PASS' if rule3_pass else 'FAIL'}")

    # 4. Conclusion
    print("\n--- Conclusion ---")
    if rule1_pass and rule2_pass and rule3_pass:
        print("The structure for 2021 IS CONSISTENT with the rules of a 5-wave impulse structure.")
        print("Specifically, the segment from the first pivot to the last appears to be a valid impulse wave.")
    else:
        print("The structure for 2021 IS NOT CONSISTENT with the rules of a 5-wave impulse structure.")
        if not rule3_pass:
            print("Primary reason: Wave 3 is the shortest among the impulse waves (1, 3, 5), which violates a core rule.")
        if not rule2_pass:
            print("Secondary reason: Wave 4 overlaps with Wave 1.")

if __name__ == '__main__':
    data_file = os.path.join(os.path.dirname(__file__), 'stock_data', '000831_long_term.parquet')
    analyze_specific_period(data_file, '2021-01-01', '2021-12-31')
