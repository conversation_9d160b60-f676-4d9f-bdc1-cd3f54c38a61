import streamlit as st
import pandas as pd
import os
from datetime import datetime

# Assuming analyze_stock_data is in the same directory or accessible via sys.path
from analyze_stock import analyze_stock_data
from elliott_wave.elliott_wave_visualizer import plot_elliott_wave_patterns

# --- UI Configuration ---
st.set_page_config(layout="wide", page_title="Elliott Wave Analysis")
st.title("Elliott Wave Analysis with DRL Backtesting")

# --- Sidebar for User Inputs ---
st.sidebar.header("Input Parameters")
stock_code = st.sidebar.text_input("Stock Code (e.g., 000831)", "000831")

# Dummy file path for now, will be replaced with actual data acquisition
file_path = os.path.join(os.path.dirname(__file__), f'temp_stock_data_{stock_code}_20250701.parquet')

run_analysis_button = st.sidebar.button("Run Analysis")

# --- Main Content Area ---
if run_analysis_button:
    if not os.path.exists(file_path):
        st.error(f"Data file not found: {file_path}. Please ensure the data is available.")
    else:
        st.info(f"Analyzing {stock_code}...")
        
        with st.spinner("Running analysis..."):
            analysis_results = analyze_stock_data(stock_code, file_path)

        if analysis_results and "error" not in analysis_results:
            st.success("Analysis complete!")
            
            all_timeframes_data = analysis_results["all_timeframes_data"]
            all_final_patterns = analysis_results["all_final_patterns"]
            backtest_results_dict = analysis_results["backtest_results"]

            st.subheader("Overall Patterns (Ranked by Confidence)")
            if all_final_patterns:
                # Display top 10 patterns
                for i, p in enumerate(all_final_patterns[:10]):
                    st.write(f"{i+1}. {p['pattern_name']} ({p['timeframe']}) - Confidence: {p['confidence']:.4f}, MTA Valid: {p.get('mta_validation', 'N/A')}")
            else:
                st.info("No significant Elliott Wave patterns detected.")

            # Display plots and backtest results for each timeframe
            for timeframe, data_df in all_timeframes_data.items():
                st.subheader(f"Analysis for {timeframe.capitalize()} Timeframe")
                
                timeframe_patterns = [p for p in all_final_patterns if p.get('timeframe') == timeframe]
                if timeframe_patterns:
                    # Plotting
                    fig, _ = plot_elliott_wave_patterns(data_df, timeframe_patterns, {}, stock_code)
                    if fig:
                        st.plotly_chart(fig, use_container_width=True)
                    else:
                        st.warning(f"Failed to generate plot for {timeframe} timeframe.")

                    # Backtesting Results
                    if timeframe in backtest_results_dict:
                        st.write("Backtest Results:")
                        bt_res = backtest_results_dict[timeframe]['backtest_results']
                        st.write(f"Total Return: {bt_res.get('total_return', 'N/A'):.2f}%")
                        st.write(f"Max Drawdown: {bt_res.get('max_drawdown', 'N/A'):.2f}%")
                        st.write(f"Profit Factor: {bt_res.get('profit_factor', 'N/A'):.2f}")
                        
                        st.write("Trades (Top 5):")
                        for trade in bt_res.get('trades', [])[:5]:
                            st.write(f"- {trade.get('type')} {trade.get('shares')} shares at {trade.get('price'):.2f} on {trade.get('date')} (Pattern: {trade.get('pattern')}, Conf: {trade.get('confidence'):.2f})")
                    else:
                        st.info(f"No backtest results for {timeframe} timeframe.")
                else:
                    st.info(f"No patterns found for {timeframe} timeframe to display.")

        else:
            st.error(f"Analysis failed: {analysis_results.get('error', 'Unknown error')}")

else:
    st.info("Enter stock code and click 'Run Analysis' to start.")