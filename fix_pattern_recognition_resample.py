import re
import logging

file_path = "/Users/<USER>/Projects/stock/ai_elliott/elliott_wave/pattern_recognition.py"

with open(file_path, 'r') as f:
    content = f.read()

# Define the pattern to find the function definition and its initial logging statement
# This regex captures everything from the function definition up to and including the first logging.info call
pattern = r"""(def find_elliott_wave_patterns\(df: pd.DataFrame, ml_validator: 'MLValidator' = None, _extrema_override: list = None\) -> dict:\n    """\n    Identifies multiple hypotheses for Elliott Wave patterns, optionally using an ML validator.\n    """\n    logging.info\(f"\\[find_elliott_wave_patterns\\] Starting analysis for dataframe of size {\\len\\(df\\)}."\)\n)"""

# Define the replacement string, inserting the resample and new logging line
replacement = r"""\1    df = df.resample('D').ffill() # Ensure continuous daily data\n    logging.info(f"[find_elliott_wave_patterns] DataFrame after daily resampling and ffill: {len(df)}.")\n"""

# Perform the replacement
content = re.sub(pattern, replacement, content, count=1)

with open(file_path, 'w') as f:
    f.write(content)

print("Successfully modified find_elliott_wave_patterns in elliott_wave/pattern_recognition.py")