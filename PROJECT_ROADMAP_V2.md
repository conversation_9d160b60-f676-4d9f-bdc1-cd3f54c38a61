# PROJECT ROADMAP V2: ElliottAgents - An Intelligent Multi-Agent System

This document outlines the development plan to evolve the current Elliott Wave analysis tool into a sophisticated, multi-agent system as conceptualized in the reference paper "Large Language Models and the Elliott Wave Principle".

## Guiding Principles

- **Agent-Based Architecture**: Transition from a monolithic script to a collaborative multi-agent framework using `LangGraph`.
- **Continuous Learning**: Implement mechanisms for the system to learn from its performance and improve over time (DRL-based backtesting).
- **Explainable AI (XAI)**: Enhance the system's output with clear, context-aware explanations grounded in theory and data, leveraging RAG and a Knowledge Graph.
- **External Knowledge Integration**: Actively incorporate best practices, code examples, and insights from external high-quality sources to enhance the implementation.

---

## Phase 1: System "Agentification" & Workflow Automation

**Goal**: To refactor the current procedural execution into a robust, automated workflow driven by a coordinator agent.

- **Task 1.1: Introduce LangGraph Framework**
  - **Action**: Set up the main `LangGraph` state graph which will define the overall application state and workflow.
  - **File**: `main_agent.py` (new)

- **Task 1.2: Create the Data Analyst Agent**
  - **Action**: Encapsulate the data fetching logic from `fetch_full_history.py` into a dedicated agent node. This agent will be responsible for acquiring data when requested by the coordinator.
  - **Files**: `agents/data_agent.py` (new)

- **Task 1.3: Create the EWP Analyst Agent**
  - **Action**: Encapsulate the powerful pattern recognition logic from `elliott_wave/core_patterns.py` into its own agent node. It will receive data and produce a list of identified patterns.
  - **Files**: `agents/ewp_agent.py` (new)

- **Task 1.4: Implement the Coordinator Agent**
  - **Action**: Build the main control flow within the `LangGraph`. The coordinator will receive a user request (e.g., "Analyze stock 600111"), delegate the task to the Data Analyst, pass the result to the EWP Analyst, and collect the final analysis.
  - **File**: `main_agent.py`

## Phase 2: Knowledge Brain Implementation (RAG + Knowledge Graph)

**Goal**: To enable the system to store, retrieve, and reason about its findings, providing deep, context-aware explanations.

- **Task 2.1: Activate the Knowledge Graph**
  - **Action**: Create a `GraphDBManagerAgent` responsible for connecting to the Neo4j database.
  - **Action**: This agent will define a graph schema (e.g., `(Wave)-[:SUBWAVE_OF]->(Wave)`, `(Wave)-[:HAS_PIVOT]->(Pivot)`) and be responsible for writing the identified wave patterns and their relationships into the graph.
  - **Files**: `agents/graph_agent.py` (new), `database/neo4j_db.py` (update)

- **Task 2.2: Implement RAG for Explainability**
  - **Action**: Create a `ReportWriterAgent`.
  - **Action**: This agent will take the final, validated wave patterns. When generating the final textual summary, it will use Retrieval-Augmented Generation (RAG) to query both the Neo4j knowledge graph (for pattern relationships) and the text documents (`docs/`) to explain its findings, citing specific Elliott Wave rules.
  - **Files**: `agents/report_agent.py` (new), `context/rag_integration.py` (update)

## Phase 3: Self-Evolution Capability (DRL)

**Goal**: To create a feedback loop that allows the system to learn from its past successes and failures, automatically improving its own performance.

- **Task 3.1: Activate the Backtester Agent**
  - **Action**: Fully implement the `BacktesterAgent` using the `drl_backtesting` module.
  - **Action**: This agent will take a historical pattern and simulate the trades recommended by the forecast, comparing them against what actually happened.
  - **Files**: `agents/backtester_agent.py` (new), `drl_backtesting/backtester.py` (update)

- **Task 3.2: Create the Feedback Loop**
  - **Action**: The results from the `BacktesterAgent` (i.e., which patterns led to profitable trades) will be used to update the `MLValidator` model.
  - **Action**: This creates a true continuous learning loop, where the system's pattern recognition abilities get progressively better over time.
  - **Files**: `train_ml_validator.py` (update), `main_agent.py` (update to orchestrate the loop)
