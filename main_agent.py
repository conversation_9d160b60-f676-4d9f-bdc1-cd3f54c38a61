from typing import TypedDict, List, Dict, Any
import pandas as pd
from langchain_core.messages import BaseMessage, HumanMessage
from langgraph.graph import StateGraph, END
import os
import sys
import json

# --- Path Setup ---
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# --- Agent Imports ---
from agents.data_agent import fetch_data_node
from agents.ewp_agent import analyze_waves_node
from agents.report_agent import generate_report_node
from elliott_wave.elliott_wave_visualizer import plot_elliott_wave_patterns

# --- State Definition ---
class AgentState(TypedDict):
    stock_code: str
    messages: List[BaseMessage]
    data: pd.DataFrame
    patterns: dict
    report: str
    forecast_data: Dict[str, Any]
    similar_patterns: List[Dict[str, Any]]
    final_patterns: List[Dict[str, Any]]

# --- Agent Node Definitions ---
def coordinator_node(state: AgentState) -> dict:
    print("--- Entering Coordinator Node ---")
    last_message = state['messages'][-1]
    stock_code = last_message.content
    print(f"Task: Analyze stock {stock_code}")
    return {"stock_code": stock_code}

def visualizer_node(state: AgentState) -> dict:
    """Final node to generate the plot."""
    print("--- Entering Visualizer Node ---")
    stock_code = state['stock_code']
    daily_df = state['data']
    final_patterns = state['final_patterns']
    forecast_data = state['forecast_data']
    similar_patterns = state['similar_patterns']

    plot_filename = f"elliott_wave_{stock_code}_agent_analysis.html"
    fig, _ = plot_elliott_wave_patterns(daily_df, final_patterns, {}, stock_code, forecast_data, similar_patterns)
    if fig:
        fig.write_html(plot_filename)
        print(f"Successfully saved final plot to {plot_filename}")
    return {}

# --- Graph Definition ---
workflow = StateGraph(AgentState)

workflow.add_node("coordinator", coordinator_node)
workflow.add_node("data_analyst", fetch_data_node)
workflow.add_node("ewp_analyst", analyze_waves_node)
workflow.add_node("report_writer", generate_report_node)
workflow.add_node("visualizer", visualizer_node)

workflow.set_entry_point("coordinator")
workflow.add_edge("coordinator", "data_analyst")
workflow.add_edge("data_analyst", "ewp_analyst")
workflow.add_edge("ewp_analyst", "report_writer")
workflow.add_edge("report_writer", "visualizer")
workflow.add_edge("visualizer", END)

app = workflow.compile()

# --- Main Execution Block ---
if __name__ == '__main__':
    print("--- Starting ElliottAgents System v2.0 ---")
    initial_request = {
        "messages": [HumanMessage(content="600111")]
    }
    
    # Use invoke to get the final state of the graph after execution
    final_state = app.invoke(initial_request)

    print("\n--- Run Complete ---")
    print("\nFinal Report:")
    print(final_state.get('report', "No report was generated."))
