import pandas as pd
import numpy as np
import os
import logging
from elliott_wave.ml_validator import M<PERSON>Validator
from elliott_wave.core_patterns import find_elliott_wave_patterns

# --- Setup ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Constants ---
MODEL_PATH = "/Users/<USER>/Projects/stock/ai_elliott/drl_logs/ml_validator_model.joblib"
DATA_PATH = "/Users/<USER>/Projects/stock/ai_elliott/stock_data/000831.parquet"
CONFIDENCE_PERCENTILE_THRESHOLD = 0.75 # Use top 25% of patterns as 'valid'

def generate_labels(patterns: list) -> pd.Series:
    """
    Generates labels based on the pattern's own confidence score.
    Patterns in the top CONFIDENCE_PERCENTILE_THRESHOLD are labeled 1, others 0.
    """
    labels = []
    if not patterns:
        return pd.Series([], dtype=int)

    confidences = [p.get('confidence', 0.0) for p in patterns]
    if not confidences:
        return pd.Series([], dtype=int)

    # Calculate the confidence threshold based on the percentile
    confidence_threshold = pd.Series(confidences).quantile(CONFIDENCE_PERCENTILE_THRESHOLD)
    
    logging.info(f"Using confidence threshold of {confidence_threshold:.4f} ({CONFIDENCE_PERCENTILE_THRESHOLD*100:.0f}th percentile).")

    # Handle the edge case where the threshold is 0.0, we must accept some patterns.
    if confidence_threshold == 0.0:
        # Fallback: if all confidences are 0, this will still be 0.
        # If some are > 0, this will be a small positive number.
        confidence_threshold = pd.Series(confidences).quantile(0.9)
        logging.info(f"Initial threshold was 0.0, falling back to 90th percentile: {confidence_threshold:.4f}")

    for pattern in patterns:
        # If all scores are 0, this will label nothing as valid, which is correct.
        if pattern.get('confidence', 0.0) > 0 and pattern.get('confidence', 0.0) >= confidence_threshold:
            labels.append(1)
        else:
            labels.append(0)
            
    return pd.Series(labels)

def main():
    """
    Main function to load data, generate patterns, create labels,
    and train the ML validator model.
    """
    logging.info("Starting ML Validator training process...")

    # --- 1. Load Data ---
    if not os.path.exists(DATA_PATH):
        logging.error(f"Data file not found at {DATA_PATH}. Aborting.")
        return
    
    logging.info(f"Loading data from {DATA_PATH}")
    stock_df = pd.read_parquet(DATA_PATH)

    # FIX: If 'date' is not a column, reset the index.
    if 'date' not in stock_df.columns:
        logging.info("'date' column not found, resetting index.")
        stock_df.reset_index(inplace=True)

    # Ensure the date column is named 'date'
    if 'index' in stock_df.columns and 'date' not in stock_df.columns:
        stock_df.rename(columns={'index': 'date'}, inplace=True)
        
    # Ensure date column is in datetime format
    stock_df['date'] = pd.to_datetime(stock_df['date'])


    # --- 2. Recognize Patterns ---
    logging.info("Recognizing Elliott Wave patterns from data...")
    # Assuming find_elliott_wave_patterns takes the dataframe and returns a list of pattern dicts
    # We pass the full dataframe for a more thorough analysis
    patterns_result = find_elliott_wave_patterns(stock_df)
    
    # Flatten the dictionary of lists into a single list of patterns
    all_patterns = []
    if patterns_result and 'pattern_hypotheses' in patterns_result:
        for pattern_type, pattern_list in patterns_result['pattern_hypotheses'].items():
            all_patterns.extend(pattern_list)

    if not all_patterns:
        logging.warning("No patterns were recognized. Aborting training.")
        return
    logging.info(f"Recognized {len(all_patterns)} patterns in total.")

    # --- 3. Generate Labels ---
    logging.info("Generating labels for recognized patterns...")
    labels = generate_labels(all_patterns)
    
    valid_patterns_count = sum(labels)
    logging.info(f"Generated {len(labels)} labels. Found {valid_patterns_count} 'valid' patterns.")

    if valid_patterns_count < 5: # Need a minimum number of positive samples
        logging.warning("Insufficient number of 'valid' patterns to train a meaningful model. Aborting.")
        return

    # --- 4. Train Model ---
    ml_validator = MLValidator()
    
    logging.info("Preparing features for training...")
    X_train = ml_validator._prepare_features(all_patterns)
    y_train = labels

    # Ensure directory for model exists
    os.makedirs(os.path.dirname(MODEL_PATH), exist_ok=True)

    logging.info("Training the model...")
    ml_validator.train_model(X_train, y_train)

    # --- 5. Save Model ---
    if ml_validator.model:
        logging.info(f"Saving trained model to {MODEL_PATH}")
        ml_validator.save_model(MODEL_PATH)
        logging.info("Training process completed successfully.")
    else:
        logging.warning("Model training was skipped or failed. Model not saved.")

if __name__ == '__main__':
    main()