import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.neural_network import MLPClassifier
from sklearn.metrics import accuracy_score, classification_report
import logging
import warnings
import joblib

class MLValidator:
    def __init__(self):
        self.model = None
        self.scaler = None
        self.columns = None # To store feature columns during training
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    def _prepare_features(self, pattern_data: list) -> pd.DataFrame:
        """
        Prepares features for the ML model based on simplified pattern data.
        This version expects pattern_data to be a list of dictionaries, where each dictionary
        represents a pattern instance with keys like 'pattern_type', 'confidence', 'hurst_exponent',
        and potentially 'duration' or 'completion'.
        """
        all_features = []
        for pattern in pattern_data:
            features = {}
            features['confidence'] = pattern.get('confidence', 0.0)
            features['hurst'] = pattern.get('hurst_exponent', 0.5)
            
            # Convert pattern_type to numerical feature
            pattern_type_encoded = 0
            if 'pattern_type' in pattern:
                if "impulsive_bullish" == pattern['pattern_type']: pattern_type_encoded = 1
                elif "impulsive_bearish" == pattern['pattern_type']: pattern_type_encoded = 2
                elif "corrective_bearish" == pattern['pattern_type']: pattern_type_encoded = 3
                elif "corrective_flat_bullish" == pattern['pattern_type']: pattern_type_encoded = 4
                elif "corrective_flat_bearish" == pattern['pattern_type']: pattern_type_encoded = 5
                elif "corrective_triangle" == pattern['pattern_type']: pattern_type_encoded = 6
                elif "corrective_wxy_bullish" == pattern['pattern_type']: pattern_type_encoded = 7
                elif "corrective_wxy_bearish" == pattern['pattern_type']: pattern_type_encoded = 8
                elif "corrective_wxyxz_bullish" == pattern['pattern_type']: pattern_type_encoded = 9
                elif "corrective_wxyxz_bearish" == pattern['pattern_type']: pattern_type_encoded = 10
                elif "corrective_double_zigzag_bullish" == pattern['pattern_type']: pattern_type_encoded = 11
                elif "corrective_double_zigzag_bearish" == pattern['pattern_type']: pattern_type_encoded = 12
                elif "corrective_triple_zigzag_bullish" == pattern['pattern_type']: pattern_type_encoded = 13
                elif "corrective_triple_zigzag_bearish" == pattern['pattern_type']: pattern_type_encoded = 14
            features['pattern_type_encoded'] = pattern_type_encoded

            # Add features that might be present from TradingEnv
            features['remaining_duration'] = pattern.get('remaining_duration', 0.0)
            features['top_impulse_conf'] = pattern.get('top_impulse_conf', 0.0)
            features['top_corrective_conf'] = pattern.get('top_corrective_conf', 0.0)
            features['top_impulse_type'] = pattern.get('top_impulse_type', 0)
            features['top_corrective_type'] = pattern.get('top_corrective_type', 0)
            features['top_impulse_duration'] = pattern.get('top_impulse_duration', 0.0)
            features['top_corrective_duration'] = pattern.get('top_corrective_duration', 0.0)
            features['top_impulse_completion'] = pattern.get('top_impulse_completion', 0.0)
            features['top_corrective_completion'] = pattern.get('top_corrective_completion', 0.0)

            all_features.append(features)
        
        df = pd.DataFrame(all_features).fillna(0)
        # Align columns with training columns
        if self.columns is not None:
            # Add missing columns that were in training data, fill with 0
            for col in self.columns:
                if col not in df.columns:
                    df[col] = 0
            df = df[self.columns] # Reorder columns to match training order
        return df

    def train_model(self, X: pd.DataFrame, y: pd.Series):
        """
        with warnings.catch_warnings():
            warnings.simplefilter('ignore', category=UserWarning)
            warnings.simplefilter('ignore', category=FutureWarning)
        Trains a simple MLPClassifier model.
        """
        logging.info("Starting model training...")
        self.scaler = StandardScaler()
        self.columns = X.columns.tolist()
        X_scaled = self.scaler.fit_transform(X[self.columns])
        
        if len(X) < 2 or len(y) < 2: # Minimum 2 samples for R^2 score, and generally for meaningful training
            logging.warning("Not enough samples to train the ML model. Skipping training.")
            self.model = None
            self.scaler = None
            self.columns = None
            return

        self.model = MLPClassifier(hidden_layer_sizes=(10, 5), max_iter=1000, random_state=42, early_stopping=True, validation_fraction=0.25)
        self.model.fit(X_scaled, y)
        logging.info("Model training complete.")

    def save_model(self, path: str):
        """
        Saves the trained model and scaler to a file.
        """
        if self.model is None or self.scaler is None:
            logging.error("No model has been trained yet. Cannot save.")
            return
        joblib.dump({'model': self.model, 'scaler': self.scaler, 'columns': self.columns}, path)
        logging.info(f"Model saved to {path}")

    def load_model(self, path: str):
        """
        Loads a pre-trained model and scaler from a file.
        """
        try:
            data = joblib.load(path)
            self.model = data['model']
            self.scaler = data['scaler']
            self.columns = data['columns']
            logging.info(f"Model loaded from {path}")
        except FileNotFoundError:
            logging.error(f"Model file not found at {path}")
        except Exception as e:
            logging.error(f"An error occurred while loading the model: {e}")

    def predict_validity(self, pattern_data: list) -> list:
        """
        Predicts the validity of detected patterns.
        """
        if self.model is None or self.scaler is None:
            logging.error("Model not loaded or trained. Cannot make predictions.")
            return pattern_data # Return original data without scores

        X_predict = self._prepare_features(pattern_data)
        if X_predict.empty: return pattern_data

        X_predict_scaled = self.scaler.transform(X_predict[self.columns])
        probabilities = self.model.predict_proba(X_predict_scaled)[:, 1]
        
        for i, pattern in enumerate(pattern_data):
            if i < len(probabilities):
                pattern['ml_confidence'] = probabilities[i]
        
        return pattern_data

if __name__ == '__main__':
    # Dummy data for demonstration
    # In a real scenario, `pattern_data` would come from `pattern_recognition.py`
    # and `labels` would be manually annotated or derived from expert rules.
    dummy_patterns = [
        {'pattern_name': 'Bullish Impulse Wave 1', 'pattern_type': 'impulsive_bullish', 'hurst_exponent': 0.7, 'confidence': 0.9, 'points': [{'price': 100, 'date': pd.to_datetime('2023-01-01')}, {'price': 110, 'date': pd.to_datetime('2023-01-10')}, {'price': 105, 'date': pd.to_datetime('2023-01-15')}, {'price': 125, 'date': pd.to_datetime('2023-02-01')}, {'price': 120, 'date': pd.to_datetime('2023-02-05')}, {'price': 130, 'date': pd.to_datetime('2023-02-15')}]},
        {'pattern_name': 'Bearish Corrective Wave 1', 'pattern_type': 'corrective_bearish', 'hurst_exponent': 0.4, 'confidence': 0.7, 'points': [{'price': 130, 'date': pd.to_datetime('2023-02-15')}, {'price': 122, 'date': pd.to_datetime('2023-02-25')}, {'price': 126, 'date': pd.to_datetime('2023-03-01')}, {'price': 118, 'date': pd.to_datetime('2023-03-10')}]},
        {'pattern_name': 'Bullish Impulse Wave 2', 'pattern_type': 'impulsive_bullish', 'hurst_exponent': 0.75, 'confidence': 0.8, 'points': [{'price': 200, 'date': pd.to_datetime('2023-05-01')}, {'price': 210, 'date': pd.to_datetime('2023-05-10')}, {'price': 205, 'date': pd.to_datetime('2023-05-15')}, {'price': 225, 'date': pd.to_datetime('2023-06-01')}, {'price': 220, 'date': pd.to_datetime('2023-06-05')}, {'price': 230, 'date': pd.to_datetime('2023-06-15')}]},
        {'pattern_name': 'Bearish Corrective Wave 2', 'pattern_type': 'corrective_bearish', 'hurst_exponent': 0.45, 'confidence': 0.6, 'points': [{'price': 230, 'date': pd.to_datetime('2023-06-15')}, {'price': 222, 'date': pd.to_datetime('2023-06-25')}, {'price': 226, 'date': pd.to_datetime('2023-07-01')}, {'price': 218, 'date': pd.to_datetime('2023-07-10')}]},
        # --- Invalid Patterns ---
        {'pattern_name': 'Invalid Impulse Wave 1', 'pattern_type': 'impulsive_bullish', 'hurst_exponent': 0.3, 'confidence': 0.2, 'points': [{'price': 100, 'date': pd.to_datetime('2023-01-01')}, {'price': 110, 'date': pd.to_datetime('2023-01-10')}, {'price': 105, 'date': pd.to_datetime('2023-01-15')}, {'price': 125, 'date': pd.to_datetime('2023-02-01')}, {'price': 109, 'date': pd.to_datetime('2023-02-05')}, {'price': 130, 'date': pd.to_datetime('2023-02-15')}]},
        {'pattern_name': 'Invalid Impulse Wave 2', 'pattern_type': 'impulsive_bearish', 'hurst_exponent': 0.6, 'confidence': 0.3, 'points': [{'price': 150, 'date': pd.to_datetime('2023-04-01')}, {'price': 140, 'date': pd.to_datetime('2023-04-10')}, {'price': 145, 'date': pd.to_datetime('2023-04-15')}, {'price': 142, 'date': pd.to_datetime('2023-04-20')}, {'price': 144, 'date': pd.to_datetime('2023-04-25')}, {'price': 130, 'date': pd.to_datetime('2023-05-01')}]},
        {'pattern_name': 'Invalid Impulse Wave 3', 'pattern_type': 'impulsive_bullish', 'hurst_exponent': 0.2, 'confidence': 0.1, 'points': [{'price': 10, 'date': pd.to_datetime('2023-08-01')}, {'price': 1, 'date': pd.to_datetime('2023-08-10')}, {'price': 5, 'date': pd.to_datetime('2023-08-15')}, {'price': 2, 'date': pd.to_datetime('2023-08-20')}, {'price': 4, 'date': pd.to_datetime('2023-08-25')}, {'price': 3, 'date': pd.to_datetime('2023-09-01')}]},
        {'pattern_name': 'Invalid Corrective Wave 1', 'pattern_type': 'corrective_bearish', 'hurst_exponent': 0.9, 'confidence': 0.05, 'points': [{'price': 50, 'date': pd.to_datetime('2023-09-15')}, {'price': 60, 'date': pd.to_datetime('2023-09-25')}, {'price': 40, 'date': pd.to_datetime('2023-10-01')}, {'price': 70, 'date': pd.to_datetime('2023-10-10')}]}
    ]

    # Dummy labels: 1 for valid, 0 for invalid
    dummy_labels = pd.Series([1, 1, 1, 1, 0, 0, 0, 0]) # Valid, Valid, Valid, Valid, Invalid, Invalid, Invalid, Invalid

    ml_validator = MLValidator()
    
    # Prepare features for training
    X_train = ml_validator._prepare_features(dummy_patterns)
    y_train = dummy_labels

    # Train the model
    ml_validator.train_model(X_train, y_train)

    # Predict validity for new (or same) patterns
    predicted_patterns = ml_validator.predict_validity(dummy_patterns)

    print("\nPredicted Validity Scores:")
    for p in predicted_patterns:
        print(f"Pattern: {p['pattern_name']}, Confidence: {p['confidence']:.2f}, ML Validity Score: {p.get('ml_confidence', 'N/A')}")

    # Evaluate the model (optional, for testing/development)
    y_pred = [1 if p.get('ml_confidence', 0) > 0.5 else 0 for p in predicted_patterns]
    print(f"\nAccuracy: {accuracy_score(y_train, y_pred):.2f}")
    print("Classification Report:")
    with warnings.catch_warnings():
        warnings.simplefilter("ignore")
        print(classification_report(y_train, y_pred, zero_division=0))