import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler
from dtw import dtw
import logging

class SimilaritySearcher:
    def __init__(self, full_history_df: pd.DataFrame):
        self.df = full_history_df
        self.scaler = MinMaxScaler()
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    def _normalize_segment(self, segment: np.ndarray) -> np.ndarray:
        """Normalizes a price segment to a 0-1 scale."""
        # Reshape for scaler, fit, and transform
        return self.scaler.fit_transform(segment.reshape(-1, 1)).flatten()

    def find_similar(self, target_pattern: dict, num_matches: int = 3) -> list:
        """
        Finds historical segments similar to the target pattern using Dynamic Time Warping (DTW).

        Args:
            target_pattern (dict): The pattern to find matches for.
            num_matches (int): The number of top similar patterns to return.

        Returns:
            list: A list of dictionaries, each representing a similar historical pattern.
        """
        logging.info(f"Starting similarity search for pattern: {target_pattern['pattern_name']}")
        
        target_points = target_pattern['points']
        target_start_date = pd.to_datetime(target_points[0]['date'])
        target_end_date = pd.to_datetime(target_points[-1]['date'])
        
        # Extract and normalize the target pattern's price series
        target_series = self.df.loc[target_start_date:target_end_date]['close'].values
        if len(target_series) < 2:
            logging.warning("Target pattern has too few data points to analyze.")
            return []
        normalized_target = self._normalize_segment(target_series)
        
        window_size = len(target_series)
        all_distances = []

        # Slide a window across the historical data
        for i in range(len(self.df) - window_size + 1):
            window_segment = self.df.iloc[i : i + window_size]
            window_start_date = window_segment.index[0]
            
            # Don't compare the pattern to itself
            if abs((window_start_date - target_start_date).days) < window_size:
                continue

            historical_series = window_segment['close'].values
            normalized_historical = self._normalize_segment(historical_series)
            
            # Calculate DTW distance
            # The alignment object provides the normalized distance
            alignment = dtw(normalized_historical, normalized_target, keep_internals=True)
            distance = alignment.normalizedDistance
            
            all_distances.append({
                'start_date': window_start_date,
                'end_date': window_segment.index[-1],
                'distance': distance
            })

        if not all_distances:
            logging.warning("Could not compute any similarity distances.")
            return []

        # Sort by distance and get the top N matches
        all_distances.sort(key=lambda x: x['distance'])
        best_matches = all_distances[:num_matches]

        logging.info(f"Found {len(best_matches)} similar historical patterns.")
        return best_matches
