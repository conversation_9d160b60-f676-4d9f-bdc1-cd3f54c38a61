

import pandas as pd

def run_backtest(df: pd.DataFrame, patterns: list) -> dict:
    """
    Runs a simple backtest based on identified Elliott Wave patterns.

    Args:
        df (pd.DataFrame): DataFrame containing OHLCV data.
        patterns (list): List of identified patterns from pattern_recognition.py.

    Returns:
        dict: A dictionary containing backtest results (e.g., total_return).
    """
    if df.empty or not patterns:
        return {"total_return": 0.0, "trades": []}

    # Ensure index is datetime
    df.index = pd.to_datetime(df.index)

    # Initialize portfolio
    initial_capital = 10000.0
    capital = initial_capital
    shares_held = 0
    trades = []

    # Group patterns by their execution date
    patterns_by_date = {}
    for pattern in patterns:
        pattern_end_date = pd.to_datetime(pattern['end_date'])
        execution_date = None
        for date in df.index:
            if date >= pattern_end_date:
                execution_date = date
                break
        if execution_date:
            if execution_date not in patterns_by_date:
                patterns_by_date[execution_date] = []
            patterns_by_date[execution_date].append(pattern)

    # Iterate through the DataFrame's index (all trading days)
    for current_date in df.index:
        execution_price = df.loc[current_date]['close']

        buy_signal_present = False
        sell_signal_present = False

        if current_date in patterns_by_date:
            patterns_for_day = patterns_by_date[current_date]
            # Sort patterns for the day to prioritize buy signals if multiple patterns end on the same day
            patterns_for_day.sort(key=lambda x: (0 if x['pattern_type'].startswith('impulsive_bullish') else 1))

            for pattern in patterns_for_day:
                pattern_type = pattern['pattern_type']
                confidence = pattern['confidence']

                if pattern_type.startswith('impulsive_bullish') and confidence > 0.3:
                    buy_signal_present = True
                elif pattern_type.startswith('corrective') or pattern_type.startswith('impulsive_bearish'):
                    sell_signal_present = True

        # Decision Logic for the day
        if buy_signal_present and shares_held == 0: # Only buy if not holding shares
            buy_shares = int(capital / execution_price)
            if buy_shares > 0:
                shares_held += buy_shares
                capital -= buy_shares * execution_price
                trades.append({
                    "type": "BUY",
                    "date": current_date.strftime('%Y-%m-%d'),
                    "price": execution_price,
                    "shares": buy_shares,
                    "pattern": "Multiple (Impulsive)", # Indicate aggregated signal
                    "confidence": 0.0 # Confidence not directly applicable for aggregated signal
                })
                # print(f"  EXECUTED BUY: {buy_shares} shares at {execution_price:.2f} on {current_date.strftime('%Y-%m-%d')}. Capital: {capital:.2f}, Shares: {shares_held}")
        elif sell_signal_present and shares_held > 0: # Sell if holding shares and a corrective or bearish impulse wave is detected
            capital += shares_held * execution_price
            trades.append({
                "type": "SELL",
                "date": current_date.strftime('%Y-%m-%d'),
                "price": execution_price,
                "shares": shares_held,
                "pattern": "Multiple (Corrective/Bearish)", # Indicate aggregated signal
                "confidence": 0.0 # Confidence not directly applicable for aggregated signal
            })
            # print(f"  EXECUTED SELL: {shares_held} shares at {execution_price:.2f} on {current_date.strftime('%Y-%m-%d')}. Capital: {capital:.2f}, Shares: {shares_held}")
            shares_held = 0

    # Final portfolio value
    final_value = capital + shares_held * df['close'].iloc[-1] if not df.empty else capital
    total_return = (final_value - initial_capital) / initial_capital * 100

    return {"total_return": total_return, "trades": trades}

if __name__ == '__main__':
    # Dummy data for testing the backtester
    np.random.seed(42)
    num_points = 200
    dates = pd.to_datetime(pd.date_range(start='2023-01-01', periods=num_points, freq='D'))
    close_prices = 100 + np.cumsum(np.random.normal(0, 1, num_points))
    open_prices = close_prices + np.random.normal(0, 0.5, num_points)
    high_prices = np.maximum(open_prices, close_prices) + np.abs(np.random.normal(0, 0.5, num_points))
    low_prices = np.minimum(open_prices, close_prices) - np.abs(np.random.normal(0, 0.5, num_points))
    volume = np.random.randint(1000, 5000, num_points)

    dummy_df = pd.DataFrame({
        'open': open_prices,
        'high': high_prices,
        'low': low_prices,
        'close': close_prices,
        'volume': volume
    }, index=dates)

    # Example patterns (simplified for testing)
    example_patterns = [
        {
            "pattern_name": "Bullish Impulse Wave",
            "pattern_type": "impulsive",
            "confidence": 0.95,
            "start_date": "2023-01-10",
            "end_date": "2023-02-20",
            "description": "Example impulse wave",
            "hurst_exponent": 0.7,
            "wave_degree": "Primary",
            "timeframe": "daily"
        },
        {
            "pattern_name": "Corrective Wave (A-B-C)",
            "pattern_type": "corrective",
            "confidence": 0.80,
            "start_date": "2023-03-01",
            "end_date": "2023-04-15",
            "description": "Example corrective wave",
            "hurst_exponent": 0.6,
            "wave_degree": "Primary",
            "timeframe": "daily"
        },
        {
            "pattern_name": "Bullish Impulse Wave",
            "pattern_type": "impulsive",
            "confidence": 0.85,
            "start_date": "2023-05-01",
            "end_date": "2023-06-10",
            "description": "Another example impulse wave",
            "hurst_exponent": 0.72,
            "wave_degree": "Primary",
            "timeframe": "daily"
        },
        {
            "pattern_name": "Corrective Wave (Flat)",
            "pattern_type": "corrective",
            "confidence": 0.75,
            "start_date": "2023-06-15",
            "end_date": "2023-07-20",
            "description": "Example flat corrective wave",
            "hurst_exponent": 0.65,
            "wave_degree": "Primary",
            "timeframe": "daily"
        }
    ]

    backtest_results = run_backtest(dummy_df, example_patterns)
    print("\nBacktest Results:")
    print(f"Total Return: {backtest_results['total_return']:.2f}%")
    print("Trades:")
    for trade in backtest_results['trades']:
        print(f"  {trade['type']} {trade['shares']} shares at {trade['price']:.2f} on {trade['date']} (Pattern: {trade['pattern']}, Conf: {trade['confidence']:.2f})")
