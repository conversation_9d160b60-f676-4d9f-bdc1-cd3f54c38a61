import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd

def plot_elliott_wave_patterns(df: pd.DataFrame, patterns: list, backtest_results: dict, stock_symbol: str, forecast_data: dict = None, similar_patterns: list = None):
    # print(f"[plot_elliott_wave_patterns] Received {len(patterns)} for {stock_symbol}.")
    if patterns:
        # print(f"[plot_elliott_wave_patterns] First 3 patterns: {patterns[:3]}")

        if df.empty or not patterns:
            return go.Figure(), None

    fig = make_subplots(rows=2, cols=1, shared_xaxes=True, 
                        vertical_spacing=0.05, row_heights=[0.7, 0.3],
                        subplot_titles=("Elliott Wave Analysis", "Volume"))

    # --- Candlestick Chart ---
    fig.add_trace(go.Candlestick(x=df.index, open=df['open'], high=df['high'],
                                 low=df['low'], close=df['close'], name='Candlestick',
                                 increasing_line_color='red', decreasing_line_color='green'),
                  row=1, col=1)

    # --- Highlight Similar Historical Patterns ---
    if similar_patterns:
        for i, p in enumerate(similar_patterns):
            fig.add_vrect(
                x0=p['start_date'], x1=p['end_date'],
                fillcolor="rgba(70,130,180,0.2)", # SteelBlue with opacity
                layer="below", line_width=0,
                annotation_text=f"Similar #{i+1}", annotation_position="top left",
                row=1, col=1
            )

    # --- Draw all detected patterns ---
    for i, pattern in enumerate(patterns):
        pattern_dates = [point['date'].isoformat() for point in pattern['points']]
        pattern_prices = [float(point['price']) for point in pattern['points']]
        
        # Assign a color based on pattern type for better visualization
        if "impulsive_diagonal" in pattern['pattern_type']:
            color = 'purple'
        elif "impulsive" in pattern['pattern_type']:
            color = 'blue'
        elif "corrective" in pattern['pattern_type']:
            color = 'orange'
        else:
            color = 'gray'

        fig.add_trace(go.Scatter(x=pattern_dates, y=pattern_prices, mode='lines+markers',
                                 line=dict(width=2),
                                 marker=dict(color=color, size=8),
                                 name=f"{pattern['pattern_name']} ({pattern['confidence']:.2f})"),
                      row=1, col=1)

    # --- Draw Forecast ---
    if forecast_data and forecast_data.get('targets'):
        start_date = pd.to_datetime(forecast_data['start_date'])
        for target in forecast_data['targets']:
            target_date = pd.to_datetime(target['date'])
            target_price = target['price']
            
            # Draw a line from the start of the forecast to the target
            fig.add_trace(go.Scatter(x=[start_date, target_date], y=[df.loc[start_date]['close'], target_price],
                                     mode='lines+markers', line=dict(color='red', dash='dash', width=1),
                                     marker=dict(color='red', size=7, symbol='star'),
                                     name=f"Forecast: {target['type']}"),
                          row=1, col=1)
            
            # Add annotation
            fig.add_annotation(x=target_date, y=target_price,
                               text=f"{target['type']}<br>{target_price}",
                               showarrow=True, arrowhead=1, arrowcolor="red",
                               row=1, col=1)

    # --- Volume Chart ---
    fig.add_trace(go.Bar(x=df.index, y=df['volume'], name='Volume', marker_color='rgba(0,0,100,0.3)'),
                  row=2, col=1)

    # --- Layout and Axis Configuration ---
    fig.update_layout(title_text=f"Elliott Wave Analysis for {stock_symbol}",
                      xaxis_rangeslider_visible=False,
                      height=800)
    fig.update_xaxes(rangebreaks=[dict(bounds=["sat", "mon"])]) 

    # --- Backtest Net Worth Chart ---
    net_worth_fig = None
    if backtest_results:
        net_worth_history = backtest_results.get('backtest_results', {}).get("net_worth_history", [])
        if net_worth_history:
            net_worth_fig = go.Figure()
            # Ensure the x-axis for net worth also uses the daily_df index for correct alignment
            net_worth_dates = df.index[:len(net_worth_history)]
            net_worth_fig.add_trace(go.Scatter(x=net_worth_dates, y=net_worth_history, mode='lines', name='Net Worth', line=dict(color='green')))
            net_worth_fig.update_layout(title="DRL Agent's Net Worth Progression", xaxis_title="Date", yaxis_title="Net Worth", height=300)
            net_worth_fig.update_xaxes(rangebreaks=[dict(bounds=["sat", "mon"])])


    return fig, net_worth_fig

def plot_single_pattern(fig, pattern_data: dict, color: str, name: str):
    # This function is no longer needed as plotting is handled in plot_elliott_wave_patterns
    pass

