import numpy as np
import pywt
import nolds
from scipy.signal import find_peaks

def find_nested_wave_structures(
    ts: np.ndarray,
    scales: np.ndarray = np.arange(1, 64),
    wavelet_name: str = 'morl',
    hurst_threshold: float = 0.6,
    min_segment_len: int = 20
) -> tuple[list, np.ndarray, np.ndarray]:
    """
    Analyzes a time series to find segments with trend-like behavior using CWT and Hurst Exponent.

    Args:
        ts: 1D numpy array of the time series (e.g., closing prices).
        scales: Array of scales to use for the Continuous Wavelet Transform (CWT).
        wavelet_name: Name of the wavelet to use (e.g., 'morl', 'gaus1').
        hurst_threshold: Hurst Exponent threshold to filter for trending segments.
        min_segment_len: The minimum length for a segment to be considered a candidate.

    Returns:
        A tuple containing:
        - list: A list of tuples, where each tuple is (start_index, end_index, hurst_exponent).
        - np.ndarray: The CWT coefficients.
        - np.ndarray: The frequencies corresponding to the CWT scales.
    """
    # 1. Perform Continuous Wavelet Transform (CWT)
    coeffs, freqs = pywt.cwt(ts, scales, wavelet_name)
    energy = np.abs(coeffs) ** 2

    # 2. Identify high-energy regions
    mean_energy = np.mean(energy, axis=1, keepdims=True)
    std_energy = np.std(energy, axis=1, keepdims=True)
    energy_threshold = mean_energy + 1.5 * std_energy  # Threshold for significant energy

    significant_energy_mask = energy > energy_threshold
    # Collapse the 2D mask into a 1D mask by checking if any scale has significant energy
    is_high_energy = np.any(significant_energy_mask, axis=0)

    # 3. Isolate and filter candidate segments
    candidate_indices, _ = find_peaks(is_high_energy.astype(int), height=0.5, distance=min_segment_len)
    
    nested_wave_candidates = []
    
    # This is a simplified way to get segments. A more robust way would be to find contiguous blocks.
    # Let's find contiguous blocks of high energy
    is_high_energy_int = is_high_energy.astype(int)
    diff = np.diff(is_high_energy_int, prepend=0, append=0)
    starts = np.where(diff == 1)[0]
    ends = np.where(diff == -1)[0]

    if len(starts) == 0 or len(ends) == 0:
        return [], coeffs, freqs

    for start, end in zip(starts, ends):
        if end - start >= min_segment_len:
            segment = ts[start:end]
            try:
                # 4. Calculate Hurst Exponent for each segment
                hurst_exponent = nolds.hurst_rs(segment)
                # 5. Filter by Hurst Exponent
                if hurst_exponent > hurst_threshold:
                    nested_wave_candidates.append((start, end, hurst_exponent))
            except Exception as e:
                # nolds can sometimes fail on certain data shapes
                # print(f"Could not calculate Hurst for segment {start}-{end}: {e}")
                continue

    return nested_wave_candidates, coeffs, freqs

if __name__ == '__main__':
    # --- Test with synthetic data ---
    # Create a time series with a clear trending part and a mean-reverting part
    trending_part = np.linspace(0, 20, 100) + np.random.randn(100) * 0.5
    mean_reverting_part = np.sin(np.linspace(0, 40, 100)) * 5 + np.random.randn(100) * 0.5
    test_ts = np.concatenate([mean_reverting_part, trending_part, mean_reverting_part])

    print(f"Analyzing a test time series of length {len(test_ts)}...")
    candidates, _, _ = find_nested_wave_structures(test_ts, hurst_threshold=0.7)

    print(f"\nFound {len(candidates)} candidate wave structures:")
    for start, end, hurst in candidates:
        print(f"  - Segment from index {start} to {end} with Hurst Exponent: {hurst:.4f}")

    # Expected: Should primarily identify the middle segment (approx. 100-200)
    assert len(candidates) > 0, "Should have found at least one candidate"
    assert any(90 < c[0] < 110 and 190 < c[1] < 210 for c in candidates), "Did not find the expected trending segment"
    print("\nTest passed: Successfully identified the synthetic trending segment.")