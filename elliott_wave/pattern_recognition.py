"""
艾略特波浪模式识别模块
提供完整的波浪识别和分析功能
"""

import pandas as pd
import numpy as np
from scipy.signal import find_peaks, argrelextrema
import pywt
from typing import Dict, List, Any, Optional, Tuple
import logging
from dataclasses import dataclass
from datetime import datetime, timedelta

from elliott_wave.wave_structure_analyzer import find_nested_wave_structures
from elliott_wave.multi_timeframe_analysis import validate_and_score_by_subwaves
from elliott_wave.core_patterns import find_elliott_wave_patterns, create_pattern_dict, _split_df_by_gaps

logger = logging.getLogger(__name__)


@dataclass
class WavePoint:
    """波浪点数据结构"""
    price: float
    date: pd.Timestamp
    wave_type: str  # 'peak' or 'trough'
    confidence: float


@dataclass
class ElliottWave:
    """艾略特波浪结构"""
    wave_type: str  # 'impulse' or 'corrective'
    direction: str  # 'bullish' or 'bearish'
    waves: List[WavePoint]
    confidence: float
    start_date: pd.Timestamp
    end_date: pd.Timestamp
    fibonacci_ratios: Dict[str, float]


class TechnicalIndicators:
    """技术指标计算器"""
    
    @staticmethod
    def calculate_rsi(prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    @staticmethod
    def calculate_macd(prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """计算MACD指标"""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        signal_line = macd.ewm(span=signal).mean()
        histogram = macd - signal_line
        return macd, signal_line, histogram
    
    @staticmethod
    def calculate_kdj(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """计算KDJ指标"""
        lowest_low = low.rolling(window=period).min()
        highest_high = high.rolling(window=period).max()
        
        rsv = (close - lowest_low) / (highest_high - lowest_low) * 100
        k = rsv.ewm(alpha=1/3).mean()
        d = k.ewm(alpha=1/3).mean()
        j = 3 * k - 2 * d
        
        return k, d, j
    
    def calculate_all(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算所有技术指标"""
        df = data.copy()
        
        # RSI
        df['RSI'] = self.calculate_rsi(df['close'])
        
        # MACD
        macd, signal, histogram = self.calculate_macd(df['close'])
        df['MACD'] = macd
        df['MACD_signal'] = signal
        df['MACD_histogram'] = histogram
        
        # KDJ
        k, d, j = self.calculate_kdj(df['high'], df['low'], df['close'])
        df['KDJ_K'] = k
        df['KDJ_D'] = d
        df['KDJ_J'] = j
        
        return df


class ElliottWaveAnalyzer:
    """艾略特波浪分析器"""
    
    def __init__(self):
        self.indicators = TechnicalIndicators()
        self.min_wave_length = 5  # 最小波浪长度
        self.confidence_threshold = 0.6
        
    def analyze(self, data: pd.DataFrame) -> Dict[str, Any]:
        """执行完整的艾略特波浪分析"""
        try:
            # 数据预处理
            processed_data = self._preprocess_data(data)
            
            # 识别波浪结构
            wave_structures = self._identify_wave_structures(processed_data)
            
            # 计算技术指标
            technical_data = self.indicators.calculate_all(processed_data)
            
            # 验证波浪模式
            validated_waves = self._validate_wave_patterns(wave_structures, technical_data)
            
            # 生成分析报告
            analysis_result = self._generate_analysis_report(validated_waves, technical_data)
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"波浪分析失败: {str(e)}")
            return {"error": str(e)}
    
    def _preprocess_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """数据预处理"""
        df = data.copy()
        
        # 确保数据按时间排序
        df = df.sort_index()
        
        # 处理缺失值
        df = df.fillna(method='ffill')
        
        # 计算价格变化
        df['price_change'] = df['close'].pct_change()
        df['price_change_abs'] = df['close'].diff()
        
        return df
    
    def _identify_wave_structures(self, data: pd.DataFrame) -> List[ElliottWave]:
        """识别波浪结构"""
        waves = []
        
        # 寻找极值点
        peaks, _ = find_peaks(data['high'], distance=self.min_wave_length)
        troughs, _ = find_peaks(-data['low'], distance=self.min_wave_length)
        
        # 合并并排序所有极值点
        all_extrema = []
        for peak in peaks:
            all_extrema.append(WavePoint(
                price=data['high'].iloc[peak],
                date=data.index[peak],
                wave_type='peak',
                confidence=0.8
            ))
        
        for trough in troughs:
            all_extrema.append(WavePoint(
                price=data['low'].iloc[trough],
                date=data.index[trough],
                wave_type='trough',
                confidence=0.8
            ))
        
        # 按时间排序
        all_extrema.sort(key=lambda x: x.date)
        
        # 识别波浪模式
        waves.extend(self._identify_impulse_waves(all_extrema, data))
        waves.extend(self._identify_corrective_waves(all_extrema, data))
        
        return waves
    
    def _identify_impulse_waves(self, extrema: List[WavePoint], data: pd.DataFrame) -> List[ElliottWave]:
        """识别推动浪"""
        impulse_waves = []
        
        if len(extrema) < 5:
            return impulse_waves
        
        # 寻找5浪结构
        for i in range(len(extrema) - 4):
            wave_points = extrema[i:i+5]
            
            # 检查是否为有效的推动浪结构
            if self._is_valid_impulse_wave(wave_points, data):
                wave = ElliottWave(
                    wave_type='impulse',
                    direction='bullish' if wave_points[-1].price > wave_points[0].price else 'bearish',
                    waves=wave_points,
                    confidence=self._calculate_wave_confidence(wave_points),
                    start_date=wave_points[0].date,
                    end_date=wave_points[-1].date,
                    fibonacci_ratios=self._calculate_fibonacci_ratios(wave_points)
                )
                impulse_waves.append(wave)
        
        return impulse_waves
    
    def _identify_corrective_waves(self, extrema: List[WavePoint], data: pd.DataFrame) -> List[ElliottWave]:
        """识别调整浪"""
        corrective_waves = []
        
        if len(extrema) < 3:
            return corrective_waves
        
        # 寻找3浪调整结构
        for i in range(len(extrema) - 2):
            wave_points = extrema[i:i+3]
            
            # 检查是否为有效的调整浪结构
            if self._is_valid_corrective_wave(wave_points, data):
                wave = ElliottWave(
                    wave_type='corrective',
                    direction='bullish' if wave_points[-1].price > wave_points[0].price else 'bearish',
                    waves=wave_points,
                    confidence=self._calculate_wave_confidence(wave_points),
                    start_date=wave_points[0].date,
                    end_date=wave_points[-1].date,
                    fibonacci_ratios=self._calculate_fibonacci_ratios(wave_points)
                )
                corrective_waves.append(wave)
        
        return corrective_waves
    
    def _is_valid_impulse_wave(self, waves: List[WavePoint], data: pd.DataFrame) -> bool:
        """验证推动浪结构"""
        if len(waves) != 5:
            return False
        
        # 检查波浪方向一致性
        prices = [w.price for w in waves]
        if not (all(prices[i] < prices[i+1] for i in range(4)) or 
                all(prices[i] > prices[i+1] for i in range(4))):
            return False
        
        # 检查波浪比例关系
        wave1 = abs(prices[1] - prices[0])
        wave3 = abs(prices[3] - prices[2])
        wave5 = abs(prices[4] - prices[3])
        
        # 第3浪不能是最短的
        if wave3 < min(wave1, wave5):
            return False
        
        return True
    
    def _is_valid_corrective_wave(self, waves: List[WavePoint], data: pd.DataFrame) -> bool:
        """验证调整浪结构"""
        if len(waves) < 3:
            return False
        
        # 检查是否为ABC调整结构
        prices = [w.price for w in waves]
        
        # 简单的ABC结构检查
        if len(prices) == 3:
            # A浪和C浪方向相同，B浪为反弹
            return True
        
        return False
    
    def _calculate_wave_confidence(self, waves: List[WavePoint]) -> float:
        """计算波浪置信度"""
        if len(waves) < 3:
            return 0.0
        
        # 基于波浪比例和时间对称性计算置信度
        prices = [w.price for w in waves]
        
        # 计算价格比例
        total_move = abs(prices[-1] - prices[0])
        wave_moves = [abs(prices[i+1] - prices[i]) for i in range(len(prices)-1)]
        
        # 检查是否符合斐波那契比例
        fib_ratios = [0.382, 0.5, 0.618, 0.786, 1.0, 1.272, 1.618]
        confidence = 0.0
        
        for move in wave_moves:
            ratio = move / total_move if total_move > 0 else 0
            closest_fib = min(fib_ratios, key=lambda x: abs(x - ratio))
            confidence += 1 - abs(closest_fib - ratio)
        
        return min(confidence / len(wave_moves), 1.0)
    
    def _calculate_fibonacci_ratios(self, waves: List[WavePoint]) -> Dict[str, float]:
        """计算斐波那契比例"""
        if len(waves) < 2:
            return {}
        
        prices = [w.price for w in waves]
        total_range = abs(prices[-1] - prices[0])
        
        ratios = {}
        for i in range(1, len(prices)):
            partial_range = abs(prices[i] - prices[0])
            ratio = partial_range / total_range if total_range > 0 else 0
            ratios[f"wave_{i}_ratio"] = ratio
        
        return ratios
    
    def _validate_wave_patterns(self, waves: List[ElliottWave], technical_data: pd.DataFrame) -> List[ElliottWave]:
        """验证波浪模式"""
        validated_waves = []
        
        for wave in waves:
            if wave.confidence >= self.confidence_threshold:
                # 检查技术指标确认
                if self._confirm_with_technical_indicators(wave, technical_data):
                    validated_waves.append(wave)
        
        return validated_waves
    
    def _confirm_with_technical_indicators(self, wave: ElliottWave, technical_data: pd.DataFrame) -> bool:
        """使用技术指标确认波浪"""
        # 获取波浪对应时间段的技术指标
        wave_data = technical_data[
            (technical_data.index >= wave.start_date) & 
            (technical_data.index <= wave.end_date)
        ]
        
        if wave_data.empty:
            return False
        
        # 检查RSI是否支持波浪方向
        avg_rsi = wave_data['RSI'].mean()
        
        if wave.direction == 'bullish' and avg_rsi < 70:
            return True
        elif wave.direction == 'bearish' and avg_rsi > 30:
            return True
        
        return True
    
    def _generate_analysis_report(self, waves: List[ElliottWave], technical_data: pd.DataFrame) -> Dict[str, Any]:
        """生成分析报告"""
        if not waves:
            return {
                "status": "no_waves_found",
                "message": "未找到有效的艾略特波浪结构",
                "recommendation": "建议等待更明确的趋势信号"
            }
        
        # 找到最新的波浪
        latest_wave = max(waves, key=lambda w: w.end_date)
        
        # 计算关键价位
        current_price = technical_data['close'].iloc[-1]
        supports, resistances = self._calculate_key_levels(latest_wave, current_price)
        
        # 生成详细分析
        analysis = {
            "status": "success",
            "wave_structure": {
                "type": latest_wave.wave_type,
                "direction": latest_wave.direction,
                "confidence": latest_wave.confidence,
                "start_date": latest_wave.start_date.strftime('%Y-%m-%d'),
                "end_date": latest_wave.end_date.strftime('%Y-%m-%d'),
                "wave_count": len(latest_wave.waves)
            },
            "key_levels": {
                "supports": supports,
                "resistances": resistances,
                "current_price": current_price
            },
            "fibonacci_ratios": latest_wave.fibonacci_ratios,
            "technical_indicators": {
                "rsi": technical_data['RSI'].iloc[-1],
                "macd": technical_data['MACD'].iloc[-1],
                "volume": technical_data['volume'].iloc[-1]
            },
            "detailed_analysis": self._generate_detailed_text(latest_wave, current_price, technical_data)
        }
        
        return analysis
    
    def _calculate_key_levels(self, wave: ElliottWave, current_price: float) -> Tuple[List[float], List[float]]:
        """计算关键支撑位和阻力位"""
        wave_range = abs(wave.waves[-1].price - wave.waves[0].price)
        
        # 基于斐波那契回撤计算关键位
        fib_levels = [0.236, 0.382, 0.5, 0.618, 0.786]
        
        supports = []
        resistances = []
        
        wave_start = wave.waves[0].price
        wave_end = wave.waves[-1].price
        
        for level in fib_levels:
            if wave.direction == 'bullish':
                # 上升趋势中的回撤位作为支撑
                support = wave_end - (wave_end - wave_start) * level
                supports.append(round(support, 2))
            else:
                # 下降趋势中的反弹位作为阻力
                resistance = wave_end + (wave_start - wave_end) * level
                resistances.append(round(resistance, 2))
        
        # 添加当前价格附近的支撑/阻力
        if wave.direction == 'bullish':
            resistances.extend([round(current_price * 1.02, 2), round(current_price * 1.05, 2)])
        else:
            supports.extend([round(current_price * 0.98, 2), round(current_price * 0.95, 2)])
        
        return supports, resistances
    
    def _generate_detailed_text(self, wave: ElliottWave, current_price: float, technical_data: pd.DataFrame) -> str:
        """生成详细分析文本"""
        direction_text = "上升" if wave.direction == "bullish" else "下降"
        wave_type_text = "推动浪" if wave.wave_type == "impulse" else "调整浪"
        
        latest_rsi = technical_data['RSI'].iloc[-1]
        latest_macd = technical_data['MACD'].iloc[-1]
        
        analysis_text = f"""
## 波浪分析详情

### 当前识别到的波浪
- **波浪类型**: {wave_type_text}
- **趋势方向**: {direction_text}
- **置信度**: {wave.confidence*100:.1f}%
- **波浪周期**: {wave.start_date.strftime('%Y-%m-%d')} 至 {wave.end_date.strftime('%Y-%m-%d')}

### 技术指标确认
- **RSI**: {latest_rsi:.2f} - {"超买区域，需谨慎" if latest_rsi > 70 else "超卖区域，可能反弹" if latest_rsi < 30 else "正常区域"}
- **MACD**: {latest_macd:.3f} - {"多头信号" if latest_macd > 0 else "空头信号"}

### 斐波那契分析
{chr(10).join([f"- {k}: {v:.3f}" for k, v in wave.fibonacci_ratios.items()])}

### 操作建议
基于当前波浪分析和技术指标确认，建议：
1. 关注关键支撑/阻力位的突破情况
2. 结合成交量变化确认趋势延续性
3. 设置合理的止损位以控制风险
"""
        
        return analysis_text


# 测试代码
if __name__ == '__main__':
    import yfinance as yf
    
    # 获取测试数据
    ticker = "AAPL"
    data = yf.download(ticker, period="6mo", interval="1d")
    
    # 创建分析器
    analyzer = ElliottWaveAnalyzer()
    
    # 执行分析
    result = analyzer.analyze(data)
    
    print(f"分析结果: {result}")
    
    if "error" not in result:
        print(f"\n最新波浪结构:")
        print(f"类型: {result['wave_structure']['type']}")
        print(f"方向: {result['wave_structure']['direction']}")
        print(f"置信度: {result['wave_structure']['confidence']}")
        print(f"\n关键价位:")
        print(f"支撑位: {result['key_levels']['supports']}")
        print(f"阻力位: {result['key_levels']['resistances']}")
