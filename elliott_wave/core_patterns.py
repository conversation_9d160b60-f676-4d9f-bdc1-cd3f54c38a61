import pandas as pd
import numpy as np
from scipy.signal import find_peaks, argrelextrema
import pywt
from elliott_wave.wave_structure_analyzer import find_nested_wave_structures
import logging
logger = logging.getLogger(__name__)

# --- Constants for Fibonacci Ratios ---
WAVE2_RETRACEMENT_RATIOS = [0.5, 0.618, 0.786]
WAVE3_EXTENSION_RATIOS = [1.618, 2.618, 4.236]
WAVE4_RETRACEMENT_RATIOS = [0.236, 0.382, 0.5]
WAVE5_EXTENSION_RATIOS = [0.618, 1.0, 1.618]
CORRECTION_WAVE_B_RATIOS = [0.382, 0.5, 0.618]
CORRECTION_WAVE_C_RATIOS = [1.0, 1.618]
FLAT_WAVE_B_RETRACEMENT = [0.8, 1.0, 1.05] # B retraces A by 80-105%
FLAT_WAVE_C_EXTENSION = [1.0, 1.618] # C is usually 100-161.8% of A
TRIANGLE_WAVE_C_RETRACEMENT = [0.618, 0.786] # C retraces B
TRIANGLE_WAVE_D_RETRACEMENT = [0.618, 0.786] # D retraces C
TRIANGLE_WAVE_E_RETRACEMENT = [0.618, 0.786] # E retraces D
X_WAVE_RETRACEMENT_RATIOS = [0.382, 0.5, 0.618] # X wave retraces previous wave
Z_WAVE_EXTENSION_RATIOS = [0.618, 1.0, 1.618] # Z wave is often related to W and Y
FIB_TOLERANCE = 0.15  # Increased tolerance for real-world data

# --- Helper: ATR for dynamic prominence ---
def _calculate_atr(df: pd.DataFrame, period: int = 14) -> pd.Series:
    high_low = df['high'] - df['low']
    high_close = np.abs(df['high'] - df['close'].shift())
    low_close = np.abs(df['low'] - df['close'].shift())
    true_range = pd.DataFrame({'high_low': high_low, 'high_close': high_close, 'low_close': low_close}).max(axis=1)
    atr = true_range.ewm(span=period, adjust=False).mean()
    return atr

# --- Advanced Pivot Detection using CWT ---
def _find_pivots_cwt(prices: np.ndarray, dates: pd.Index, wavelet: str = 'mexh', scales: np.ndarray = None) -> list:
    logging.info(f"[_find_pivots_cwt] Starting CWT-based pivot detection with wavelet '{wavelet}'.")
    if len(prices) < 10:
        logging.warning("Segment too small for CWT analysis.")
        return []

    if scales is None:
        # Dynamically adjust scales based on the length of the price series
        # A common heuristic is to use scales up to half the length of the series
        max_scale = min(len(prices) // 2, 64) # Cap at 64 to prevent excessively large scales
        scales = np.arange(1, max_scale + 1)
        if len(scales) < 2: # Ensure at least two scales for meaningful analysis
            scales = np.arange(1, 3) # Fallback to small fixed scales

    # Perform Continuous Wavelet Transform
    cwt_matrix, _ = pywt.cwt(prices, scales, wavelet)

    # Find ridges in the CWT matrix to identify significant extrema
    # A simple approach is to find local extrema at different scales
    peaks = argrelextrema(cwt_matrix, np.greater, axis=1)
    troughs = argrelextrema(cwt_matrix, np.less, axis=1)

    # Aggregate extrema across scales to find the most significant ones
    # Here, we count how many scales support each pivot point
    pivot_scores = np.zeros(len(prices))
    for scale_idx, price_idx in zip(peaks[0], peaks[1]):
        pivot_scores[price_idx] += 1
    for scale_idx, price_idx in zip(troughs[0], troughs[1]):
        pivot_scores[price_idx] += 1

    # Find points that are significant across multiple scales
    # The threshold can be tuned, e.g., supported by at least 1/4 of the scales
    significant_pivot_indices = np.where(pivot_scores > (len(scales) / 32))[0] # Relaxed threshold

    if len(significant_pivot_indices) < 2:
        logging.warning("CWT did not yield enough significant pivots.")
        return []

    # Classify as peak or trough based on local price action
    all_extrema = []
    for idx in significant_pivot_indices:
        # Ensure index is not at the very edge
        if 1 < idx < len(prices) - 1:
            if prices[idx] > prices[idx-1] and prices[idx] > prices[idx+1]:
                all_extrema.append({'date': dates[idx], 'price': prices[idx], 'type': 'peak'})
            elif prices[idx] < prices[idx-1] and prices[idx] < prices[idx+1]:
                all_extrema.append({'date': dates[idx], 'price': prices[idx], 'type': 'trough'})

    all_extrema.sort(key=lambda x: x['date'])
    logging.info(f"[_find_pivots_cwt] Found {len(all_extrema)} raw pivots via CWT.")
    return all_extrema

# --- Fallback: Original ATR-based extrema finding ---
def _find_local_extrema_fallback_atr(segment_df: pd.DataFrame) -> list:
    logging.info("[_find_local_extrema_fallback_atr] Using ATR-based fallback.")
    prices = segment_df['close'].values
    dates = segment_df.index
    price_range = prices.max() - prices.min()
    if price_range == 0: 
        logging.warning("Price range is zero. Cannot find extrema.")
        return []

    atr_series = _calculate_atr(segment_df)
    if not atr_series.empty and not np.isnan(atr_series.mean()):
        min_prominence = atr_series.mean() * 0.3
    else:
        min_prominence = price_range * 0.05
    
    min_distance = 5
    peaks_indices, _ = find_peaks(prices, prominence=min_prominence, distance=min_distance)
    troughs_indices, _ = find_peaks(-prices, prominence=min_prominence, distance=min_distance)

    if len(peaks_indices) == 0 and len(troughs_indices) == 0:
        logging.warning("No extrema found with ATR. Falling back to smaller percentage.")
        min_prominence = price_range * 0.02
        peaks_indices, _ = find_peaks(prices, prominence=min_prominence, distance=min_distance)
        troughs_indices, _ = find_peaks(-prices, prominence=min_prominence, distance=min_distance)

    all_extrema = []
    for idx in peaks_indices:
        all_extrema.append({'date': dates[idx], 'price': prices[idx], 'type': 'peak'})
    for idx in troughs_indices:
        all_extrema.append({'date': dates[idx], 'price': prices[idx], 'type': 'trough'})

    all_extrema.sort(key=lambda x: x['date'])
    return all_extrema

# --- Main Helper: Find significant price turning points ---
def _find_local_extrema(segment_df: pd.DataFrame) -> list:
    logging.info(f"[_find_local_extrema] Received segment of size {len(segment_df)}")
    prices = segment_df['close'].values
    dates = segment_df.index
    if len(prices) < 10: 
        logging.warning("Segment too small to find extrema, using fallback.")
        return _find_local_extrema_fallback_atr(segment_df)

    # --- Primary Method: CWT ---
    all_extrema = _find_pivots_cwt(prices, dates)

    # --- Fallback Method --- 
    if len(all_extrema) < 3:
        logging.warning("CWT method found < 3 pivots. Switching to ATR-based fallback.")
        all_extrema = _find_local_extrema_fallback_atr(segment_df)

    if not all_extrema: 
        logging.error("No extrema found even after fallback.")
        return []

    # --- Post-processing: Filter for alternating peaks and troughs ---
    filtered_extrema = [all_extrema[0]]
    for i in range(1, len(all_extrema)):
        if all_extrema[i]['type'] != filtered_extrema[-1]['type']:
            filtered_extrema.append(all_extrema[i])
        else:
            # If same type, keep the more extreme one
            if (all_extrema[i]['type'] == 'peak' and all_extrema[i]['price'] > filtered_extrema[-1]['price']) or \
               (all_extrema[i]['type'] == 'trough' and all_extrema[i]['price'] < filtered_extrema[-1]['price']):
                filtered_extrema[-1] = all_extrema[i]
    
    logging.info(f"[_find_local_extrema] Returning {len(filtered_extrema)} alternating extrema.")
    return filtered_extrema

# --- Fibonacci Validation Logic ---
def _validate_impulse_wave(points: list, is_bullish: bool, segment_df: pd.DataFrame) -> float:
    p = [pt['price'] for pt in points]
    p0, p1, p2, p3, p4, p5 = p

    # Basic rules check
    if is_bullish:
        if not (p1 > p0 and p2 < p1 and p3 > p1 and p4 < p3 and p5 > p3): return 0
        if p2 <= p0: return 0 # Wave 2 cannot retrace 100% of Wave 1
        if p4 <= p1: return 0 # Wave 4 cannot overlap Wave 1
        wave1_len = abs(p1 - p0)
        wave3_len = abs(p3 - p2)
        wave5_len = abs(p5 - p4)
        if wave3_len < wave1_len or wave3_len < wave5_len: return 0 # Wave 3 cannot be the shortest impulse wave
    else: # Bearish
        if not (p1 < p0 and p2 > p1 and p3 < p1 and p4 > p3 and p5 < p3): return 0
        if p2 >= p0: return 0 # Wave 2 cannot retrace 100% of Wave 1
        if p4 >= p1: return 0 # Wave 4 cannot overlap Wave 1
        wave1_len = abs(p1 - p0)
        wave3_len = abs(p3 - p2)
        wave5_len = abs(p5 - p4)
        if wave3_len < wave1_len or wave3_len < wave5_len: return 0 # Wave 3 cannot be the shortest impulse wave

    wave1_len = abs(p1 - p0)
    wave2_retracement = abs(p2 - p1) / wave1_len
    wave3_extension = abs(p3 - p2) / wave1_len
    wave4_retracement = abs(p4 - p3) / abs(p3 - p2)

    score = 0
    if any(abs(wave2_retracement - r) < FIB_TOLERANCE for r in WAVE2_RETRACEMENT_RATIOS): score += 1
    if any(abs(wave3_extension - r) < FIB_TOLERANCE for r in WAVE3_EXTENSION_RATIOS): score += 1
    if any(abs(wave4_retracement - r) < FIB_TOLERANCE for r in WAVE4_RETRACEMENT_RATIOS): score += 1

    # Volume analysis for Impulse Waves
    volume_score = 0
    try:
        vol_wave1 = segment_df.loc[points[0]['date']:points[1]['date']]['volume'].mean()
        vol_wave3 = segment_df.loc[points[2]['date']:points[3]['date']]['volume'].mean()
        vol_wave5 = segment_df.loc[points[4]['date']:points[5]['date']]['volume'].mean()

        if vol_wave3 > vol_wave1 and vol_wave3 > vol_wave5: # Wave 3 volume should be highest
            volume_score += 1
        # Volume should generally confirm the trend (impulse waves have higher volume)
        vol_wave2 = segment_df.loc[points[1]['date']:points[2]['date']]['volume'].mean()
        vol_wave4 = segment_df.loc[points[3]['date']:points[4]['date']]['volume'].mean()
        if vol_wave1 > vol_wave2 and vol_wave3 > vol_wave4 and vol_wave5 > vol_wave4: # Simplified check
            volume_score += 1
    except Exception:
        volume_score = 0 # If error, no score from volume

    # Combine Fibonacci score and Volume score
    total_score = (score / 3.0) * 0.8 + (volume_score / 2.0) * 0.2 # Max volume_score is 2
    return total_score

def _validate_corrective_wave(points: list, is_bullish_correction: bool, segment_df: pd.DataFrame) -> float:
    # The list `points` now contains [context_point, A, B, C]
    if len(points) != 4: return 0
    p_context, pA, pB, pC = [pt['price'] for pt in points]
    
    # Basic rules for a simple ZigZag correction
    if is_bullish_correction: # Correcting a prior downtrend (context is trough, A is up, B is down, C is up)
        if not (pA > p_context and pB < pA and pC > pA): return 0
        if pB <= p_context: return 0 # B cannot be lower than the start of A
    else: # Correcting a prior uptrend (context is peak, A is down, B is up, C is down)
        if not (pA < p_context and pB > pA and pC < pA): return 0
        if pB >= p_context: return 0 # B cannot be higher than the start of A

    waveA_len = abs(pA - p_context)
    if waveA_len == 0: return 0
    
    waveB_retracement = abs(pB - pA) / waveA_len
    waveC_extension = abs(pC - pA) / waveA_len # C is often compared to A's length

    score = 0
    if any(abs(waveB_retracement - r) < FIB_TOLERANCE for r in CORRECTION_WAVE_B_RATIOS): score += 1
    if any(abs(waveC_extension - r) < FIB_TOLERANCE for r in CORRECTION_WAVE_C_RATIOS): score += 1

    # Volume analysis for Corrective Waves
    volume_score = 0
    try:
        vol_waveA = segment_df.loc[points[0]['date']:points[1]['date']]['volume'].mean()
        vol_waveB = segment_df.loc[points[1]['date']:points[2]['date']]['volume'].mean()
        vol_waveC = segment_df.loc[points[2]['date']:points[3]['date']]['volume'].mean()

        if vol_waveA > vol_waveB and vol_waveB > vol_waveC: # Volume declining during correction
            volume_score += 1
    except Exception:
        volume_score = 0

    total_score = (score / 2.0) * 0.8 + (volume_score / 1.0) * 0.2
    return total_score

def _validate_flat_correction(points: list, is_bullish: bool, segment_df: pd.DataFrame) -> float:
    # The list `points` now contains [context_point, A, B, C]
    if len(points) != 4: return 0
    p_context, pA, pB, pC = [pt['price'] for pt in points]

    # Bullish flat: Corrects a downtrend. A up, B down, C up.
    # Bearish flat: Corrects an uptrend. A down, B up, C down.
    if is_bullish: # Bullish Flat (correcting a downtrend)
        if not (pA > p_context and pB < pA and pC > pB): return 0
    else: # Bearish Flat (correcting an uptrend)
        if not (pA < p_context and pB > pA and pC < pB): return 0

    waveA_len = abs(pA - p_context)
    if waveA_len == 0: return 0

    waveB_retracement = abs(pB - pA) / waveA_len
    waveC_extension = abs(pC - pB) / waveA_len

    score = 0
    if any(abs(waveB_retracement - r) < FIB_TOLERANCE for r in FLAT_WAVE_B_RETRACEMENT): score += 1
    if any(abs(waveC_extension - r) < FIB_TOLERANCE for r in FLAT_WAVE_C_EXTENSION): score += 1

    # Volume analysis for Flats (often declining volume)
    volume_score = 0
    try:
        vol_waveA = segment_df.loc[points[0]['date']:points[1]['date']]['volume'].mean()
        vol_waveB = segment_df.loc[points[1]['date']:points[2]['date']]['volume'].mean()
        vol_waveC = segment_df.loc[points[2]['date']:points[3]['date']]['volume'].mean()
        if vol_waveA > vol_waveB and vol_waveB > vol_waveC: # Volume declining
            volume_score += 1
    except Exception:
        volume_score = 0

    total_score = (score / 2.0) * 0.8 + (volume_score / 1.0) * 0.2
    return total_score

def _validate_triangle_correction(points: list, segment_df: pd.DataFrame) -> float:
    # Triangle correction rules: 5 waves (A-B-C-D-E) that converge
    # Needs 6 points for A-B-C-D-E
    if len(points) != 6: return 0
    pA, pB, pC, pD, pE, pF = [pt['price'] for pt in points] # F is the end of E

    # Check convergence (contracting triangle)
    if not (abs(pC - pB) < abs(pA - pB) and abs(pE - pD) < abs(pC - pD)): return 0

    # Check alternating direction
    if not ((pB > pA and pC < pB and pD > pC and pE < pD and pF > pE) or # Bullish triangle
            (pB < pA and pC > pB and pD < pC and pE > pD and pF < pE)): # Bearish triangle
        return 0

    score = 0
    # Fibonacci relationships (e.g., each wave is 61.8% of the previous)
    # This is a simplification, real triangles have more complex ratios
    wave_AB_len = abs(pB - pA)
    wave_BC_len = abs(pC - pB)
    wave_CD_len = abs(pD - pC)
    wave_DE_len = abs(pE - pD)

    if wave_AB_len > 0 and abs(wave_BC_len / wave_AB_len - 0.618) < FIB_TOLERANCE: score += 1
    if wave_BC_len > 0 and abs(wave_CD_len / wave_BC_len - 0.618) < FIB_TOLERANCE: score += 1
    if wave_CD_len > 0 and abs(wave_DE_len / wave_CD_len - 0.618) < FIB_TOLERANCE: score += 1

    # Volume analysis for Triangles (volume typically contracts)
    volume_score = 0
    try:
        vol_A = segment_df.loc[points[0]['date']:points[1]['date']]['volume'].mean()
        vol_B = segment_df.loc[points[1]['date']:points[2]['date']]['volume'].mean()
        vol_C = segment_df.loc[points[2]['date']:points[3]['date']]['volume'].mean()
        vol_D = segment_df.loc[points[3]['date']:points[4]['date']]['volume'].mean()
        vol_E = segment_df.loc[points[4]['date']:points[5]['date']]['volume'].mean()
        if vol_A > vol_B and vol_B > vol_C and vol_C > vol_D and vol_D > vol_E: # Volume contracting
            volume_score += 1
    except Exception:
        volume_score = 0

    total_score = (score / 3.0) * 0.8 + (volume_score / 1.0) * 0.2
    return total_score

def _validate_expanded_flat(points: list, is_bullish: bool, segment_df: pd.DataFrame) -> float:
    if len(points) != 4: return 0
    p_context, pA, pB, pC = [pt['price'] for pt in points]

    # Expanded Flat rules: B wave goes beyond start of A, C wave goes beyond end of A
    if is_bullish: # Correcting a downtrend
        if not (pA > p_context and pB < p_context and pC > pA): return 0
    else: # Correcting an uptrend
        if not (pA < p_context and pB > p_context and pC < pA): return 0

    waveA_len = abs(pA - p_context)
    if waveA_len == 0: return 0
    
    waveB_extension = abs(pB - p_context) / waveA_len
    waveC_extension = abs(pC - pA) / waveA_len

    score = 0
    if 1.05 < waveB_extension < 1.4: score +=1 # B is typically 105-140% of A
    if 1.0 < waveC_extension < 1.7: score += 1 # C is typically 100-170% of A
    
    return score / 2.0

def _validate_running_flat(points: list, is_bullish: bool, segment_df: pd.DataFrame) -> float:
    if len(points) != 4: return 0
    p_context, pA, pB, pC = [pt['price'] for pt in points]

    # Running Flat rules: B wave goes beyond start of A, C wave does NOT go beyond end of A
    if is_bullish: # Correcting a downtrend
        if not (pA > p_context and pB < p_context and pC < pA and pC > pB): return 0
    else: # Correcting an uptrend
        if not (pA < p_context and pB > p_context and pC > pA and pC < pB): return 0

    waveA_len = abs(pA - p_context)
    if waveA_len == 0: return 0
    
    waveB_extension = abs(pB - p_context) / waveA_len
    
    score = 0
    if waveB_extension > 1.0: score +=1 # B goes beyond A
    # C does not go beyond A, so no ratio check needed, just structural
    score += 1
    
    return score / 2.0

def _validate_barrier_triangle(points: list, segment_df: pd.DataFrame) -> float:
    if len(points) != 6: return 0
    pA, pB, pC, pD, pE, pF = [pt['price'] for pt in points]

    # Barrier triangle: one of the trendlines is horizontal
    # For simplicity, we'll check if B and D are at similar levels
    if abs(pB - pD) / ((pB+pD)/2) > 0.05: # B & D are not within 5% of each other
        return 0.0

    # Use the standard triangle validation for the rest
    return _validate_triangle_correction(points, segment_df)

def _validate_expanding_triangle(points: list, segment_df: pd.DataFrame) -> float:
    if len(points) != 6: return 0
    pA, pB, pC, pD, pE, pF = [pt['price'] for pt in points]

    # Check divergence (expanding triangle)
    if not (abs(pC - pB) > abs(pA - pB) and abs(pE - pD) > abs(pC - pD)): return 0

    # Check alternating direction
    if not ((pB > pA and pC < pB and pD > pC and pE < pD and pF > pE) or # Bullish triangle
            (pB < pA and pC > pB and pD < pC and pE > pD and pF < pE)): # Bearish triangle
        return 0

    score = 0
    wave_AB_len = abs(pB - pA)
    wave_BC_len = abs(pC - pB)
    wave_CD_len = abs(pD - pC)
    wave_DE_len = abs(pE - pD)

    # In expanding triangles, waves are often related by 1.618
    if wave_AB_len > 0 and abs(wave_BC_len / wave_AB_len - 1.618) < FIB_TOLERANCE: score += 1
    if wave_BC_len > 0 and abs(wave_CD_len / wave_BC_len - 1.618) < FIB_TOLERANCE: score += 1
    if wave_CD_len > 0 and abs(wave_DE_len / wave_CD_len - 1.618) < FIB_TOLERANCE: score += 1
    
    return score / 3.0

def _validate_double_zigzag(points: list, is_bullish: bool, segment_df: pd.DataFrame) -> float:
    # A double zigzag (W-X-Y) has 7 sub-waves, requiring 8 points
    if len(points) != 8: return 0
    
    # We can approximate by checking the main W, X, Y points
    p_start, p_W, p_X, p_Y = points[0], points[3], points[4], points[7]

    # Use the WXY validation logic
    return _validate_double_three_correction([p_start, p_W, p_X, p_Y], is_bullish, segment_df)

def _validate_triple_zigzag(points: list, is_bullish: bool, segment_df: pd.DataFrame) -> float:
    # A triple zigzag (W-X-Y-X-Z) has 11 sub-waves, requiring 12 points
    if len(points) != 12: return 0

    # We can approximate by checking the main W, X, Y, XX, Z points
    p_start, p_W, p_X, p_Y, p_XX, p_Z = points[0], points[3], points[4], points[7], points[8], points[11]

    # Use the WXYXZ validation logic
    return _validate_triple_three_correction([p_start, p_W, p_X, p_Y, p_XX, p_Z], is_bullish, segment_df)


def _validate_double_three_correction(points: list, is_bullish: bool, segment_df: pd.DataFrame) -> float:
    """
    Validates a Double Three (WXY) correction.
    This pattern consists of two simple corrections (like Zigzags) joined by an X wave.
    It requires 4 main points: start of W, end of W, end of X, end of Y.
    We will use sub-points to define the structure, so we need 8 points for a W(abc)-X(a)-Y(abc) structure.
    For simplicity, we'll check the major W, X, Y points first, which requires 4 extrema.
    """
    if len(points) < 4: return 0
    p_start, p_W, p_X, p_Y = [pt['price'] for pt in points[:4]]

    # Basic structural check for W-X-Y
    if is_bullish: # Correcting a downtrend, so WXY moves up
        if not (p_W > p_start and p_X < p_W and p_Y > p_X): return 0
    else: # Correcting an uptrend, so WXY moves down
        if not (p_W < p_start and p_X > p_W and p_Y < p_X): return 0

    wave_W_len = abs(p_W - p_start)
    if wave_W_len == 0: return 0

    wave_X_retracement = abs(p_X - p_W) / wave_W_len
    wave_Y_extension = abs(p_Y - p_X) / wave_W_len

    score = 0
    # Validate X wave retracement
    if any(abs(wave_X_retracement - r) < FIB_TOLERANCE for r in X_WAVE_RETRACEMENT_RATIOS):
        score += 1
    
    # Validate Y wave extension (commonly 0.618, 1.0, or 1.618 of W)
    y_ratios = [0.618, 1.0, 1.618]
    if any(abs(wave_Y_extension - r) < FIB_TOLERANCE for r in y_ratios):
        score += 1

    # Volume analysis: Volume should ideally diminish through the pattern
    volume_score = 0
    try:
        vol_W = segment_df.loc[points[0]['date']:points[1]['date']]['volume'].mean()
        vol_X = segment_df.loc[points[1]['date']:points[2]['date']]['volume'].mean()
        vol_Y = segment_df.loc[points[2]['date']:points[3]['date']]['volume'].mean()
        if vol_W > vol_X and vol_X > vol_Y:
            volume_score += 1
    except Exception:
        volume_score = 0

    total_score = (score / 2.0) * 0.8 + (volume_score / 1.0) * 0.2
    return total_score

def _validate_triple_three_correction(points: list, is_bullish: bool, segment_df: pd.DataFrame) -> float:
    """
    Validates a Triple Three (WXYXZ) correction.
    This is a complex pattern with 5 sub-waves (W, X, Y, XX, Z).
    It requires 6 main points to define the structure.
    """
    if len(points) < 6: return 0
    p_start, p_W, p_X, p_Y, p_XX, p_Z = [pt['price'] for pt in points[:6]]

    # Basic structural check for W-X-Y-XX-Z
    if is_bullish: # Correcting a downtrend, so WXYXZ moves up
        if not (p_W > p_start and p_X < p_W and p_Y > p_X and p_XX < p_Y and p_Z > p_XX):
            return 0
    else: # Correcting an uptrend, so WXYXZ moves down
        if not (p_W < p_start and p_X > p_W and p_Y < p_X and p_XX > p_Y and p_Z < p_XX):
            return 0
    
    wave_W_len = abs(p_W - p_start)
    if wave_W_len == 0: return 0

    wave_X_retracement = abs(p_X - p_W) / wave_W_len
    wave_Y_extension = abs(p_Y - p_X) / wave_W_len
    wave_XX_retracement = abs(p_XX - p_Y) / abs(p_Y - p_X) # XX retraces Y
    wave_Z_extension = abs(p_Z - p_XX) / wave_W_len # Z is often related to W

    score = 0
    # Validate X and XX wave retracements
    if any(abs(wave_X_retracement - r) < FIB_TOLERANCE for r in X_WAVE_RETRACEMENT_RATIOS):
        score += 1
    if any(abs(wave_XX_retracement - r) < FIB_TOLERANCE for r in X_WAVE_RETRACEMENT_RATIOS):
        score += 1
    
    # Validate Y and Z wave extensions
    y_ratios = [0.618, 1.0, 1.618]
    if any(abs(wave_Y_extension - r) < FIB_TOLERANCE for r in y_ratios):
        score += 1
    if any(abs(wave_Z_extension - r) < FIB_TOLERANCE for r in Z_WAVE_EXTENSION_RATIOS):
        score += 1

    # Volume analysis: Volume should ideally diminish
    volume_score = 0
    try:
        vol_W = segment_df.loc[points[0]['date']:points[1]['date']]['volume'].mean()
        vol_Y = segment_df.loc[points[2]['date']:points[3]['date']]['volume'].mean()
        vol_Z = segment_df.loc[points[4]['date']:points[5]['date']]['volume'].mean()
        if vol_W > vol_Y > vol_Z:
            volume_score += 1
    except Exception:
        volume_score = 0

    total_score = (score / 4.0) * 0.8 + (volume_score / 1.0) * 0.2
    return total_score

def _validate_diagonal_triangle(points: list, is_bullish: bool, segment_df: pd.DataFrame) -> float:
    """Validates a 5-wave diagonal triangle, where Wave 4 MUST overlap Wave 1."""
    if len(points) != 6:
        return 0

    p = [pt['price'] for pt in points]
    p0, p1, p2, p3, p4, p5 = p

    # General structure check
    if is_bullish:
        if not (p1 > p0 and p2 < p1 and p3 > p2 and p4 < p3 and p5 > p4):
            return 0
    else: # Bearish
        if not (p1 < p0 and p2 > p1 and p3 < p2 and p4 > p3 and p5 < p4):
            return 0

    score = 0
    # Rule 1: Wave 4 MUST overlap with Wave 1
    if is_bullish and p4 < p1:
        score += 1
    elif not is_bullish and p4 > p1:
        score += 1
    else:
        return 0 # This is the defining characteristic, if it fails, it's not a diagonal.

    # Rule 2: Wave 2 cannot retrace more than 100% of Wave 1
    if (is_bullish and p2 <= p0) or (not is_bullish and p2 >= p0):
        return 0
    score += 1

    # Rule 3: Wave 3 is never the shortest
    wave1_len = abs(p1 - p0)
    wave3_len = abs(p3 - p2)
    wave5_len = abs(p5 - p4)
    if wave3_len < wave1_len and wave3_len < wave5_len:
        return 0 # Wave 3 cannot be the shortest
    score += 1

    # Rule 4 (Bonus): Check for converging wedge shape (contracting diagonal)
    if wave3_len < wave1_len and wave5_len < wave3_len:
        score += 1

    return score / 4.0 # Max score is 4

# --- Main Pattern Recognition Function ---
def _split_df_by_gaps(df: pd.DataFrame, max_days_gap: int = 30) -> list[pd.DataFrame]:
    """
    Splits a DataFrame into multiple smaller DataFrames if there are significant time gaps.
    This is crucial for handling data with long periods of no trading (e.g., suspensions).
    It now dynamically adjusts the split threshold based on the data's median gap.

    Args:
        df (pd.DataFrame): Input DataFrame with a DatetimeIndex.
        max_days_gap (int): The absolute maximum number of days allowed in a gap before splitting.

    Returns:
        list[pd.DataFrame]: A list of DataFrame segments.
    """
    logger.info(f"Checking for significant gaps in DataFrame (absolute max: {max_days_gap} days).")

    if not isinstance(df.index, pd.DatetimeIndex) or len(df) < 2:
        logger.warning("DataFrame index is not a DatetimeIndex or too short. Cannot split.")
        return [df]

    gaps = df.index.to_series().diff().dt.days.dropna()

    if gaps.empty:
        logger.info("No gaps found in the DataFrame.")
        return [df]

    median_gap = gaps.median()
    # Define a dynamic threshold: 4x the median gap, but at least the specified max_days_gap.
    # This handles weekly/monthly data where median gap is 7/30 days, but also catches true suspensions.
    # We also cap it at a reasonable maximum to avoid excessively large thresholds on unusual data.
    dynamic_threshold = max(min(median_gap * 4, 90), max_days_gap)

    logger.info(f"Median gap: {median_gap:.2f} days. Dynamic split threshold: {dynamic_threshold:.2f} days.")

    split_indices = gaps[gaps > dynamic_threshold].index

    if split_indices.empty:
        logger.info("No significant gaps found based on dynamic threshold. Returning original DataFrame.")
        return [df]

    logger.info(f"Found {len(split_indices)} gaps to split on. Splitting DataFrame...")
    
    segments = []
    last_split_point = df.index[0]

    for split_point in split_indices:
        loc = df.index.get_loc(split_point)
        segment_end_date = df.index[loc - 1]
        
        segment = df.loc[last_split_point:segment_end_date]
        if len(segment) > 1: # Only add segments with more than one data point
            segments.append(segment)
        
        last_split_point = split_point

    final_segment = df.loc[last_split_point:]
    if len(final_segment) > 1:
        segments.append(final_segment)
        
    logger.info(f"Successfully split DataFrame into {len(segments)} segments.")
    for i, segment in enumerate(segments):
        logger.debug(f"  Segment {i+1}: {len(segment)} rows, from {segment.index.min()} to {segment.index.max()}")

    return segments

def find_elliott_wave_patterns(df: pd.DataFrame, ml_validator: 'MLValidator' = None, _extrema_override: list = None) -> dict:
    """
    Identifies multiple hypotheses for Elliott Wave patterns, optionally using an ML validator.
    """
    # --- FIX START ---
    # Ensure the DataFrame has a DatetimeIndex for time-series operations.
    if not isinstance(df.index, pd.DatetimeIndex):
        if 'date' in df.columns:
            logger.info("DataFrame does not have DatetimeIndex, setting 'date' column as index.")
            df = df.set_index('date', drop=True)
        else:
            logger.error("DataFrame must have a 'date' column or a DatetimeIndex.")
            return {} # Return empty dict if no date information is available
    # --- FIX END ---

    logging.info(f"[find_elliott_wave_patterns] Starting analysis for dataframe of size {len(df)}.")
    
    trading_segments = _split_df_by_gaps(df)
    
    all_hypotheses = {
        "impulsive_bullish": [], "impulsive_bearish": [],
        "impulsive_diagonal_bullish": [], "impulsive_diagonal_bearish": [], # Added for diagonals
        "corrective_bullish": [], "corrective_bearish": [], "corrective_flat_bullish": [], "corrective_flat_bearish": [],
        "corrective_triangle": [], "corrective_wxy_bullish": [], "corrective_wxy_bearish": [],
        "corrective_wxyxz_bullish": [], "corrective_wxyxz_bearish": [],
        "corrective_double_zigzag_bullish": [], "corrective_double_zigzag_bearish": [],
        "corrective_triple_zigzag_bullish": [], "corrective_triple_zigzag_bearish": []
    }

    for i, segment_df_orig in enumerate(trading_segments):
        logging.info(f"--- Analyzing Trading Segment {i+1}/{len(trading_segments)} ---")
        
        segment_df = segment_df_orig.resample('D').ffill()
        segment_df['volume'] = segment_df['volume'].fillna(0)

        close_prices = segment_df['close'].values
        candidate_segments, _, _ = find_nested_wave_structures(close_prices)

        if not candidate_segments:
            logging.warning("No candidate wave structures found. Analyzing full segment as fallback.")
            candidate_segments = [(0, len(close_prices), 0.5)]

        for start, end, hurst in candidate_segments:
            logging.info(f"Analyzing sub-segment from {start} to {end} with Hurst: {hurst:.2f}")
            sub_segment_df = segment_df.iloc[start:end]
            
            extrema = _find_local_extrema(sub_segment_df) if _extrema_override is None else _extrema_override
            logging.info(f"Found {len(extrema)} extrema for this sub-segment.")

            if len(extrema) < 3: continue

            # Generate hypotheses
            # Impulse Waves (5 waves, 6 points)
            for j in range(len(extrema) - 5):
                points = extrema[j : j + 6]
                # Determine if bullish or bearish based on the overall direction of the 5 waves
                is_bullish = points[5]['price'] > points[0]['price']
                
                # Check for standard impulse wave
                confidence = _validate_impulse_wave(points, is_bullish, sub_segment_df)
                if confidence > 0:
                    all_hypotheses["impulsive_bullish" if is_bullish else "impulsive_bearish"].append(
                        create_pattern_dict("Impulse Wave", "impulsive", points, confidence, hurst)
                    )

                # Check for diagonal triangle
                confidence_diag = _validate_diagonal_triangle(points, is_bullish, sub_segment_df)
                if confidence_diag > 0:
                    all_hypotheses["impulsive_diagonal_bullish" if is_bullish else "impulsive_diagonal_bearish"].append(
                        create_pattern_dict("Diagonal Triangle", "impulsive_diagonal", points, confidence_diag, hurst)
                    )

            # Corrective Waves (Zigzag: 3 waves, 4 points)
            # The _validate_corrective_wave expects [context_point, A, B, C]
            # So we need to iterate through 4 extrema points.
            for j in range(len(extrema) - 3): # This ensures we have extrema[j], extrema[j+1], extrema[j+2], extrema[j+3]
                points_for_validation = extrema[j : j + 4] # This will be [context_point, A, B, C]
                
                # Determine if it's a bullish or bearish correction
                # If the first wave (A) is upward, it's a bullish correction (correcting a prior downtrend)
                is_bullish_correction = points_for_validation[1]['price'] > points_for_validation[0]['price']
                
                confidence = _validate_corrective_wave(points_for_validation, is_bullish_correction, sub_segment_df)
                if confidence > 0:
                    all_hypotheses["corrective_bullish" if is_bullish_correction else "corrective_bearish"].append(
                        create_pattern_dict("Zigzag Correction", "corrective_zigzag", points_for_validation, confidence, hurst)
                    )

            # Corrective Waves (Flat: 3 waves, 4 points)
            # The _validate_flat_correction expects [context_point, A, B, C]
            for j in range(len(extrema) - 3):
                points_for_validation = extrema[j : j + 4]
                is_bullish_flat = points_for_validation[1]['price'] > points_for_validation[0]['price'] # A is up for bullish flat
                confidence = _validate_flat_correction(points_for_validation, is_bullish_flat, sub_segment_df)
                if confidence > 0:
                    all_hypotheses["corrective_flat_bullish" if is_bullish_flat else "corrective_flat_bearish"].append(
                        create_pattern_dict("Flat Correction", "corrective_flat", points_for_validation, confidence, hurst)
                    )

            # Corrective Waves (Expanded Flat: 3 waves, 4 points)
            for j in range(len(extrema) - 3):
                points_for_validation = extrema[j : j + 4]
                is_bullish_expanded_flat = points_for_validation[1]['price'] > points_for_validation[0]['price']
                confidence = _validate_expanded_flat(points_for_validation, is_bullish_expanded_flat, sub_segment_df)
                if confidence > 0:
                    all_hypotheses["corrective_flat_bullish" if is_bullish_expanded_flat else "corrective_flat_bearish"].append(
                        create_pattern_dict("Expanded Flat Correction", "corrective_expanded_flat", points_for_validation, confidence, hurst)
                    )

            # Corrective Waves (Running Flat: 3 waves, 4 points)
            for j in range(len(extrema) - 3):
                points_for_validation = extrema[j : j + 4]
                is_bullish_running_flat = points_for_validation[1]['price'] > points_for_validation[0]['price']
                confidence = _validate_running_flat(points_for_validation, is_bullish_running_flat, sub_segment_df)
                if confidence > 0:
                    all_hypotheses["corrective_flat_bullish" if is_bullish_running_flat else "corrective_flat_bearish"].append(
                        create_pattern_dict("Running Flat Correction", "corrective_running_flat", points_for_validation, confidence, hurst)
                    )

            # Corrective Waves (Triangle: 5 waves, 6 points)
            for j in range(len(extrema) - 5):
                points_for_validation = extrema[j : j + 6]
                confidence = _validate_triangle_correction(points_for_validation, sub_segment_df)
                if confidence > 0:
                    all_hypotheses["corrective_triangle"].append(
                        create_pattern_dict("Triangle Correction", "corrective_triangle", points_for_validation, confidence, hurst)
                    )

            # Corrective Waves (Barrier Triangle: 5 waves, 6 points)
            for j in range(len(extrema) - 5):
                points_for_validation = extrema[j : j + 6]
                confidence = _validate_barrier_triangle(points_for_validation, sub_segment_df)
                if confidence > 0:
                    all_hypotheses["corrective_triangle"].append(
                        create_pattern_dict("Barrier Triangle Correction", "corrective_barrier_triangle", points_for_validation, confidence, hurst)
                    )

            # Corrective Waves (Expanding Triangle: 5 waves, 6 points)
            for j in range(len(extrema) - 5):
                points_for_validation = extrema[j : j + 6]
                confidence = _validate_expanding_triangle(points_for_validation, sub_segment_df)
                if confidence > 0:
                    all_hypotheses["corrective_triangle"].append(
                        create_pattern_dict("Expanding Triangle Correction", "corrective_expanding_triangle", points_for_validation, confidence, hurst)
                    )

            # Corrective Waves (Double Three / WXY: 7 sub-waves, 8 points for W(abc)-X(a)-Y(abc))
            # The _validate_double_three_correction expects 4 main points: start of W, end of W, end of X, end of Y.
            # If we are using 8 extrema points for W(abc)-X(a)-Y(abc), then the main points would be:
            # extrema[j], extrema[j+3], extrema[j+4], extrema[j+7]
            for j in range(len(extrema) - 7):
                # Extract the main W, X, Y points for validation
                main_wxy_points = [extrema[j], extrema[j+3], extrema[j+4], extrema[j+7]]
                
                is_bullish_wxy = main_wxy_points[1]['price'] > main_wxy_points[0]['price']
                confidence = _validate_double_three_correction(main_wxy_points, is_bullish_wxy, sub_segment_df)
                if confidence > 0:
                    all_hypotheses["corrective_wxy_bullish" if is_bullish_wxy else "corrective_wxy_bearish"].append(
                        create_pattern_dict("Double Three (WXY) Correction", "corrective_wxy", main_wxy_points, confidence, hurst)
                    )

            # Corrective Waves (Triple Three / WXYXZ: 11 sub-waves, 12 points for W(abc)-X(a)-Y(abc)-XX(a)-Z(abc))
            # The _validate_triple_three_correction expects 6 main points: start of W, end of W, end of X, end of Y, end of XX, end of Z.
            # If we are using 12 extrema points, then the main points would be:
            # extrema[j], extrema[j+3], extrema[j+4], extrema[j+7], extrema[j+8], extrema[j+11]
            for j in range(len(extrema) - 11):
                # Extract the main W, X, Y, XX, Z points for validation
                main_wxyxz_points = [extrema[j], extrema[j+3], extrema[j+4], extrema[j+7], extrema[j+8], extrema[j+11]]
                
                is_bullish_wxyxz = main_wxyxz_points[1]['price'] > main_wxyxz_points[0]['price']
                confidence = _validate_triple_three_correction(main_wxyxz_points, is_bullish_wxyxz, sub_segment_df)
                if confidence > 0:
                    all_hypotheses["corrective_wxyxz_bullish" if is_bullish_wxyxz else "corrective_wxyxz_bearish"].append(
                        create_pattern_dict("Triple Three (WXYXZ) Correction", "corrective_wxyxz", main_wxyxz_points, confidence, hurst)
                    )

    # --- Step 3: Validate all hypotheses with ML model if available ---
    if ml_validator:
        logging.info("Applying ML Validator to all generated hypotheses.")
        for pattern_type, patterns in all_hypotheses.items():
            if not patterns: continue
            validated_patterns = ml_validator.predict_validity(patterns)
            # Blend scores: original confidence * ml_confidence
            for p in validated_patterns:
                p['confidence'] = p.get('confidence', 0) * p.get('ml_confidence', 0)
            all_hypotheses[pattern_type] = validated_patterns

    # --- Step 4: Sort all hypothesis lists by final confidence score ---
    for pattern_type in all_hypotheses:
        all_hypotheses[pattern_type].sort(key=lambda p: p['confidence'], reverse=True)
    
    return {"pattern_hypotheses": all_hypotheses}

def create_pattern_dict(name, type, points, confidence, hurst):
    """Helper function to create a consistent pattern dictionary."""
    return {
        "pattern_name": f"{name} (Hurst: {hurst:.2f})",
        "pattern_type": type,
        "start_date": points[0]['date'].strftime('%Y-%m-%d'),
        "end_date": points[-1]['date'].strftime('%Y-%m-%d'),
        "confidence": confidence,
        "hurst_exponent": hurst,
        "points": points
    }