import pandas as pd
import logging
import numpy as np
from typing import Dict, List, Any
from elliott_wave.core_patterns import find_elliott_wave_patterns

# --- Constants for Subwave Structure ---
# Defines the expected subwave sequences for a given parent wave type.
# The validation will check if the found subwave type *starts with* the expected string.
SUBWAVE_SEQUENCES = {
    "impulsive_bullish": ["impulsive_bullish", "corrective_bearish", "impulsive_bullish", "corrective_bearish", "impulsive_bullish"],
    "impulsive_bearish": ["impulsive_bearish", "corrective_bullish", "impulsive_bearish", "corrective_bullish", "impulsive_bearish"],
    "corrective_zigzag_bullish": ["impulsive_bullish", "corrective_bearish", "impulsive_bullish"],
    "corrective_zigzag_bearish": ["impulsive_bearish", "corrective_bullish", "impulsive_bearish"],
    "corrective_flat_bullish": ["corrective_bullish", "corrective_bearish", "impulsive_bullish"], # A(3), B(3), C(5) -> simplified to corrective, corrective, impulsive
    "corrective_flat_bearish": ["corrective_bearish", "corrective_bullish", "impulsive_bearish"],
    "corrective_triangle": ["corrective", "corrective", "corrective", "corrective", "corrective"], # A,B,C,D,E are all corrective
}

# Confidence adjustment factors
CONFIDENCE_BOOST_FACTOR = 1.5 # Factor for a perfect match
PARTIAL_MATCH_BOOST = 1.1     # Factor for a partial match
CONFIDENCE_PENALTY_FACTOR = 0.6 # Factor for a mismatch

def validate_and_score_by_subwaves(parent_hypotheses: Dict[str, List[Dict[str, Any]]], 
                                   full_df: pd.DataFrame, ml_validator: Any, 
                                   recursion_depth: int = 0, max_recursion_depth: int = 2) -> Dict[str, List[Dict[str, Any]]]:
    """
    Validates and re-scores parent wave hypotheses based on the sequence and structure
    of their subwaves found in a smaller timeframe.
    """
    logging.info(f"(Depth {recursion_depth}) Starting multi-timeframe validation...")
    
    validated_hypotheses = {k: [p.copy() for p in v] for k, v in parent_hypotheses.items()}

    for p_type, p_patterns in validated_hypotheses.items():
        if not p_patterns: continue

        # Find the most specific sequence definition available
        expected_sequence = SUBWAVE_SEQUENCES.get(p_type)
        if not expected_sequence:
            # Fallback to broader category if specific type (e.g., wxy) is not defined
            general_type = '_'.join(p_type.split('_')[:2]) # e.g., 'corrective_bearish'
            expected_sequence = SUBWAVE_SEQUENCES.get(general_type)
            if not expected_sequence:
                continue

        for p_pattern in p_patterns:
            p_start = pd.to_datetime(p_pattern['start_date'])
            p_end = pd.to_datetime(p_pattern['end_date'])
            
            sub_df = full_df.loc[p_start:p_end]
            
            if sub_df.empty or len(sub_df) < 10:
                p_pattern['mta_validation'] = "Not enough data for subwave analysis"
                p_pattern['confidence'] *= CONFIDENCE_PENALTY_FACTOR
                continue

            # Recursively find patterns in the child timeframe
            child_hypotheses_raw = find_elliott_wave_patterns(sub_df, ml_validator=ml_validator)
            child_hypotheses = child_hypotheses_raw.get("pattern_hypotheses", {})

            if recursion_depth < max_recursion_depth:
                child_hypotheses = validate_and_score_by_subwaves(child_hypotheses, sub_df, ml_validator, recursion_depth + 1, max_recursion_depth)

            # --- New Sequence Validation Logic ---
            all_child_patterns = []
            for c_patterns in child_hypotheses.values():
                all_child_patterns.extend(c_patterns)
            
            # Filter for children strictly within the parent's timeframe and sort them
            relevant_children = [p for p in all_child_patterns if pd.to_datetime(p['start_date']) >= p_start and pd.to_datetime(p['end_date']) <= p_end]
            relevant_children.sort(key=lambda p: pd.to_datetime(p['start_date']))

            if not relevant_children or len(relevant_children) < len(expected_sequence):
                p_pattern['confidence'] *= CONFIDENCE_PENALTY_FACTOR
                p_pattern['mta_validation'] = f"Fail (Found {len(relevant_children)}/{len(expected_sequence)} subwaves)"
                continue

            # Check the sequence of the most prominent subwaves
            actual_sequence = [p['pattern_type'] for p in relevant_children[:len(expected_sequence)]]
            
            match_score = 0
            for actual, expected in zip(actual_sequence, expected_sequence):
                if actual.startswith(expected):
                    match_score += 1
            
            match_ratio = match_score / len(expected_sequence)

            if match_ratio == 1.0:
                p_pattern['confidence'] *= CONFIDENCE_BOOST_FACTOR
                p_pattern['mta_validation'] = f"Perfect Match ({match_score}/{len(expected_sequence)})"
            elif match_ratio >= 0.6: # At least 3 out of 5, or 2 out of 3 match
                p_pattern['confidence'] *= PARTIAL_MATCH_BOOST
                p_pattern['mta_validation'] = f"Partial Match ({match_score}/{len(expected_sequence)})"
            else:
                p_pattern['confidence'] *= CONFIDENCE_PENALTY_FACTOR
                p_pattern['mta_validation'] = f"Mismatch ({match_score}/{len(expected_sequence)}) - Expected {expected_sequence}"

    # Sort the lists again based on the new confidence scores
    for p_type in validated_hypotheses:
        validated_hypotheses[p_type].sort(key=lambda p: p['confidence'], reverse=True)

    logging.info(f"(Depth {recursion_depth}) Multi-timeframe validation complete.")
    return validated_hypotheses

if __name__ == '__main__':
    # Dummy data for testing
    # Create a dummy ml_validator for testing purposes
    from elliott_wave.ml_validator import MLValidator
    ml_validator_dummy = MLValidator()
    ml_validator_dummy.model = "dummy_model" # Mock a trained model
    ml_validator_dummy.scaler = "dummy_scaler" # Mock a trained scaler
    ml_validator_dummy.columns = [] # Mock columns

    # Create a dummy DataFrame that spans the dates of the hypotheses
    dates_full = pd.to_datetime(pd.date_range(start='2023-01-01', periods=200))
    prices_full = np.sin(np.linspace(0, 50, 200)) * 10 + 100
    full_df_dummy = pd.DataFrame({
        'open': prices_full, 'high': prices_full, 'low': prices_full, 'close': prices_full, 'volume': 1000
    }, index=dates_full)
    full_df_dummy.index.name = 'date'

    dummy_parent_hypotheses = {
        "impulsive_bullish": [
            {"pattern_name": "Parent Impulse 1", "pattern_type": "impulsive_bullish", "start_date": "2023-01-05", "end_date": "2023-02-20", "confidence": 0.9, "points": [{"date": pd.to_datetime("2023-01-05"), "price": 100}, {"date": pd.to_datetime("2023-01-10"), "price": 110}, {"date": pd.to_datetime("2023-01-15"), "price": 105}, {"date": pd.to_datetime("2023-01-25"), "price": 120}, {"date": pd.to_datetime("2023-02-01"), "price": 115}, {"date": pd.to_datetime("2023-02-20"), "price": 130}]},
        ],
        "corrective_bearish": [
            {"pattern_name": "Parent Correction 1", "pattern_type": "corrective_bearish", "start_date": "2023-02-21", "end_date": "2023-03-10", "confidence": 0.7, "points": [{"date": pd.to_datetime("2023-02-21"), "price": 130}, {"date": pd.to_datetime("2023-02-25"), "price": 122}, {"date": pd.to_datetime("2023-03-01"), "price": 126}, {"date": pd.to_datetime("2023-03-10"), "price": 118}]}
        ]
    }
    validated_results = validate_and_score_by_subwaves(dummy_parent_hypotheses, full_df_dummy, ml_validator_dummy)
    
    print("--- Multi-Timeframe Analysis Results ---")
    for p_type, patterns in validated_results.items():
        if not patterns: continue
        print(f"\n-- {p_type} --")
        for p in patterns:
            print(f"  Pattern: {p['pattern_name']}, Original Conf: {p.get('original_confidence', p['confidence']):.2f} -> New Conf: {p['confidence']:.2f}, Validation: {p.get('mta_validation', 'N/A')}")