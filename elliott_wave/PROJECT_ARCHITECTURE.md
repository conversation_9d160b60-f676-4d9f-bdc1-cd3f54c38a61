# 项目架构文档：艾略特波浪模式识别系统

## 1. 引言

本项目 `ai_elliott/elliott_wave` 旨在利用艾略特波浪理论对金融市场数据（特别是股票数据）进行自动化模式识别。艾略特波浪理论是一种技术分析方法，认为市场价格走势遵循可预测的重复波浪模式，这些模式反映了投资者心理的潮起潮落。

本系统的核心目标是：
1.  **识别潜在波浪结构**：通过先进的信号处理技术（如小波分析）从原始价格数据中提取出具有特定特征的波浪段。
2.  **确认艾略特波浪模式**：在识别出的波浪段内，应用艾略特波浪理论的严格规则、斐波那契比率和成交量分析，识别出经典的推动浪、调整浪和三角形等模式。

## 2. 总体架构

本项目采用模块化设计，主要由两个核心Python模块组成，它们协同工作以完成艾略特波浪的识别任务。

```
+---------------------+       +-----------------------+
| 原始股票历史数据    |       |                       |
| (DataFrame: OHLCV)  |       |                       |
+----------+----------+       |                       |
           |                   |                       |
           v                   |                       |
+-----------------------------+-----------------------+
|                             |                       |
|   wave_structure_analyzer.py (波浪结构分析器)       |
|   - 连续小波变换 (CWT)      |                       |
|   - 赫斯特指数 (Hurst Exponent) |                       |
|                             |                       |
+----------+------------------+                       |
           |                                           |
           | (潜在波浪结构的起止索引, 赫斯特指数)        |
           v                                           |
+-----------------------------------------------------+
|                                                     |
|   pattern_recognition.py (模式识别器)               |
|   - 局部极值识别                                    |
|   - 艾略特波浪规则验证                              |
|   - 斐波那契比率验证                                |
|   - 成交量模式分析                                  |
|                                                     |
+----------+------------------------------------------+
           |
           v
+---------------------+
| 识别到的艾略特波浪模式 |
| (类型, 置信度, 起止日期, 描述) |
+---------------------+
```

## 3. 模块详解

### 3.1 `wave_structure_analyzer.py` (波浪结构分析器)

*   **功能**: 该模块是整个系统的第一步，负责从原始时间序列数据中识别出具有“分形”或“趋势持续性”特征的潜在波浪结构。它不直接识别艾略特波浪模式，而是提供一个“候选区域”列表，供后续的模式识别模块进行精细分析。
*   **核心技术**: 
    *   **连续小波变换 (CWT)**: CWT 是一种强大的信号处理工具，能够分析信号在不同频率（或尺度）上的局部特征。通过对股票收盘价时间序列进行 CWT，可以揭示隐藏在不同时间尺度上的波动模式和能量分布。
    *   **赫斯特指数 (Hurst Exponent)**: 赫斯特指数用于衡量时间序列的长期记忆性或趋势持续性。一个较高的赫斯特指数（通常 H > 0.5）表明时间序列具有趋势持续性，即过去的趋势倾向于在未来持续；而较低的赫斯特指数（H < 0.5）则表明均值回归。在艾略特波浪理论中，波浪结构通常表现出一定的趋势持续性。
*   **输入**: 
    *   `ts`: 股票收盘价的 NumPy 数组（一维时间序列）。
    *   `scales`: CWT 分析的尺度范围。
    *   `wavelet_name`: 使用的小波函数名称（例如 'morl'）。
    *   `hurst_threshold`: 筛选潜在波浪结构的赫斯特指数阈值。
    *   `min_segment_len`: 最小的波浪段长度，用于过滤掉过短的噪音段。
*   **输出**: 
    *   `nested_wave_candidates`: 一个列表，包含识别到的潜在波浪结构的元组 `(start_index, end_index, hurst_exponent)`。
    *   `coeffs`: CWT 系数。
    *   `freqs`: CWT 频率。
*   **运行逻辑**: 
    1.  **执行 CWT**: 对输入的收盘价时间序列 `ts` 执行 CWT，得到不同尺度下的能量系数。
    2.  **识别高能量区域**: 通过设定一个能量阈值（例如，平均能量加上两倍标准差），识别出在某些尺度上能量显著高于平均水平的区域。这些区域被认为是潜在的“波浪”活动区域。
    3.  **隔离候选段**: 将连续的高能量区域合并为独立的“候选波浪段”，并确保每个段的长度超过 `min_segment_len`。
    4.  **计算赫斯特指数**: 对每个隔离出的候选波浪段，计算其赫斯特指数。
    5.  **筛选**: 只有赫斯特指数高于 `hurst_threshold` 的波浪段才会被认为是有效的“嵌套波浪结构候选”，并传递给下一个模块。

### 3.2 `pattern_recognition.py` (模式识别器)

*   **功能**: 该模块接收 `wave_structure_analyzer.py` 提供的潜在波浪结构，并在这些结构内部应用艾略特波浪理论的详细规则，识别出具体的艾略特波浪模式（如推动浪、调整浪、三角形）。
*   **核心技术**: 
    *   **局部极值识别**: 精确识别波浪段内的波峰（Peaks）和波谷（Troughs），这些极值点构成了波浪的转折点。
    *   **艾略特波浪规则验证**: 严格检查波浪的形态、相对长度和相互关系是否符合艾略特波浪理论的基本规则（例如，推动浪的浪2不能低于浪1的起点，浪4不能进入浪1的价格范围，浪3通常不是最短浪等）。
    *   **斐波那契比率验证**: 艾略特波浪理论与斐波那契数列有着紧密的联系。该模块会计算波浪之间的斐波那契回撤和扩展比率，以验证波浪的内部结构是否符合预期的斐波那契关系（例如，浪2通常回撤浪1的38.2%或61.8%，浪3通常是浪1的1.618倍扩展等）。
    *   **成交量模式分析**: 成交量是验证艾略特波浪模式的重要辅助指标。例如，推动浪的浪3通常伴随着成交量的放大，而调整浪的成交量通常会萎缩。该模块会分析波浪段内的成交量模式，以提高模式识别的置信度。
*   **输入**: 
    *   `df`: 包含股票历史数据（开盘价、最高价、最低价、收盘价、成交量）的 Pandas DataFrame。
    *   `drl_feedback`: （可选）来自强化学习的反馈，用于辅助模式识别（当前版本中可能未完全实现或作为未来扩展）。
*   **输出**: 
    *   `detected_patterns`: 一个字典列表，每个字典代表一个识别到的艾略特波浪模式，包含以下信息：
        *   `pattern_name`: 模式名称（例如 "Bullish Impulse Wave", "Corrective Wave (A-B-C)", "Contracting Triangle"）。
        *   `pattern_type`: 模式类型（"impulsive" 或 "corrective"）。
        *   `confidence`: 模式识别的置信度。
        *   `start_date`: 模式的起始日期。
        *   `end_date`: 模式的结束日期。
        *   `description`: 模式的详细描述，包括验证细节。
        *   `hurst_exponent`: 该模式所在波浪段的赫斯特指数。
        *   `wave_degree`: 波浪的级别（当前为占位符，可根据尺度进一步细化）。
*   **运行逻辑**: 
    1.  **遍历候选波浪段**: 对于 `wave_structure_analyzer` 提供的每个候选波浪段，截取相应的 `segment_df`。
    2.  **识别局部极值**: 在 `segment_df` 中识别出所有重要的局部波峰和波谷。
    3.  **尝试模式识别**: 
        *   **五浪推动模式 (`_recognize_5_wave_impulse`)**: 尝试从极值点中识别出五浪推动结构。
            *   **基本规则**: 检查浪2不低于浪1起点，浪4不进入浪1价格范围，浪3不是最短浪等。
            *   **斐波那契**: 检查浪2、浪3、浪4、浪5与前一浪的斐波那契回撤/扩展关系。
            *   **成交量**: 检查浪3成交量是否大于浪1，浪4成交量是否小于浪3。
            *   **扩展与失败**: 识别可能的浪3扩展或浪5失败（截断）。
        *   **三浪调整模式 (`_validate_corrective_wave`)**: 尝试识别三浪调整结构（A-B-C）。
            *   **形态**: 检查是否为锯齿形（Zigzag-like）结构。
            *   **斐波那契**: 检查浪B对浪A的回撤，浪C对浪A的比例。
            *   **成交量**: 检查浪B成交量是否小于浪A，浪C成交量是否大于浪B。
        *   **平台形调整模式 (`_validate_flat_correction`)**: 尝试识别平台形（Flat）结构。
            *   **形态**: 检查浪B是否几乎完全回撤浪A，以及浪C的长度是否与浪A相似。
            *   **成交量**: 成交量在平台形中通常较低。
        *   **三角形模式 (`_validate_triangle_correction`)**: 尝试识别三角形结构（A-B-C-D-E）。
            *   **形态**: 检查波浪的收敛形态（例如，C < A, D > B, E < C）。
            *   **成交量**: 检查成交量是否随着三角形的推进而逐渐萎缩。
        *   **双重三浪模式 (`_validate_double_three_correction`)**: 尝试识别WXY双重三浪结构。
            *   **形态**: 检查是否由两个简单的调整模式（W和Y）由一个X浪连接。
            *   **斐波那契**: 验证X浪相对于W浪的回撤幅度和Y浪相对于W浪的延伸幅度。
    4.  **聚合结果**: 将所有识别到的模式及其详细信息添加到 `all_patterns` 列表中。

## 4. 项目运行逻辑（数据流）

整个系统的运行流程可以概括为以下步骤：

1.  **数据输入**: 系统接收包含历史股票数据（日期、开盘价、最高价、最低价、收盘价、成交量）的 Pandas DataFrame。
2.  **波浪结构预处理 (`wave_structure_analyzer.py`)**: 
    *   从输入的 DataFrame 中提取收盘价时间序列。
    *   将收盘价时间序列输入 `find_nested_wave_structures` 函数。
    *   该函数通过 CWT 和赫斯特指数分析，识别出数据中所有符合“趋势持续性”特征的潜在波浪段，并返回这些段的起止索引和赫斯特指数。
3.  **模式识别 (`pattern_recognition.py`)**: 
    *   将原始的完整 DataFrame 和 `wave_structure_analyzer` 返回的潜在波浪段信息输入 `find_elliott_wave_patterns` 函数。
    *   `find_elliott_wave_patterns` 函数会遍历每一个潜在波浪段。
    *   对于每个波浪段：
        *   它会截取该段的原始 OHLCV 数据。
        *   调用内部函数 `_find_local_extrema` 来识别该段内的所有波峰和波谷。
        *   然后，它会尝试调用 `_recognize_5_wave_impulse`、`_recognize_3_wave_corrective` 和 `_recognize_triangle_pattern` 等函数，利用艾略特波浪规则、斐波那契比率和成交量模式对这些极值点进行组合和验证。
        *   如果成功识别出符合条件的艾略特波浪模式，则将其详细信息（包括模式名称、类型、置信度、起止日期、描述、赫斯特指数等）记录下来。
4.  **结果输出**: 最终，`find_elliott_wave_patterns` 函数返回一个包含所有识别到的艾略特波浪模式的列表。

## 5. 样例分析：中国稀土 (股票代码：000831.SZ)

**注意**: 由于我无法访问实时股票数据或执行外部网络请求来获取中国稀土的实际历史数据，以下分析将是**概念性**的，旨在说明系统如何处理真实数据。

**假设场景**: 我们希望分析中国稀土（000831.SZ）在过去一年（例如，2023年7月1日至2024年6月30日）的价格走势，以识别其中的艾略特波浪模式。

**分析流程**:

1.  **数据准备**:
    *   首先，需要获取中国稀土在指定时间段内的历史日线数据。这通常包括日期、开盘价 (Open)、最高价 (High)、最低价 (Low)、收盘价 (Close) 和成交量 (Volume)。
    *   这些数据将被整理成一个 Pandas DataFrame，其结构与 `pattern_recognition.py` 中 `dummy_df` 的结构类似。

    ```python
    # 概念性数据加载 (实际操作中会从数据库或API获取)
    import pandas as pd
    # 假设 df_china_rare_earth 已经加载了中国稀土的历史数据
    # df_china_rare_earth = pd.read_csv('china_rare_earth_000831.csv')
    # ... 确保 df_china_rare_earth 包含 'close', 'volume' 等列，且索引为日期
    ```

2.  **波浪结构识别 (`wave_structure_analyzer.py` 的应用)**:
    *   将 `df_china_rare_earth` 的 `close` 列（收盘价）作为时间序列输入到 `find_nested_wave_structures` 函数。

    ```python
    from elliott_wave.wave_structure_analyzer import find_nested_wave_structures
    # 假设 df_china_rare_earth 已经准备好
    # close_prices_china_rare_earth = df_china_rare_earth['close'].values
    # candidate_segments, _, _ = find_nested_wave_structures(close_prices_china_rare_earth)

    # 预期输出示例 (概念性):
    # candidate_segments = [
    #     (120, 250, 0.75), # 索引120到250之间存在一个赫斯特指数0.75的波浪结构
    #     (300, 450, 0.68)  # 索引300到450之间存在一个赫斯特指数0.68的波浪结构
    # ]
    ```
    *   系统可能会识别出多个潜在的波浪结构，例如，在某个较长的时间段内（对应于 DataFrame 的某个索引范围），中国稀土的收盘价表现出较强的趋势持续性，赫斯特指数高于设定的阈值（例如 0.6）。

3.  **模式识别 (`pattern_recognition.py` 的应用)**:
    *   将完整的 `df_china_rare_earth` 和 `candidate_segments` 输入到 `find_elliott_wave_patterns` 函数。

    ```python
    from elliott_wave.pattern_recognition import find_elliott_wave_patterns
    # patterns = find_elliott_wave_patterns(df_china_rare_earth)
    ```
    *   系统将对每个 `candidate_segment` 进行深入分析。

    *   **概念性分析过程**:
        *   **识别极值**: 在第一个候选段（例如，对应于2023年9月至2024年1月）内，系统会识别出中国稀土股价的波峰和波谷。
        *   **应用规则**:
            *   **如果识别到看涨推动浪**:
                *   系统会检查：浪2是否回撤了浪1的38.2%到85.4%之间？浪3是否是浪1的1.236到4.236倍扩展？浪4是否回撤了浪3的23.6%到61.8%之间，并且没有进入浪1的价格区间？浪5是否是浪1的0.618到1.0倍，或者浪3的0.618倍？
                *   同时，会检查成交量：浪3的成交量是否显著大于浪1？浪4的成交量是否小于浪3？
                *   如果所有条件都满足，系统会报告一个“看涨推动浪”模式，并给出其起止日期和高置信度。
            *   **如果识别到A-B-C调整浪**:
                *   系统会检查：浪B是否回撤了浪A的38.2%到90%之间？浪C是否是浪A的0.618到2.618倍？
                *   同时，会检查成交量：浪B的成交量是否小于浪A？浪C的成交量是否大于浪B？
                *   如果条件满足，系统会报告一个“调整浪 (A-B-C)”模式。
            *   **如果识别到三角形模式**:
                *   系统会检查波浪的收敛形态（例如，C < A, D > B, E < C）。
                *   同时，会检查成交量是否随着三角形的推进而逐渐萎缩（Vol A > Vol C > Vol E）。

    *   **最终输出示例 (概念性)**:

    ```json
    {
      "detected_patterns": [
        {
          "pattern_name": "Bullish Impulse Wave (3rd Wave Extension)",
          "pattern_type": "impulsive",
          "confidence": 0.98,
          "start_date": "2023-09-15",
          "end_date": "2024-01-20",
          "description": "Detected a Bullish Impulse Wave from 2023-09-15 to 2024-01-20 with Fibonacci and Volume validation. Wave 3 showed significant extension.",
          "hurst_exponent": 0.75,
          "wave_degree": "Primary"
        },
        {
          "pattern_name": "Corrective Wave (A-B-C)",
          "pattern_type": "corrective",
          "confidence": 0.85,
          "start_date": "2024-01-21",
          "end_date": "2024-04-10",
          "description": "Detected a potential 3-wave corrective pattern from 2024-01-21 to 2024-04-10 with Fibonacci and Volume validation.",
          "hurst_exponent": 0.68,
          "wave_degree": "Primary"
        }
      ]
    }
    ```
    *   通过这样的分析，系统能够为中国稀土的历史价格走势提供基于艾略特波浪理论的解释，帮助投资者理解当前的市场结构和可能的未来走向。

## 6. 局限性与未来展望

**当前局限性**:
*   **复杂修正浪**: 目前的模式识别主要集中在简单的推动浪、A-B-C调整浪和三角形。艾略特波浪理论中存在更复杂的修正浪形态（如三重三浪等），这些可能需要更复杂的识别逻辑。
*   **市场噪音**: 金融市场数据中存在大量噪音，`_find_local_extrema` 的参数（`min_prominence`, `min_distance`）需要根据数据特性进行调优，以避免识别过多无效的极值点。
*   **数据质量依赖**: 模式识别的准确性高度依赖于输入数据的质量和完整性。
*   **主观性**: 艾略特波浪理论本身具有一定的主观性，即使是经验丰富的分析师也可能对同一图表有不同的波浪划分。本系统试图通过量化规则减少主观性，但仍可能存在误判。
*   **多时间框架**: 当前分析主要在一个时间框架内进行。艾略特波浪理论强调多时间框架的嵌套关系，未来可以考虑引入多时间框架分析以提高准确性。

**未来展望**:
*   **更复杂的模式识别**: 扩展 `pattern_recognition.py` 以识别更多高级的艾略特波浪形态（如三重三浪 WXYXZ）。
*   **机器学习集成**: 探索使用机器学习模型（如深度学习）来辅助或替代部分模式识别逻辑，特别是对于那些难以用硬编码规则捕捉的复杂模式。
*   **动态参数调整**: 引入自适应机制，根据市场波动性或数据特性动态调整 `_find_local_extrema` 和其他识别函数的参数。
*   **可视化**: 开发可视化工具，将识别到的波浪模式直观地呈现在价格图表上，方便用户验证和理解。
*   **回测与优化**: 结合历史数据进行回测，评估模式识别的有效性，并优化识别参数。
*   **与交易策略结合**: 将识别到的模式作为交易策略的输入信号，构建自动化交易系统。

---