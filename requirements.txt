# Core dependencies
akshare>=1.12.0
pandas>=2.0.0
numpy>=1.24.0

# Data processing
scikit-learn>=1.3.0  # For preprocessing and normalization
ta-lib>=0.4.0  # Efficient technical indicators (C-optimized)

# Parallel processing (built-in but requires backport for Python <3.11)
futures>=3.0.5; python_version < '3.11'

# Progress tracking
tqdm>=4.0.0  # For progress bars

# Visualization (optional for EDA)
matplotlib>=3.7.0
plotly>=5.0.0
streamlit>=1.0.0

# Data storage
pyarrow>=10.0.0 # For parquet support

# Database
neo4j>=5.0.0 # Neo4j Python Driver
python-dotenv>=1.0.0

# AI dependencies
torch>=2.0.0
transformers>=4.30.0
langchain>=0.1.0
langchain-google-genai>=0.1.0 # Added for Gemini models
langchain-community>=0.0.0 # For document loading and text splitting
chromadb>=0.0.0 # For vector store
langgraph>=0.0.0  # Agent orchestration framework
stable-baselines3[extra]>=2.0.0 # DRL framework
gymnasium>=0.29.0 # Reinforcement Learning environments
pywavelets>=1.4.1
nolds>=0.5.3
kaleido==0.2.1
dtw-python>=1.3